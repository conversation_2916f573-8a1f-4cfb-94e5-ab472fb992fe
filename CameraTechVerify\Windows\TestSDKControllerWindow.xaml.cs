using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using CameraTechVerify.Implementations;
using CameraTechVerify.Models;

namespace CameraTechVerify.Windows
{
    /// <summary>
    /// TestSDKControllerWindow.xaml 的交互逻辑
    /// 使用TestSDKCameraController实现类，提供与TestSDK相同的功能界面
    /// </summary>
    public partial class TestSDKControllerWindow : Window
    {
        #region 私有字段

        private TestSDKCameraController cameraController;
        private List<CameraDeviceInfo> deviceInfoList = new List<CameraDeviceInfo>();
        private bool isGrabbing = false;
        private bool isRecord = false;
        private long imageCount = 0;

        #endregion

        #region 构造函数

        public TestSDKControllerWindow()
        {
            InitializeComponent();
            InitializeCameraController();
            RefreshDeviceList();
            SetCtrlWhenClose();
        }

        #endregion

        #region 初始化

        private void InitializeCameraController()
        {
            try
            {
                cameraController = new TestSDKCameraController();
                
                // 订阅事件
                cameraController.ImageReceived += OnImageReceived;
                cameraController.ConnectionStatusChanged += OnConnectionStatusChanged;
                cameraController.ErrorOccurred += OnErrorOccurred;
                
                ShowMessage("相机控制器初始化成功");
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"初始化相机控制器失败: {ex.Message}", -1);
            }
        }

        #endregion

        #region 设备管理事件

        private void bnEnum_Click(object sender, RoutedEventArgs e)
        {
            RefreshDeviceList();
        }

        private void bnOpen_Click(object sender, RoutedEventArgs e)
        {
            if (deviceInfoList.Count == 0 || cbDeviceList.SelectedIndex == -1)
            {
                ShowErrorMsg("No device, please select", 0);
                return;
            }

            try
            {
                bool result = cameraController.Connect(cbDeviceList.SelectedIndex);
                if (result)
                {
                    ShowMessage("设备连接成功");
                    SetCtrlWhenOpen();
                    
                    // 获取参数
                    bnGetParam_Click(null, null);
                }
                else
                {
                    ShowErrorMsg("设备连接失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"连接设备异常: {ex.Message}", -1);
            }
        }

        private void bnClose_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 停止采集
                if (isGrabbing)
                {
                    bnStopGrab_Click(sender, e);
                }

                // 断开连接
                cameraController?.Disconnect();
                
                SetCtrlWhenClose();
                ShowMessage("设备已断开连接");
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"断开设备异常: {ex.Message}", -1);
            }
        }

        #endregion

        #region 图像采集事件

        private void bnStartGrab_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                bool result = cameraController.StartGrabbing();
                if (result)
                {
                    isGrabbing = true;
                    SetCtrlWhenStartGrab();
                    ShowMessage("开始采集");
                }
                else
                {
                    ShowErrorMsg("开始采集失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"开始采集异常: {ex.Message}", -1);
            }
        }

        private async void bnStopGrab_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮防止重复点击
                bnStopGrab.IsEnabled = false;
                ShowMessage("正在停止采集...");

                // 使用Task.Run在后台线程执行停止操作，避免阻塞UI线程
                bool result = await Task.Run(() => cameraController.StopGrabbing());

                if (result)
                {
                    isGrabbing = false;
                    SetCtrlWhenStopGrab();
                    ShowMessage("停止采集");
                }
                else
                {
                    ShowErrorMsg("停止采集失败", -1);
                    // 如果失败，重新启用停止按钮
                    bnStopGrab.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"停止采集异常: {ex.Message}", -1);
                // 异常时也要重新启用按钮
                bnStopGrab.IsEnabled = true;
            }
        }

        private void bnContinuesMode_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected == true)
                {
                    bool result = cameraController.SetTriggerMode(TriggerMode.Continuous);
                    if (result)
                    {
                        ShowMessage("设置连续模式成功");
                        cbSoftTrigger.IsEnabled = false;
                        bnTriggerExec.IsEnabled = false;
                    }
                    else
                    {
                        ShowErrorMsg("设置连续模式失败", -1);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置连续模式异常: {ex.Message}", -1);
            }
        }

        private void bnTriggerMode_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected == true)
                {
                    bool result = cameraController.SetTriggerMode(TriggerMode.Software);
                    if (result)
                    {
                        ShowMessage("设置触发模式成功");
                        cbSoftTrigger.IsEnabled = true;
                        cbSoftTrigger.IsChecked = true;
                    }
                    else
                    {
                        ShowErrorMsg("设置触发模式失败", -1);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置触发模式异常: {ex.Message}", -1);
            }
        }

        private void cbSoftTrigger_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected == true)
                {
                    bool result = cameraController.SetTriggerMode(TriggerMode.Software);
                    if (result)
                    {
                        bnTriggerExec.IsEnabled = true;
                        ShowMessage("软件触发模式已启用");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置软件触发异常: {ex.Message}", -1);
            }
        }

        private void cbSoftTrigger_Unchecked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected == true)
                {
                    bool result = cameraController.SetTriggerMode(TriggerMode.Hardware);
                    if (result)
                    {
                        bnTriggerExec.IsEnabled = false;
                        ShowMessage("硬件触发模式已启用");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置硬件触发异常: {ex.Message}", -1);
            }
        }

        private void bnTriggerExec_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                bool result = cameraController.SoftwareTrigger();
                if (result)
                {
                    ShowMessage("软件触发执行成功");
                }
                else
                {
                    ShowErrorMsg("软件触发执行失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"软件触发异常: {ex.Message}", -1);
            }
        }

        private void bnStartRecord_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string fileName = $"record_{DateTime.Now:yyyyMMdd_HHmmss}.avi";
                bool result = cameraController.StartRecording(fileName);
                if (result)
                {
                    isRecord = true;
                    bnStartRecord.IsEnabled = false;
                    bnStopRecord.IsEnabled = true;
                    ShowMessage($"开始录制: {fileName}");
                }
                else
                {
                    ShowErrorMsg("开始录制失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"开始录制异常: {ex.Message}", -1);
            }
        }

        private void bnStopRecord_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                bool result = cameraController.StopRecording();
                if (result)
                {
                    isRecord = false;
                    bnStartRecord.IsEnabled = true;
                    bnStopRecord.IsEnabled = false;
                    ShowMessage("停止录制");
                }
                else
                {
                    ShowErrorMsg("停止录制失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"停止录制异常: {ex.Message}", -1);
            }
        }

        #endregion

        #region 图像保存事件

        private void bnSaveBmp_Click(object sender, RoutedEventArgs e)
        {
            SaveImage("BMP");
        }

        private void bnSaveJpg_Click(object sender, RoutedEventArgs e)
        {
            SaveImage("JPG");
        }

        private void bnSaveTiff_Click(object sender, RoutedEventArgs e)
        {
            SaveImage("TIFF");
        }

        private void bnSavePng_Click(object sender, RoutedEventArgs e)
        {
            SaveImage("PNG");
        }

        private void SaveImage(string format)
        {
            try
            {
                string fileName = $"Image_{DateTime.Now:yyyyMMdd_HHmmss}_{imageCount:D6}.{format.ToLower()}";
                bool result = cameraController.SaveImage(fileName, format);
                if (result)
                {
                    ShowMessage($"图像保存成功: {fileName}");
                }
                else
                {
                    ShowErrorMsg("图像保存失败", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"保存图像异常: {ex.Message}", -1);
            }
        }

        #endregion

        #region 参数控制事件

        private void bnGetParam_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected != true)
                {
                    ShowErrorMsg("设备未连接", -1);
                    return;
                }

                // 获取曝光时间
                float exposureTime = cameraController.GetExposureTime();
                tbExposure.Text = exposureTime.ToString("F1");

                // 获取增益
                float gain = cameraController.GetGain();
                tbGain.Text = gain.ToString("F1");

                // 获取帧率
                float frameRate = cameraController.GetFrameRate();
                tbFrameRate.Text = frameRate.ToString("F1");

                // 获取像素格式
                string pixelFormat = cameraController.GetPixelFormat();
                for (int i = 0; i < cbPixelFormat.Items.Count; i++)
                {
                    if (cbPixelFormat.Items[i] is ComboBoxItem item &&
                        item.Content.ToString() == pixelFormat)
                    {
                        cbPixelFormat.SelectedIndex = i;
                        break;
                    }
                }

                ShowMessage("参数获取成功");
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"获取参数异常: {ex.Message}", -1);
            }
        }

        private void bnSetParam_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected != true)
                {
                    ShowErrorMsg("设备未连接", -1);
                    return;
                }

                // 验证输入
                if (!float.TryParse(tbExposure.Text, out float exposureTime))
                {
                    ShowErrorMsg("曝光时间格式错误", 0);
                    return;
                }

                if (!float.TryParse(tbGain.Text, out float gain))
                {
                    ShowErrorMsg("增益格式错误", 0);
                    return;
                }

                if (!float.TryParse(tbFrameRate.Text, out float frameRate))
                {
                    ShowErrorMsg("帧率格式错误", 0);
                    return;
                }

                // 设置参数
                bool result1 = cameraController.SetExposureTime(exposureTime);
                bool result2 = cameraController.SetGain(gain);
                bool result3 = cameraController.SetFrameRate(frameRate);

                if (result1 && result2 && result3)
                {
                    ShowMessage("参数设置成功");
                }
                else
                {
                    ShowErrorMsg($"参数设置失败: 曝光={result1}, 增益={result2}, 帧率={result3}", -1);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置参数异常: {ex.Message}", -1);
            }
        }

        private void cbPixelFormat_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cameraController?.IsConnected == true && cbPixelFormat.SelectedItem is ComboBoxItem selectedItem)
                {
                    string pixelFormat = selectedItem.Content.ToString();
                    bool result = cameraController.SetPixelFormat(pixelFormat);
                    if (result)
                    {
                        ShowMessage($"像素格式设置成功: {pixelFormat}");
                    }
                    else
                    {
                        ShowErrorMsg($"像素格式设置失败: {pixelFormat}", -1);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"设置像素格式异常: {ex.Message}", -1);
            }
        }

        #endregion

        #region 辅助方法

        private void RefreshDeviceList()
        {
            try
            {
                cbDeviceList.Items.Clear();
                deviceInfoList = cameraController.EnumerateDevices();

                if (deviceInfoList.Count == 0)
                {
                    ShowErrorMsg("未找到设备", 0);
                    return;
                }

                // 在窗体列表中显示设备名
                for (int i = 0; i < deviceInfoList.Count; i++)
                {
                    CameraDeviceInfo deviceInfo = deviceInfoList[i];
                    string displayName;

                    if (!string.IsNullOrEmpty(deviceInfo.UserDefinedName))
                    {
                        displayName = $"{deviceInfo.DeviceType}: {deviceInfo.UserDefinedName} ({deviceInfo.SerialNumber})";
                    }
                    else
                    {
                        displayName = $"{deviceInfo.DeviceType}: {deviceInfo.ManufacturerName} {deviceInfo.ModelName} ({deviceInfo.SerialNumber})";
                    }

                    cbDeviceList.Items.Add(displayName);
                }

                // 选择第一项
                if (deviceInfoList.Count > 0)
                {
                    cbDeviceList.SelectedIndex = 0;
                }

                ShowMessage($"找到 {deviceInfoList.Count} 个设备");
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"刷新设备列表异常: {ex.Message}", -1);
            }
        }

        private void SetCtrlWhenOpen()
        {
            bnOpen.IsEnabled = false;
            bnClose.IsEnabled = true;
            bnStartGrab.IsEnabled = true;
            bnStopGrab.IsEnabled = false;
            bnContinuesMode.IsEnabled = true;
            bnTriggerMode.IsEnabled = true;
            cbSoftTrigger.IsEnabled = false;
            bnTriggerExec.IsEnabled = false;

            bnSaveBmp.IsEnabled = false;
            bnSaveJpg.IsEnabled = false;
            bnSaveTiff.IsEnabled = false;
            bnSavePng.IsEnabled = false;

            tbExposure.IsEnabled = true;
            tbGain.IsEnabled = true;
            tbFrameRate.IsEnabled = true;
            bnGetParam.IsEnabled = true;
            bnSetParam.IsEnabled = true;
            cbPixelFormat.IsEnabled = true;
            bnStartRecord.IsEnabled = false;
            bnStopRecord.IsEnabled = false;

            tbStatus.Text = "已连接";
            tbStatus.Foreground = System.Windows.Media.Brushes.Green;
        }

        private void SetCtrlWhenClose()
        {
            bnOpen.IsEnabled = true;
            bnClose.IsEnabled = false;
            bnStartGrab.IsEnabled = false;
            bnStopGrab.IsEnabled = false;
            bnContinuesMode.IsEnabled = false;
            bnTriggerMode.IsEnabled = false;
            cbSoftTrigger.IsEnabled = false;
            bnTriggerExec.IsEnabled = false;

            bnSaveBmp.IsEnabled = false;
            bnSaveJpg.IsEnabled = false;
            bnSaveTiff.IsEnabled = false;
            bnSavePng.IsEnabled = false;

            tbExposure.IsEnabled = false;
            tbGain.IsEnabled = false;
            tbFrameRate.IsEnabled = false;
            bnGetParam.IsEnabled = false;
            bnSetParam.IsEnabled = false;
            cbPixelFormat.IsEnabled = false;
            bnStartRecord.IsEnabled = false;
            bnStopRecord.IsEnabled = false;

            tbStatus.Text = "未连接";
            tbStatus.Foreground = System.Windows.Media.Brushes.Red;
        }

        private void SetCtrlWhenStartGrab()
        {
            bnStartGrab.IsEnabled = false;
            bnStopGrab.IsEnabled = true;
            bnContinuesMode.IsEnabled = false;
            bnTriggerMode.IsEnabled = false;

            bnSaveBmp.IsEnabled = true;
            bnSaveJpg.IsEnabled = true;
            bnSaveTiff.IsEnabled = true;
            bnSavePng.IsEnabled = true;
            bnStartRecord.IsEnabled = true;
        }

        private void SetCtrlWhenStopGrab()
        {
            bnStartGrab.IsEnabled = true;
            bnStopGrab.IsEnabled = false;
            bnContinuesMode.IsEnabled = true;
            bnTriggerMode.IsEnabled = true;

            bnSaveBmp.IsEnabled = false;
            bnSaveJpg.IsEnabled = false;
            bnSaveTiff.IsEnabled = false;
            bnSavePng.IsEnabled = false;
            bnStartRecord.IsEnabled = false;
            bnStopRecord.IsEnabled = false;
        }

        private void ShowMessage(string message)
        {
            Console.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss} - {message}");
            // 可以添加状态栏显示或其他UI反馈
        }

        private void ShowErrorMsg(string message, int errorCode)
        {
            string fullMessage = errorCode == 0 ? message : $"[{errorCode}] {message}";
            Console.WriteLine($"[ERROR] {DateTime.Now:HH:mm:ss} - {fullMessage}");
            MessageBox.Show(fullMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        #endregion

        #region 事件处理

        private void OnImageReceived(object sender, ImageReceivedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    imageCount++;
                    DisplayImage(e.Image);
                    ShowMessage($"接收图像 #{e.ImageNumber} - {e.Width}x{e.Height}");
                });
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"处理接收图像异常: {ex.Message}", -1);
            }
        }

        private void OnConnectionStatusChanged(object sender, ConnectionStatusChangedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    ShowMessage($"连接状态: {e.Status} - {e.Message}");

                    switch (e.Status)
                    {
                        case ConnectionStatus.Connected:
                            tbStatus.Text = "已连接";
                            tbStatus.Foreground = System.Windows.Media.Brushes.Green;
                            break;
                        case ConnectionStatus.Disconnected:
                            tbStatus.Text = "未连接";
                            tbStatus.Foreground = System.Windows.Media.Brushes.Red;
                            break;
                        case ConnectionStatus.Connecting:
                            tbStatus.Text = "连接中...";
                            tbStatus.Foreground = System.Windows.Media.Brushes.Orange;
                            break;
                        case ConnectionStatus.Failed:
                            tbStatus.Text = "连接失败";
                            tbStatus.Foreground = System.Windows.Media.Brushes.Red;
                            break;
                    }
                });
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"处理连接状态事件异常: {ex.Message}", -1);
            }
        }

        private void OnErrorOccurred(object sender, ErrorOccurredEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    ShowErrorMsg(e.ErrorMessage, e.ErrorCode);
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理错误事件异常: {ex.Message}");
            }
        }

        private void DisplayImage(System.Drawing.Bitmap bitmap)
        {
            try
            {
                if (bitmap == null) return;

                // 将Bitmap转换为BitmapSource
                using (var memory = new MemoryStream())
                {
                    bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                    memory.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.StreamSource = memory;
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze();

                    Image1.Source = bitmapImage;
                }
            }
            catch (Exception ex)
            {
                ShowErrorMsg($"显示图像异常: {ex.Message}", -1);
            }
        }

        #endregion

        #region 窗体事件

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 停止采集
                if (isGrabbing)
                {
                    bnStopGrab_Click(sender, null);
                }

                // 断开连接
                if (cameraController?.IsConnected == true)
                {
                    bnClose_Click(sender, null);
                }

                // 释放资源
                cameraController?.Dispose();
                cameraController = null;

                ShowMessage("窗体关闭，资源已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"窗体关闭异常: {ex.Message}");
            }
        }

        #endregion
    }
}
