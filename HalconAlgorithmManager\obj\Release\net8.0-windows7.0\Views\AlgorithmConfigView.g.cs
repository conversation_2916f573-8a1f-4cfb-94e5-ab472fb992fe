﻿#pragma checksum "..\..\..\..\Views\AlgorithmConfigView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "32103E3BAB4ED9388C191EB1408D426B43097ADE"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HalconAlgorithmManager.Views {
    
    
    /// <summary>
    /// AlgorithmConfigView
    /// </summary>
    public partial class AlgorithmConfigView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AlgorithmTitleText;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AlgorithmSubtitleText;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageInfoText;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RectROITool;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CircleROITool;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton PolygonROITool;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton LineTool;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CircleTool;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AngleTool;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ImageCanvas;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image MainImage;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas AnnotationCanvas;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultImagePanel;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ImageInfoOverlay;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageSizeText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MousePosText;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PixelValueText;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AnnotationInfoOverlay;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ROICountText;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MeasureCountText;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentToolText;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EngineComboBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlgorithmTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider GaussianKernelSlider;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider EdgeEnhanceSlider;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ContrastSlider;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EdgeDetectionComboBox;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LowThresholdTextBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HighThresholdTextBox;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EdgeLinkingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PixelScaleTextBox;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MeasurePrecisionComboBox;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StandardValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UpperToleranceTextBox;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LowerToleranceTextBox;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarningThresholdTextBox;
        
        #line default
        #line hidden
        
        
        #line 355 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ToleranceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToleranceRangeText;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarningRangeText;
        
        #line default
        #line hidden
        
        
        #line 387 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ROIListBox;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox MeasureListBox;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider EdgeSensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EdgeSensitivityValueText;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider NoiseFilterSlider;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoiseFilterValueText;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider AccuracyLevelSlider;
        
        #line default
        #line hidden
        
        
        #line 476 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccuracyLevelValueText;
        
        #line default
        #line hidden
        
        
        #line 483 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DetectionModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 520 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedTimeText;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryUsageText;
        
        #line default
        #line hidden
        
        
        #line 528 "..\..\..\..\Views\AlgorithmConfigView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReliabilityText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VisionAlgorithmManager;component/views/algorithmconfigview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AlgorithmTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AlgorithmSubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            
            #line 30 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveConfig_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 33 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetConfig_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 36 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ImageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 72 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadImage_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.RectROITool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 79 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.RectROITool.Click += new System.Windows.RoutedEventHandler(this.ROITool_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CircleROITool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 82 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.CircleROITool.Click += new System.Windows.RoutedEventHandler(this.ROITool_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PolygonROITool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 85 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.PolygonROITool.Click += new System.Windows.RoutedEventHandler(this.ROITool_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.LineTool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 93 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.LineTool.Click += new System.Windows.RoutedEventHandler(this.MeasureTool_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CircleTool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 96 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.CircleTool.Click += new System.Windows.RoutedEventHandler(this.MeasureTool_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.AngleTool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 99 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.AngleTool.Click += new System.Windows.RoutedEventHandler(this.MeasureTool_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 106 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ZoomIn_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 109 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ZoomOut_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 112 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetView_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 115 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearAnnotations_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ImageCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 123 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.ImageCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ImageCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 124 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.ImageCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.ImageCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 125 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.ImageCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ImageCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 126 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            this.ImageCanvas.MouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ImageCanvas_MouseWheel);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MainImage = ((System.Windows.Controls.Image)(target));
            return;
            case 20:
            this.AnnotationCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 21:
            this.DefaultImagePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.ImageInfoOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 23:
            this.ImageSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.MousePosText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.PixelValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.AnnotationInfoOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 27:
            this.ROICountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.MeasureCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.CurrentToolText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.AlgorithmIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.AlgorithmNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.EngineComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 33:
            this.AlgorithmTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.GaussianKernelSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 35:
            this.EdgeEnhanceSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 36:
            this.ContrastSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 37:
            this.EdgeDetectionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 38:
            this.LowThresholdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 39:
            this.HighThresholdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 40:
            this.EdgeLinkingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.PixelScaleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 42:
            this.MeasurePrecisionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 43:
            this.StandardValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 44:
            this.UpperToleranceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 45:
            this.LowerToleranceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.WarningThresholdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 47:
            this.ToleranceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 48:
            this.ToleranceRangeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.WarningRangeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.ROIListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 51:
            
            #line 411 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddROI_Click);
            
            #line default
            #line hidden
            return;
            case 52:
            this.MeasureListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 53:
            
            #line 442 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddMeasure_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            this.EdgeSensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 55:
            this.EdgeSensitivityValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.NoiseFilterSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 57:
            this.NoiseFilterValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.AccuracyLevelSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 59:
            this.AccuracyLevelValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 60:
            this.DetectionModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 61:
            this.TimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 62:
            this.EstimatedTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 63:
            this.MemoryUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 64:
            this.ReliabilityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 65:
            
            #line 539 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyParameters_Click);
            
            #line default
            #line hidden
            return;
            case 66:
            
            #line 542 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewEffect_Click);
            
            #line default
            #line hidden
            return;
            case 67:
            
            #line 545 "..\..\..\..\Views\AlgorithmConfigView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportParameters_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

