﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;ConnectBtn.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="TxtSlot.Location" type="System.Drawing.Point, System.Drawing">
    <value>378, 81</value>
  </data>
  <data name="&gt;&gt;TxtRack.Name" xml:space="preserve">
    <value>TxtRack</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 18</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>上位机读PLC DB</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;tB_RecDB.Name" xml:space="preserve">
    <value>tB_RecDB</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;TxtIP.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label10.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="TxtIP.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tB_SendDB.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="TxtSlot.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 44</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>DB</value>
  </data>
  <data name="label11.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="tB_Status.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 28</value>
  </data>
  <data name="tB_Status.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="tB_SendDB.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 171</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="DisconnectBtn.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 34</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="tB_SendDB.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 28</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;tB_Status.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label7.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="DisconnectBtn.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="label7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;DisconnectBtn.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="TxtSlot.Text" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>通讯设置</value>
  </data>
  <data name="TextError.Size" type="System.Drawing.Size, System.Drawing">
    <value>491, 28</value>
  </data>
  <data name="tB_RecDB.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 28</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>上位机写PLC DB</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;TxtIP.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>9, 18</value>
  </data>
  <data name="ConnectBtn.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 34</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;TextError.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;TxtSlot.Name" xml:space="preserve">
    <value>TxtSlot</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="TxtIP.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 28</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;TxtRack.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="tB_Status.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="ConnectBtn.Text" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="&gt;&gt;TxtIP.Name" xml:space="preserve">
    <value>TxtIP</value>
  </data>
  <data name="TxtIP.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 18</value>
  </data>
  <data name="tB_RecDB.Text" xml:space="preserve">
    <value>201</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 44</value>
  </data>
  <data name="&gt;&gt;TextError.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="TextError.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;ConnectBtn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="DisconnectBtn.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>S7communication</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="ConnectBtn.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;tB_Status.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tB_RecDB.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;TxtSlot.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;TxtSlot.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 18</value>
  </data>
  <data name="TxtRack.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 28</value>
  </data>
  <data name="DisconnectBtn.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 274</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;tB_SendDB.Name" xml:space="preserve">
    <value>tB_SendDB</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="TxtIP.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 81</value>
  </data>
  <data name="&gt;&gt;TxtIP.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="ConnectBtn.Location" type="System.Drawing.Point, System.Drawing">
    <value>86, 274</value>
  </data>
  <data name="TextError.Location" type="System.Drawing.Point, System.Drawing">
    <value>-3, 475</value>
  </data>
  <data name="&gt;&gt;TxtRack.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 171</value>
  </data>
  <data name="&gt;&gt;tB_RecDB.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="tB_SendDB.Text" xml:space="preserve">
    <value>200</value>
  </data>
  <data name="tB_SendDB.Location" type="System.Drawing.Point, System.Drawing">
    <value>86, 216</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>DB</value>
  </data>
  <data name="&gt;&gt;DisconnectBtn.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="DisconnectBtn.Text" xml:space="preserve">
    <value>断开</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;TxtSlot.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tB_Status.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label10.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tB_Status.Location" type="System.Drawing.Point, System.Drawing">
    <value>515, 476</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label6.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="&gt;&gt;tB_RecDB.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="TxtIP.Text" xml:space="preserve">
    <value>************</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="tB_SendDB.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="TxtRack.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 81</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>PLC IP地址</value>
  </data>
  <data name="groupBox2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="groupBox2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>272, 220</value>
  </data>
  <data name="ConnectBtn.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="&gt;&gt;TextError.Name" xml:space="preserve">
    <value>TextError</value>
  </data>
  <data name="&gt;&gt;DisconnectBtn.Name" xml:space="preserve">
    <value>DisconnectBtn</value>
  </data>
  <data name="TxtRack.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>-3, -1</value>
  </data>
  <data name="TxtSlot.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="&gt;&gt;ConnectBtn.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;TextError.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;DisconnectBtn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="TextError.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 18</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tB_Status.Name" xml:space="preserve">
    <value>tB_Status</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;TxtRack.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="tB_RecDB.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="&gt;&gt;ConnectBtn.Name" xml:space="preserve">
    <value>ConnectBtn</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 44</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>782, 539</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="TxtRack.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="TxtSlot.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 28</value>
  </data>
  <data name="TxtRack.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Rack</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;tB_SendDB.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 18</value>
  </data>
  <data name="&gt;&gt;tB_SendDB.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="tB_RecDB.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 216</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 18</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>780, 374</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 18</value>
  </data>
  <data name="label2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 220</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Slot</value>
  </data>
  <data name="&gt;&gt;tB_RecDB.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>zh</value>
  </metadata>
</root>