using System;
using System.Collections.Generic;
using System.Drawing;

namespace CameraTechVerify.Models
{
    #region 基础数据类型

    /// <summary>
    /// 视觉点
    /// </summary>
    public struct VisionPoint
    {
        public double X { get; set; }
        public double Y { get; set; }
        
        public VisionPoint(double x, double y)
        {
            X = x;
            Y = y;
        }
    }

    /// <summary>
    /// 3D姿态
    /// </summary>
    public struct Pose3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }
        public double Rx { get; set; }
        public double Ry { get; set; }
        public double Rz { get; set; }
        
        public Pose3D(double x, double y, double z, double rx, double ry, double rz)
        {
            X = x; Y = y; Z = z;
            Rx = rx; Ry = ry; Rz = rz;
        }
    }

    /// <summary>
    /// 点云数据
    /// </summary>
    public class VisionPointCloud
    {
        public List<VisionPoint> Points2D { get; set; } = new List<VisionPoint>();
        public List<Pose3D> Points3D { get; set; } = new List<Pose3D>();
        public int Width { get; set; }
        public int Height { get; set; }
    }

    #endregion

    #region 枚举定义

    /// <summary>
    /// 像素格式
    /// </summary>
    public enum VisionPixelFormat
    {
        Mono8,
        Mono16,
        RGB24,
        BGR24,
        RGBA32,
        BGRA32,
        YUV422
    }

    /// <summary>
    /// 图像格式
    /// </summary>
    public enum VisionImageFormat
    {
        BMP,
        JPEG,
        PNG,
        TIFF,
        RAW
    }

    /// <summary>
    /// 边缘检测方法
    /// </summary>
    public enum EdgeDetectionMethod
    {
        Canny,
        Sobel,
        Laplacian,
        Roberts,
        Prewitt
    }

    /// <summary>
    /// 形态学操作
    /// </summary>
    public enum MorphologyOperation
    {
        Erosion,        // 腐蚀
        Dilation,       // 膨胀
        Opening,        // 开运算
        Closing,        // 闭运算
        TopHat,         // 顶帽
        BottomHat       // 底帽
    }

    /// <summary>
    /// 结构元素
    /// </summary>
    public enum StructuringElement
    {
        Rectangle,
        Circle,
        Cross,
        Diamond
    }

    /// <summary>
    /// 滤波类型
    /// </summary>
    public enum FilterType
    {
        Gaussian,       // 高斯滤波
        Mean,           // 均值滤波
        Median,         // 中值滤波
        Bilateral,      // 双边滤波
        Laplacian       // 拉普拉斯滤波
    }

    /// <summary>
    /// 测量类型
    /// </summary>
    public enum MeasureType
    {
        Circle,         // 圆形测量
        Line,           // 直线测量
        Rectangle,      // 矩形测量
        Distance,       // 距离测量
        Angle,          // 角度测量
        Caliper         // 卡尺测量
    }

    /// <summary>
    /// 拟合方法
    /// </summary>
    public enum FittingMethod
    {
        LeastSquares,   // 最小二乘法
        Algebraic,      // 代数法
        Geometric,      // 几何法
        Robust          // 鲁棒法
    }

    #endregion

    #region 接口定义

    /// <summary>
    /// 视觉图像接口
    /// </summary>
    public interface IVisionImage : IDisposable
    {
        int Width { get; }
        int Height { get; }
        VisionPixelFormat PixelFormat { get; }
        IntPtr ImageData { get; }
        Bitmap ToBitmap();
        IVisionImage Clone();
    }

    /// <summary>
    /// 视觉区域接口
    /// </summary>
    public interface IVisionRegion : IDisposable
    {
        double Area { get; }
        VisionPoint Centroid { get; }
        Rectangle BoundingBox { get; }
        IVisionRegion Clone();
    }

    /// <summary>
    /// 视觉轮廓接口
    /// </summary>
    public interface IVisionContour : IDisposable
    {
        List<VisionPoint> Points { get; }
        double Length { get; }
        bool IsClosed { get; }
        IVisionContour Clone();
    }

    #endregion

    #region 结果类型

    /// <summary>
    /// 匹配结果
    /// </summary>
    public class MatchResult
    {
        public double Row { get; set; }
        public double Column { get; set; }
        public double Angle { get; set; }
        public double Score { get; set; }
        public string ModelId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 测量结果
    /// </summary>
    public class MeasureResult
    {
        public MeasureType Type { get; set; }
        public Dictionary<string, double> Values { get; set; } = new Dictionary<string, double>();
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 圆形结果
    /// </summary>
    public class CircleResult
    {
        public double CenterX { get; set; }
        public double CenterY { get; set; }
        public double Radius { get; set; }
        public double Score { get; set; }
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 直线结果
    /// </summary>
    public class LineResult
    {
        public double StartX { get; set; }
        public double StartY { get; set; }
        public double EndX { get; set; }
        public double EndY { get; set; }
        public double Angle { get; set; }
        public double Score { get; set; }
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 3D可视化结果
    /// </summary>
    public class Visualization3DResult
    {
        public string ModelId { get; set; }
        public Pose3D ViewPose { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion

    #region 事件参数

    /// <summary>
    /// 算法进度事件参数
    /// </summary>
    public class AlgorithmProgressEventArgs : EventArgs
    {
        public string AlgorithmName { get; set; }
        public int ProgressPercentage { get; set; }
        public string StatusMessage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 算法完成事件参数
    /// </summary>
    public class AlgorithmCompletedEventArgs : EventArgs
    {
        public string AlgorithmName { get; set; }
        public bool IsSuccess { get; set; }
        public object Result { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 算法错误事件参数
    /// </summary>
    public class AlgorithmErrorEventArgs : EventArgs
    {
        public string AlgorithmName { get; set; }
        public Exception Exception { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    #endregion
}
