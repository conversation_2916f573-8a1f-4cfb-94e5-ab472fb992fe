using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using CameraTechVerify.Models;
using HalconDotNet;

namespace CameraTechVerify.Implementations
{
    /// <summary>
    /// Halcon 图像实现
    /// </summary>
    public class HalconVisionImage : IVisionImage
    {
        private HObject _hObject;
        private bool _disposed = false;

        public HalconVisionImage(HObject hObject)
        {
            _hObject = hObject;
            
            // 获取图像信息
            HTuple width, height, type;
            HOperatorSet.GetImageSize(_hObject, out width, out height);
            HOperatorSet.GetImageType(_hObject, out type);
            
            Width = width.I;
            Height = height.I;
            
            // 根据 Halcon 类型确定像素格式
            switch (type.S)
            {
                case "byte":
                    PixelFormat = VisionPixelFormat.Mono8;
                    break;
                case "uint2":
                    PixelFormat = VisionPixelFormat.Mono16;
                    break;
                case "rgb":
                    PixelFormat = VisionPixelFormat.RGB24;
                    break;
                default:
                    PixelFormat = VisionPixelFormat.Mono8;
                    break;
            }
        }

        public int Width { get; private set; }
        public int Height { get; private set; }
        public VisionPixelFormat PixelFormat { get; private set; }
        
        public IntPtr ImageData
        {
            get
            {
                HTuple pointer;
                HOperatorSet.GetImagePointer1(_hObject, out pointer, out HTuple type, out HTuple width, out HTuple height);
                return new IntPtr(pointer.L);
            }
        }

        public HObject HObject => _hObject;

        public Bitmap ToBitmap()
        {
            try
            {
                // 将 Halcon 图像转换为 Bitmap
                HTuple channels;
                HOperatorSet.CountChannels(_hObject, out channels);
                
                if (channels.I == 1)
                {
                    // 灰度图像
                    HTuple pointer, type, width, height;
                    HOperatorSet.GetImagePointer1(_hObject, out pointer, out type, out width, out height);
                    
                    Bitmap bitmap = new Bitmap(width.I, height.I, System.Drawing.Imaging.PixelFormat.Format8bppIndexed);
                    
                    // 设置灰度调色板
                    var palette = bitmap.Palette;
                    for (int i = 0; i < 256; i++)
                    {
                        palette.Entries[i] = Color.FromArgb(i, i, i);
                    }
                    bitmap.Palette = palette;
                    
                    // 复制图像数据
                    Rectangle rect = new Rectangle(0, 0, width.I, height.I);
                    System.Drawing.Imaging.BitmapData bmpData = bitmap.LockBits(rect, 
                        System.Drawing.Imaging.ImageLockMode.WriteOnly, bitmap.PixelFormat);
                    
                    unsafe
                    {
                        byte* src = (byte*)pointer.IP;
                        byte* dst = (byte*)bmpData.Scan0;
                        
                        for (int y = 0; y < height.I; y++)
                        {
                            for (int x = 0; x < width.I; x++)
                            {
                                dst[y * bmpData.Stride + x] = src[y * width.I + x];
                            }
                        }
                    }
                    
                    bitmap.UnlockBits(bmpData);
                    return bitmap;
                }
                else if (channels.I == 3)
                {
                    // RGB 彩色图像
                    HTuple pointerR, pointerG, pointerB, type, width, height;
                    HOperatorSet.GetImagePointer3(_hObject, out pointerR, out pointerG, out pointerB, 
                        out type, out width, out height);
                    
                    Bitmap bitmap = new Bitmap(width.I, height.I, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
                    
                    Rectangle rect = new Rectangle(0, 0, width.I, height.I);
                    System.Drawing.Imaging.BitmapData bmpData = bitmap.LockBits(rect, 
                        System.Drawing.Imaging.ImageLockMode.WriteOnly, bitmap.PixelFormat);
                    
                    unsafe
                    {
                        byte* srcR = (byte*)pointerR.IP;
                        byte* srcG = (byte*)pointerG.IP;
                        byte* srcB = (byte*)pointerB.IP;
                        byte* dst = (byte*)bmpData.Scan0;
                        
                        for (int y = 0; y < height.I; y++)
                        {
                            for (int x = 0; x < width.I; x++)
                            {
                                int srcIndex = y * width.I + x;
                                int dstIndex = y * bmpData.Stride + x * 3;
                                
                                dst[dstIndex] = srcB[srcIndex];     // B
                                dst[dstIndex + 1] = srcG[srcIndex]; // G
                                dst[dstIndex + 2] = srcR[srcIndex]; // R
                            }
                        }
                    }
                    
                    bitmap.UnlockBits(bmpData);
                    return bitmap;
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }

        public IVisionImage Clone()
        {
            HObject clonedImage;
            HOperatorSet.CopyImage(_hObject, out clonedImage);
            return new HalconVisionImage(clonedImage);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing && _hObject != null)
                {
                    _hObject.Dispose();
                    _hObject = null;
                }
                _disposed = true;
            }
        }

        ~HalconVisionImage()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// Halcon 区域实现
    /// </summary>
    public class HalconVisionRegion : IVisionRegion
    {
        private HObject _hObject;
        private bool _disposed = false;

        public HalconVisionRegion(HObject hObject)
        {
            _hObject = hObject;
            
            // 计算区域属性
            HTuple area, row, column;
            HOperatorSet.AreaCenter(_hObject, out area, out row, out column);
            Area = area.D;
            Centroid = new VisionPoint(column.D, row.D);
            
            // 计算边界框
            HTuple row1, col1, row2, col2;
            HOperatorSet.SmallestRectangle1(_hObject, out row1, out col1, out row2, out col2);
            BoundingBox = new Rectangle((int)col1.D, (int)row1.D, 
                (int)(col2.D - col1.D), (int)(row2.D - row1.D));
        }

        public double Area { get; private set; }
        public VisionPoint Centroid { get; private set; }
        public Rectangle BoundingBox { get; private set; }
        public HObject HObject => _hObject;

        public IVisionRegion Clone()
        {
            HObject clonedRegion;
            HOperatorSet.CopyObj(_hObject, out clonedRegion, 1, 1);
            return new HalconVisionRegion(clonedRegion);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing && _hObject != null)
                {
                    _hObject.Dispose();
                    _hObject = null;
                }
                _disposed = true;
            }
        }

        ~HalconVisionRegion()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// Halcon 轮廓实现
    /// </summary>
    public class HalconVisionContour : IVisionContour
    {
        private HObject _hObject;
        private bool _disposed = false;
        private List<VisionPoint> _points;

        public HalconVisionContour(HObject hObject)
        {
            _hObject = hObject;
            
            // 获取轮廓点
            HTuple row, col;
            HOperatorSet.GetContourXld(_hObject, out row, out col);
            
            _points = new List<VisionPoint>();
            for (int i = 0; i < row.Length; i++)
            {
                _points.Add(new VisionPoint(col[i].D, row[i].D));
            }
            
            // 计算长度
            HTuple length;
            HOperatorSet.LengthXld(_hObject, out length);
            Length = length.D;
            
            // 判断是否闭合
            HTuple attrib;
            HOperatorSet.GetContourGlobalAttribXld(_hObject, "cont_approx", out attrib);
            IsClosed = attrib.S == "polygon";
        }

        public List<VisionPoint> Points => _points;
        public double Length { get; private set; }
        public bool IsClosed { get; private set; }
        public HObject HObject => _hObject;

        public IVisionContour Clone()
        {
            HObject clonedContour;
            HOperatorSet.CopyObj(_hObject, out clonedContour, 1, 1);
            return new HalconVisionContour(clonedContour);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing && _hObject != null)
                {
                    _hObject.Dispose();
                    _hObject = null;
                }
                _disposed = true;
            }
        }

        ~HalconVisionContour()
        {
            Dispose(false);
        }
    }
}
