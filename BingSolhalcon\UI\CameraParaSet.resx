﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="rdbSWTrigger1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;tkbExposure2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdbHWTrigger3.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 54</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="groupBox7.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;btnExecute2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnExecute2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;groupBox7.Name" xml:space="preserve">
    <value>groupBox7</value>
  </data>
  <data name="rdbHWTrigger2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;tkbGain1.Name" xml:space="preserve">
    <value>tkbGain1</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger1.Name" xml:space="preserve">
    <value>rdbSWTrigger1</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="rdbFreerun3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="btnExecute4.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;rdbFreerun3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="Camera1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 30</value>
  </data>
  <data name="&gt;&gt;Camera3.Name" xml:space="preserve">
    <value>Camera3</value>
  </data>
  <data name="rdbFreerun3.Text" xml:space="preserve">
    <value>连续模式</value>
  </data>
  <data name="&gt;&gt;txtBExposure2.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="&gt;&gt;tkbGain1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox12.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="rdbtrigger1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdbFreerun2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="txtBGain1.Location" type="System.Drawing.Point, System.Drawing">
    <value>443, 468</value>
  </data>
  <data name="rdbSWTrigger3.Text" xml:space="preserve">
    <value>软触发模式</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger3.Parent" xml:space="preserve">
    <value>groupBox8</value>
  </data>
  <data name="btnExecute3.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 55</value>
  </data>
  <data name="&gt;&gt;Camera4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger3.Name" xml:space="preserve">
    <value>rdbSWTrigger3</value>
  </data>
  <data name="&gt;&gt;groupBox6.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbFreerun2.Name" xml:space="preserve">
    <value>rdbFreerun2</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;groupBox12.Name" xml:space="preserve">
    <value>groupBox12</value>
  </data>
  <data name="rdbFreerun1.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 48</value>
  </data>
  <data name="tkbExposure2.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="&gt;&gt;rdbFreerun1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="rdbFreerun2.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 52</value>
  </data>
  <data name="&gt;&gt;groupBox5.Name" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>799, 138</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 357</value>
  </data>
  <data name="rdbSWTrigger1.Location" type="System.Drawing.Point, System.Drawing">
    <value>453, 54</value>
  </data>
  <data name="&gt;&gt;tkbGain2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tkbGain2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbtrigger2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="rdbFreerun1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;Camera2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="txtBGain3.Location" type="System.Drawing.Point, System.Drawing">
    <value>441, 457</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>799, 145</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="rdbtrigger1.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="txtBExposure3.Location" type="System.Drawing.Point, System.Drawing">
    <value>441, 380</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 26</value>
  </data>
  <data name="rdbSWTrigger1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>CameraParaSet</value>
  </data>
  <data name="tkbGain2.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 455</value>
  </data>
  <data name="rdbFreerun2.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnExecute2.Name" xml:space="preserve">
    <value>btnExecute2</value>
  </data>
  <data name="&gt;&gt;rdbtrigger1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="groupBox2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="Camera4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="rdbFreerun2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="Camera3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="rdbtrigger2.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 52</value>
  </data>
  <data name="tkbExposure2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnExecute1.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>1067, 615</value>
  </data>
  <data name="groupBox1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="groupBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>780, 128</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdbtrigger3.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 48</value>
  </data>
  <data name="rdbHWTrigger1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="rdbSWTrigger3.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <data name="&gt;&gt;btnExecute3.Name" xml:space="preserve">
    <value>btnExecute3</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tkbGain2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtBGain1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtBExposure2.Location" type="System.Drawing.Point, System.Drawing">
    <value>443, 354</value>
  </data>
  <data name="&gt;&gt;groupBox7.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>780, 134</value>
  </data>
  <data name="rdbtrigger3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdbHWTrigger2.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 54</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 171</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>210, 62</value>
  </data>
  <data name="&gt;&gt;btnExecute4.Name" xml:space="preserve">
    <value>btnExecute4</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;txtBExposure3.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>10, 20</value>
  </data>
  <data name="label3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="&gt;&gt;groupBox5.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="rdbtrigger2.Text" xml:space="preserve">
    <value>触发模式</value>
  </data>
  <data name="txtBExposure2.Text" xml:space="preserve">
    <value>3500</value>
  </data>
  <data name="&gt;&gt;btnExecute4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tkbExposure2.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 71</value>
  </data>
  <data name="groupBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 11</value>
  </data>
  <data name="&gt;&gt;Camera1.Name" xml:space="preserve">
    <value>Camera1</value>
  </data>
  <data name="rdbFreerun3.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="rdbHWTrigger3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="txtBGain3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbHWTrigger2.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 442</value>
  </data>
  <data name="&gt;&gt;rdbFreerun2.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="rdbtrigger3.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="&gt;&gt;Camera4.Parent" xml:space="preserve">
    <value>CameraControl</value>
  </data>
  <data name="&gt;&gt;tkbGain2.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="groupBox6.Size" type="System.Drawing.Size, System.Drawing">
    <value>1079, 615</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdbFreerun1.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="label4.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="rdbtrigger1.Text" xml:space="preserve">
    <value>触发模式</value>
  </data>
  <data name="groupBox4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger2.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="Camera4.Text" xml:space="preserve">
    <value>相机C4</value>
  </data>
  <data name="&gt;&gt;rdbFreerun3.Parent" xml:space="preserve">
    <value>groupBox7</value>
  </data>
  <data name="&gt;&gt;btnExecute1.Name" xml:space="preserve">
    <value>btnExecute1</value>
  </data>
  <data name="btnExecute2.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 55</value>
  </data>
  <data name="&gt;&gt;txtBExposure2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="groupBox8.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="rdbtrigger3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;rdbtrigger1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdbSWTrigger2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 452</value>
  </data>
  <data name="tkbExposure2.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 348</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tkbExposure3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox5.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;groupBox9.Parent" xml:space="preserve">
    <value>Camera3</value>
  </data>
  <data name="btnExecute1.Text" xml:space="preserve">
    <value>软触发拍照</value>
  </data>
  <data name="CameraControl.Size" type="System.Drawing.Size, System.Drawing">
    <value>1081, 660</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>增益：</value>
  </data>
  <data name="&gt;&gt;Camera3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="tkbExposure1.Location" type="System.Drawing.Point, System.Drawing">
    <value>233, 342</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger3.Parent" xml:space="preserve">
    <value>groupBox8</value>
  </data>
  <data name="label4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="&gt;&gt;rdbtrigger2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtBGain1.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;Camera1.Parent" xml:space="preserve">
    <value>CameraControl</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="groupBox6.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;rdbFreerun3.Name" xml:space="preserve">
    <value>rdbFreerun3</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>210, 75</value>
  </data>
  <data name="rdbtrigger1.Location" type="System.Drawing.Point, System.Drawing">
    <value>453, 48</value>
  </data>
  <data name="groupBox4.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="Camera2.Text" xml:space="preserve">
    <value>相机C2</value>
  </data>
  <data name="&gt;&gt;rdbtrigger3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="groupBox12.Size" type="System.Drawing.Size, System.Drawing">
    <value>1063, 614</value>
  </data>
  <data name="rdbSWTrigger3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="btnExecute4.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 174</value>
  </data>
  <data name="groupBox7.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="Camera3.Size" type="System.Drawing.Size, System.Drawing">
    <value>1073, 626</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="Camera3.Text" xml:space="preserve">
    <value>相机C3</value>
  </data>
  <data name="rdbtrigger2.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 33</value>
  </data>
  <data name="rdbtrigger2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="txtBExposure1.Text" xml:space="preserve">
    <value>3500</value>
  </data>
  <data name="tkbGain3.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="Camera2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 30</value>
  </data>
  <data name="btnExecute3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="btnExecute2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="rdbFreerun3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger2.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;rdbtrigger2.Name" xml:space="preserve">
    <value>rdbtrigger2</value>
  </data>
  <data name="btnExecute2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;Camera4.Name" xml:space="preserve">
    <value>Camera4</value>
  </data>
  <data name="rdbFreerun3.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 48</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>曝光</value>
  </data>
  <data name="&gt;&gt;tkbGain3.Name" xml:space="preserve">
    <value>tkbGain3</value>
  </data>
  <data name="rdbSWTrigger2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="rdbHWTrigger1.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 75</value>
  </data>
  <data name="rdbSWTrigger3.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 54</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;txtBExposure1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;txtBExposure2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>250, 17</value>
  </data>
  <data name="tkbGain1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="label6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="CameraControl.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;tkbGain3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtBGain2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtBExposure2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="CameraControl.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;rdbFreerun1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rdbSWTrigger3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="txtBExposure3.Text" xml:space="preserve">
    <value>3500</value>
  </data>
  <data name="rdbHWTrigger2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdbHWTrigger1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 66</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 360</value>
  </data>
  <data name="groupBox5.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;txtBExposure3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnExecute1.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 32</value>
  </data>
  <data name="&gt;&gt;txtBExposure2.Name" xml:space="preserve">
    <value>txtBExposure2</value>
  </data>
  <data name="&gt;&gt;tkbExposure2.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="rdbtrigger2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;Camera2.Name" xml:space="preserve">
    <value>Camera2</value>
  </data>
  <data name="groupBox8.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="label1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="Camera1.Text" xml:space="preserve">
    <value>相机C1</value>
  </data>
  <data name="&gt;&gt;groupBox7.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;txtBGain3.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;tkbExposure2.Name" xml:space="preserve">
    <value>tkbExposure2</value>
  </data>
  <data name="&gt;&gt;txtBGain2.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="txtBExposure3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>增益：</value>
  </data>
  <data name="&gt;&gt;tkbExposure1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="Camera2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1073, 626</value>
  </data>
  <data name="&gt;&gt;txtBGain2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="tkbGain2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="tkbExposure1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="tkbGain1.Location" type="System.Drawing.Point, System.Drawing">
    <value>233, 442</value>
  </data>
  <data name="btnExecute2.Text" xml:space="preserve">
    <value>软触发拍照</value>
  </data>
  <data name="&gt;&gt;tkbExposure1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="Camera3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 30</value>
  </data>
  <data name="tkbExposure1.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="txtBExposure2.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="rdbFreerun1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="tkbExposure3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBox2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="Camera4.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 30</value>
  </data>
  <data name="rdbSWTrigger3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="groupBox9.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="groupBox9.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>Camera1</value>
  </data>
  <data name="&gt;&gt;groupBox8.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtBGain2.Name" xml:space="preserve">
    <value>txtBGain2</value>
  </data>
  <data name="&gt;&gt;Camera3.Parent" xml:space="preserve">
    <value>CameraControl</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox6.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="rdbFreerun1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="tkbExposure2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbSWTrigger2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;btnExecute3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger3.Name" xml:space="preserve">
    <value>rdbHWTrigger3</value>
  </data>
  <data name="txtBGain3.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="rdbtrigger3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnExecute1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="txtBGain2.Location" type="System.Drawing.Point, System.Drawing">
    <value>443, 460</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="rdbHWTrigger2.Text" xml:space="preserve">
    <value>硬触发模式</value>
  </data>
  <data name="label5.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="groupBox6.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;groupBox12.Parent" xml:space="preserve">
    <value>Camera4</value>
  </data>
  <data name="Camera1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tkbGain3.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnExecute4.Text" xml:space="preserve">
    <value>软触发拍照</value>
  </data>
  <data name="btnExecute1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;btnExecute4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 342</value>
  </data>
  <data name="rdbSWTrigger2.Text" xml:space="preserve">
    <value>软触发模式</value>
  </data>
  <data name="btnExecute4.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 55</value>
  </data>
  <data name="txtBGain1.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;groupBox9.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox7.Size" type="System.Drawing.Size, System.Drawing">
    <value>773, 138</value>
  </data>
  <data name="&gt;&gt;btnExecute2.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="&gt;&gt;CameraControl.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="rdbHWTrigger1.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 54</value>
  </data>
  <data name="&gt;&gt;rdbFreerun2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnExecute1.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 55</value>
  </data>
  <data name="rdbFreerun3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdbFreerun1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;Camera1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtBGain1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>曝光</value>
  </data>
  <data name="txtBGain2.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtBGain3.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="Camera1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1073, 626</value>
  </data>
  <data name="&gt;&gt;btnExecute1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnExecute3.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;txtBGain1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtBGain2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txtBGain3.Name" xml:space="preserve">
    <value>txtBGain3</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;rdbFreerun3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnExecute1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="rdbHWTrigger3.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <data name="&gt;&gt;CameraControl.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;CameraControl.Name" xml:space="preserve">
    <value>CameraControl</value>
  </data>
  <data name="&gt;&gt;tkbGain1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdbHWTrigger3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="txtBGain3.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnExecute3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbHWTrigger1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnExecute2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="txtBExposure1.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="btnExecute2.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 40</value>
  </data>
  <data name="&gt;&gt;btnExecute4.Parent" xml:space="preserve">
    <value>groupBox12</value>
  </data>
  <data name="txtBExposure2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="Camera3.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="txtBGain2.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="btnExecute4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtBExposure1.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="&gt;&gt;txtBGain3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="Camera4.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="btnExecute3.Text" xml:space="preserve">
    <value>软触发拍照</value>
  </data>
  <data name="&gt;&gt;groupBox7.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tkbGain1.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="tkbExposure3.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="&gt;&gt;groupBox6.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnExecute1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;btnExecute3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtBExposure1.Name" xml:space="preserve">
    <value>txtBExposure1</value>
  </data>
  <data name="txtBExposure3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="label2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="rdbHWTrigger2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbSWTrigger1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>增益：</value>
  </data>
  <data name="rdbSWTrigger1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;tkbGain3.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>CameraParaSet</value>
  </data>
  <data name="&gt;&gt;rdbtrigger1.Name" xml:space="preserve">
    <value>rdbtrigger1</value>
  </data>
  <data name="btnExecute3.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="rdbtrigger1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="btnExecute3.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tkbGain2.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="rdbHWTrigger2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="btnExecute1.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="btnExecute2.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="rdbHWTrigger1.Text" xml:space="preserve">
    <value>硬触发模式</value>
  </data>
  <data name="&gt;&gt;groupBox12.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbtrigger2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtBExposure3.Name" xml:space="preserve">
    <value>txtBExposure3</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="Camera4.Size" type="System.Drawing.Size, System.Drawing">
    <value>1073, 626</value>
  </data>
  <data name="btnExecute3.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 42</value>
  </data>
  <data name="&gt;&gt;rdbtrigger1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;groupBox8.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox8.Size" type="System.Drawing.Size, System.Drawing">
    <value>773, 145</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>曝光</value>
  </data>
  <data name="rdbSWTrigger1.Text" xml:space="preserve">
    <value>软触发模式</value>
  </data>
  <data name="&gt;&gt;txtBGain1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;groupBox12.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdbFreerun2.Text" xml:space="preserve">
    <value>连续模式</value>
  </data>
  <data name="rdbtrigger2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label6.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="txtBGain2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="tkbExposure1.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 69</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbFreerun1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtBExposure3.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="btnExecute4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;groupBox6.Name" xml:space="preserve">
    <value>groupBox6</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger2.Name" xml:space="preserve">
    <value>rdbHWTrigger2</value>
  </data>
  <data name="tkbExposure3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbtrigger3.Text" xml:space="preserve">
    <value>触发模式</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox9.Size" type="System.Drawing.Size, System.Drawing">
    <value>1064, 606</value>
  </data>
  <data name="groupBox8.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 172</value>
  </data>
  <data name="&gt;&gt;txtBExposure1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;rdbtrigger3.Name" xml:space="preserve">
    <value>rdbtrigger3</value>
  </data>
  <data name="&gt;&gt;CameraControl.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tkbExposure3.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="rdbHWTrigger3.Text" xml:space="preserve">
    <value>硬触发模式</value>
  </data>
  <data name="&gt;&gt;tkbGain3.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;tkbGain2.Name" xml:space="preserve">
    <value>tkbGain2</value>
  </data>
  <data name="&gt;&gt;Camera2.Parent" xml:space="preserve">
    <value>CameraControl</value>
  </data>
  <data name="&gt;&gt;rdbtrigger3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnExecute4.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="groupBox5.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="rdbFreerun2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="rdbSWTrigger1.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <data name="Camera3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;groupBox8.Name" xml:space="preserve">
    <value>groupBox8</value>
  </data>
  <data name="&gt;&gt;groupBox5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;Camera3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox7.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtBGain1.Name" xml:space="preserve">
    <value>txtBGain1</value>
  </data>
  <data name="label5.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 60</value>
  </data>
  <data name="&gt;&gt;tkbExposure1.Name" xml:space="preserve">
    <value>tkbExposure1</value>
  </data>
  <data name="txtBExposure1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbtrigger1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1079, 658</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="label3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;tkbGain1.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;Camera1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 26</value>
  </data>
  <data name="&gt;&gt;Camera4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox9.Name" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="btnExecute4.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="Camera2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="groupBox9.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;Camera2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdbSWTrigger2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox12.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 6</value>
  </data>
  <data name="rdbSWTrigger2.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 54</value>
  </data>
  <data name="&gt;&gt;rdbFreerun1.Name" xml:space="preserve">
    <value>rdbFreerun1</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rdbtrigger1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>250, 162</value>
  </data>
  <data name="txtBGain1.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="rdbHWTrigger1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbHWTrigger3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox6.Parent" xml:space="preserve">
    <value>Camera2</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tkbGain3.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 452</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 435</value>
  </data>
  <data name="tkbGain1.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;tkbExposure1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="rdbFreerun1.Text" xml:space="preserve">
    <value>连续模式</value>
  </data>
  <data name="txtBGain3.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 30</value>
  </data>
  <data name="CameraControl.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 3</value>
  </data>
  <data name="&gt;&gt;rdbHWTrigger1.Name" xml:space="preserve">
    <value>rdbHWTrigger1</value>
  </data>
  <data name="label6.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="rdbFreerun3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="&gt;&gt;groupBox8.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;rdbtrigger2.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="tkbExposure3.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 360</value>
  </data>
  <data name="tkbGain3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="Camera2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tkbExposure3.Name" xml:space="preserve">
    <value>tkbExposure3</value>
  </data>
  <data name="Camera2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="groupBox8.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger2.Name" xml:space="preserve">
    <value>rdbSWTrigger2</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;groupBox9.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtBExposure1.Location" type="System.Drawing.Point, System.Drawing">
    <value>461, 363</value>
  </data>
  <data name="rdbSWTrigger3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtBExposure1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbtrigger3.Parent" xml:space="preserve">
    <value>groupBox7</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="groupBox9.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 11</value>
  </data>
  <data name="rdbtrigger3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 14.25pt</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="rdbHWTrigger3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="rdbFreerun2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;rdbSWTrigger3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;rdbFreerun2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtBExposure3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tkbExposure3.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="rdbSWTrigger2.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 33</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>