﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.LookAndFeel;


namespace BingSolhalcon.UI
{
    public partial class PdfViewer : RibbonForm
    {

        public PdfViewer()
        {
            InitializeComponent();
            
        }

        private void PdfViewer_Load(object sender, EventArgs e)
        {
            string pdfFile = Application.StartupPath + "\\readme.pdf";
            if(!System.IO.File.Exists(pdfFile))
            {
                MessageBox.Show(this, "帮助文档丢失", "error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Close();
                return;
            }

            var viewer = new PdfiumViewer.PdfViewer();
            viewer.Location = new Point(5, 5);
            viewer.Dock = DockStyle.Fill;
            Controls.Add(viewer);
            viewer.Document = PdfiumViewer.PdfDocument.Load(pdfFile);
        }
    }
}
