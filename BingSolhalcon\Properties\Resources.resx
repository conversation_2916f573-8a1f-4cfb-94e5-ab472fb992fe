﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="logo" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcI
        CQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwM
        DAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCACAAIADASIAAhEBAxEB/8QA
        HwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIh
        MUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVW
        V1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXG
        x8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQF
        BgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAV
        YnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOE
        hYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq
        8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KbNMlvE0kjLGijLMxwAK53xv8UNM8Dr5c0nnXhGVtoz8/1P
        90fX8M15Z4i+I+oeM7jM8nlW4OVgThR6Z9T7n9KAH/tK/tzaT+ztLpP/ABJ7rXItUaVfMhnEIj8vZ0yp
        3Z3+3TvW14X/AGw/Duu6dbz3FpqVj9ojWTlFkVdwB6g5/SvkL/gofKH0rwb7Nd/ygqHxPfeMrbwxov8A
        widno1yRaq05vpmXJ2rtUKMcHJJbdxjoc1tGCcUZyk0z7v0n47eEdZx5euWcZPafMGP++wBXSadrVnrE
        e+0ura6XruilDj9DX5a6X+014mh1Ca1u/C+nam9vK0Ep0nVoZmSRWIK+WjSMMY6NtPqB0r2SS5kgfdGz
        I69GU4I+lTyp7M0qQqUny1YtPzVvzPsT4sfGzw98FdMtbzxBdSWtvdyNEjJC0nKxSSnO0f3Y3P4V4j8a
        /wBt0+LrXWPCnwnsdY8ceMoT9mki8OxJcppkmRuF1eMy2dowXJ2yyiQj7qk4r58+OGn33xW8EHR9Wvr6
        /wBO80MYJ7l2QAqykAZ4yrMDjqCR3r1/wZ8c9a+AP7P8tro9rpJs/C+kzmztXtdsQ8pGYA7CpOSOTnJy
        STk5p+zJUrmb/wAEavjD4u+I3hD4oaT4q8Taz4rj8N+LbiHTLrVp/tF3b27M6i3Mp+Z0TysqWyw3sCSM
        AfaFfnn40/4Ki6H+xDruj6bH8MdPuLXxhpq69cT6Rcx2BhZ554/LEXlEOqiPglxjOK7fwD/wXh+C3irY
        mrWvjDwzIfvtdaes8S/Qwu7Ef8BH0olTbd0gUu59q0V4r4D/AOCi/wAD/iR5Y034meF42k+6l/cHTnY+
        m24CHPtjNeuaB4n03xVYrdaXqFjqVq3Sa1nWaM/8CUkVnZrcovUUUUgCiiigD51+M8uPixqv+9H/AOik
        rN0+birXxskx8XNW/wB6P/0WlZdhLzQB4T/wUGYPpvg/H966/lDXUeGj/wAU3p//AF7R/wDoIrkf2/pP
        M0/wj9bs/pDXWeHP+ResP+vaMf8Ajoroj8KMZbmPrXwa8J6/NJNeeG9FmnmbfJN9jRZXbnJLgBj1PfvW
        M37Pfh2ysJrfS11LQxN1k0+/lhkTjA2HJ2fRcV3zcVE45/nWLw9Ju7ivuPSp5xj4R5Y1p27cztp5Xsc/
        H4fHhnwZDZfa76/+zbU+0Xkvmzy89XbAyeetdF8Rv+SI+Kv+wRff+ipKzvFHy6I3++v860fiScfBDxX/
        ANgi+/8ARUlbRVtEcNSpKcnOW71Z8I/8FErj7fq3wxm6+Z4LgP8A5OXdfOkYw1e//tyz/arP4Tyf3vBE
        B/8AJ28rwE8NXQtjEuWx+Zfr/St7w34hvvDl8t1p97eWFyvSa3maKQf8CUg1z0DfMv1/pWnatQM9x8Af
        t8/Gb4fCMad8SPFLLH91L27N8ij0Czhxj2xivavAn/Baf4xeHdi6kvhfxEnRmu9PMMjfQwuig/8AASPa
        vjS2bkVegfNS4xe6LvY/UP8AZv8A+CyrfGD4iaP4Z1jwItjcatL5IvLPU96KdpP+raMHt/fNfW0fxz0E
        ajptrcS3FrcavdLZWqyRFhJMyswXK5AyEbk4HFfjF+xRJj9qPwX/ANf3/sj1+mHiFd/jr4fNn7vii1P1
        /dzD+tc9WKjsVDV6k3xvkx8XtX/3k/8ARaVj2UuMVo/HJ/8Ai7+sf76f+i0rlrvxZpvh2Pdf6hZ2S9f3
        8yx/zNYlHj/7esm+y8I/W7/lBXaeHeNAsf8Ar3j/APQRXjv7VHxr8NfFubTrXw9qialN4cu7qy1HZFIq
        wTbYG2BmUB+O6kjPGcgivYPD5xoNj/17p/6CK6I/CjKW5cNR96cz81G0goEUfFhxoj/76/zqT4sTXR+A
        njLykhVhouohGMh4PlS4ONtU/GN6kWiYY/ekUD+f9Kf8ZPENnon7NvjO+u7iG2tU0a/zJK4RctHIqjJ7
        sxAA6kkAcmgIn54ftz+J7q10z4M+XIuJPh/ayHKg5Jvb3/Cvm2L4q6glwySQ2rBWI4Vgf517P4l+OsPx
        k8K+BV1bSYbO88N+HotG8lZN7kRzzSbmVsFWPnYx7V5fraaLPcXXl2bRy7ztJjA5rbVIlbm38K/FEPjf
        xdZ6bfS2mlw3DYN1NNtjTjvwa90t/wBnSO8/48PE2h3noEu48/qRXy7aaZDLq8MaqqKyM3DbeR75FdFa
        6WsXC3FwmP7s2RXVQrUYxtVhzeadv80fO5xlubVqyq5di1SVrOMqamm++8ZfjY+iG/Zk8RRcw/Zblf8A
        pnKG/kTWR4n+Fut+BtP+1ahZyQ24OC+04/lXl/gya4/t77K2rXlvCIDKGMxUhgwHBBHqa6/UNH1DXtP+
        yyeItRlhzkAapcL/ACetKlTCOL5IyT9U1+SOXB4XianXj9ZxFGdO+tqc4yt5P2klf1R6l+xHrNrdftSe
        CljuYJGa/wCAsgJ+41fp5rz/APFceAf+xotP/QJa/DD9kX4meKNC/wCCx/w68HN4h1C68OrcQzG0uCsy
        72inJIdl3jlR3r92vA1np/xQ8S+FrxZrhY9N1kXUflkLmWAyRlWyDldwYcYPA5ryazu7H2PLK3MttP6+
        aTPhP/g4J/az8Xfsr/FzwjceF5ZJI9Zmv4r2zE7QpdbIbPyyWXnKlzjthj9R+X3xE/4Kj/Fa6t5mtZtH
        0lwud8Fn50mfcyswP/fNfoh/wct2kd38V/A7Pz5U+pkZ/wCuNjX42/ECPNrdBf4kAHH+2tVH4Qe5+pf7
        LGtN4o+F8mqXNxJcalql8bu+kaMJvmeCEsQBxycngAewr740W52aFZ/9cE/9BFfnj+xgskvwwuLprxpo
        ZriGOO0522Hl2lujBeTxIwMp4X5pW69T9+6bdbdHtef+WKf+giqWsdRVElK0Xc0rjUfLHWsTWfFH2O3k
        bdjapI5qDVtUKBua4Hxv4iaKyn+b+A9/anYzOV1X42TNdeXNdSSKrZAZ8jNZPxb+LN54r/Z78R2bXU0k
        GIxsLkrgXUZHH4V4hrniqZ9T+VuM+tdPJdNcfAnxCW7qn/pQlMZ8oeM9F0+88TaT4l0XVmu9ZXxXLoet
        6bcRLi2V0Z4pY2HOGGQd2eelRatG1prF15kY/eSMArZ+X3GD/wDWrzy08Q+JvBf7SnijSI9LEmk+KPEN
        rffaSokZPsyNtIHO3lmBIweozgkH0uUx+IrSa4aTbdQhnZRzkD1HavUzOeGkqTwyt7q5v8Wt3+R5+Xxx
        CdT6w7+8+X/DZW/G5V014jr9v5xkEfltnYAW/WupZrcKnktMxPXeoH8ia42xbfrcPOMRMfrXU2K+dPCv
        947fpXlnpm/8P3sR8Q7V9StZr6wjh3TwRllaRdwBAK8jrnPqK98i8Z/CfwqVE3gm6eT0+2m4z+cmK8G+
        GNxDF44maXT9U1IjT38uGxID7t64LHa2F9eO4r1D4PfDiT4ueMU0nW7PWNKSSN2injt9qqygt8xcdCBj
        jvigDx/4Ja3ovj7/AILx+C9c8JWLW3h+yutP02VVi2Ja3RtJ3MbYyNxw/fko/oa/d74CxvFq2irIqKft
        0rAJGEG0ySFTgeoIOe+c96/Ev9l/4SW3wT/4KmSaHZ3k97Cvj/w/dCWZQrZl07XXI44wCcV+4HwlOPE+
        gjn76dev3TWEir9D8yP+DmbzG+Jfg1o22stxqX/oqxr8fvF1k05kQ9GH+B/pX6+/8HNJ3fEXwmu4rmbU
        +Qef9XYV+RXjGMN8NUvMstyLkJvB+YjB61cfhFLc/Tb9lX4ba78PfhNDJrGl3mn2+szreafJMm1byBrW
        2KyIe6kEcjvx1zX25aSY0e1/65L/AOgivHvEZ/4xg+CI648LW3P/AG6Wley2cWdItv8Arkv8hVR2IluY
        etuxQ815/wCL7KS7glUMfmUjp7V6df2HnKeKyZ/C32mT7uaok+XbT4Grf3MbTprHmn73lxJtz7Zr0GH4
        I20fwU8Rq0erLFHZXVw7SKi48kPKvT/bRc+2a9x0PwEpnU+WBz6VpeP/AA/HYfALxoQuNuj6if8AyHLR
        fUaPzTsPBvhfxNBeeLtN8XeE5rFvPvUxqNvcT72ikKRki2MiMpZEZFkwWVj3GPF9Jsvt1pcXbNt2EttH
        c1m/s33dnonwet7Py1DXp80lzkEkkt1+tWrW42rfBThcAcf71BV76FvTpN2uW4/6Ztjr/Suz8LQm81RV
        3hTCpbJHXoP61wejt5viC3X5eY26nA/Guw8NSf8AE0h4ATJBPTsaBHcfs+fFib4R/FObVoLWK8kbTZIP
        Ldio5kQ5yP8Adr2lf+Cg2qh/+Rd0/g/8/D/4V84fDO6a1+JVrd/2bDrENjCbie0mYLHLGrDIYnjHNfXv
        7LPxx0nx4+paYvh/R/DMNmFkhiicbZmYnd1UcjA/Okyj5o/ZA+Ltx8bf+Cm7a5cWcNjI3xA8P23lROXU
        CLT9eQHJ9cZr90fhqceO9I7YuAMfga/BD/gn/PH/AMPDYFUrub4i6Y2AeSBH4k/xH5iv3r+Gb58f6T/1
        8D+RrKQI/K//AIOdb7yvjf8AD61a6FtFfXmpxNI65WLKacucAZ/ir86rf9mW/wDFHhSzW61dbPw1IgvZ
        tRtrFb426bc7mjjl38Ak4xnjHXiv1e/4L7fsMeNf20/iTpj+C5tI+1eFJ71pYLy5MEk5nS02eWdpXI8h
        s7ivUe+PhfRv2ZNM+Gug6pcfEn4S/Fj4Xap4UtI71tb8KahJf2OrlZFV2AKvbxkbgxTIDKGCksVVqjLS
        xT3Pvz4raQ/hP4M/C3Sf3lx/ZWhpaCXCr5ojgtk3Y3HGcZxk49T1r1zTJJH0u1At5P8AUpyWXHQe/wDS
        vh7/AIK0ft6X37Kfw2+B1x4f8Px+J7fxZpN3Mkl3I9vJGkcViUJVVPLCbJHGMVwGjf8ABZD4xeIrC3XR
        fhXps4Mahdtle3XYf3GXNVDbQzlufpIbW4b/AJZQ/wDf0/8AxNTW+nTEj9zD/wB/D/8AE1+eFr+33+1f
        4yXdp/wqktVbo0fhG/Vf++pXI/WrcXxl/be8Uf8AHl4fm0/d0zpVhDj/AL/n+dPUk/R3TbKZSOIYsfWT
        P/oNY/xnuxafs7eOCxGV0TUySBgf6qXtX59S6B+3p4lX95fNp7N3lv8ASLdR/wCA4Y/pX2Ra+FPFniT9
        iyfwrrl9AfG+oeEptLvrxpi0X9oSWrRvIXUZK+aSdwGccgdqWxaPw38JePFj8IaZaxqrLBEAxbP3q6DT
        /HskaMo8kBhg8f8A16+oPDP/AAQC8doqrqHxA8J2ajta2txOB9NwSu/8O/8ABAUwhW1H4ss/qlv4d24/
        4E1yf/QaOYZ8X2ni52mWTcqsowMCtiy8e3cTZW42n1Cj/CvvLQf+CFfgazMY1Dx14uutxw32eK3t+x6b
        keu60D/gi58G9KC+dfeN9Q29ftGpxLn/AL9wpRzAfm9aeNbiCVpI7iSORk8ssp2kqeccdqtQ+Lrhv+W0
        x/4GR/Wv1M0L/glN8B9K2+b4TvL8r3uNZvB+iSqP6V2WhfsBfBHQtvkfDvQX29PtHmXH/ox2o5gPye/4
        J/eBdWuP+Cq3gDxDGv8AxKbjUIXlLT5LzLBIhO3OeFx+Z9TX9CPwrm3/ABC0kf8ATwP5GvBfCXwC+HXw
        /wBQt77Q/Avg/Sb61cNFdWukW8dxGemRIF3A4z3r2b4K6j9o+JukL/02z+hrOXcEcd+0HPn42eIP+u6/
        +gLXKCRZkZWVWVhghhkEelbn7QF2sHxx1q2lkjS6mVLxYC48xoGyiybeuwsjqD0JRh1Brm4nqSzyf9qr
        bEnh2ONVSONJ1VVGFUDysY+lddply32GD/rmv8hXGftTvl9B/wByf/2nXY6cuLCH/rmv8q2j8JD3Li3D
        DvViG5b1qqo5qReKkDQiuc1CXzpVz9Zf/QmqJGqKOYjTrj6y/wDoTUCRsB8U5ZcVWjn309phEjMzBVUZ
        JPQUDJXnPmR/739DU63TV574x/aV+HngR1/trx14P0to2OUudYt436HgKXyT7AV5v4r/AOCpnwL8Jblk
        8cwX0y9I7Cwubrd9GWMp+bCjUD6MW+Yd6kXUGHeviPxX/wAFwvhdpO5NL0HxprEg+632aC3hP4tLu/8A
        HK818Uf8F3765Zo9B+GtvG38Et9rDSbvrGkS4/77NVysD9JZNUYKOe4/mK7n9nfUzP8AGHRVzwZT/I1+
        NN9/wVz+OXxF1aPT9I07wfoUlxkwlbQxnA55kuZjHn8Bn0r64/4JcL+1Brvx9sfGnxK1D/ijY7K5WOB2
        sY98pG2Nlit1ycHJDN25ycik46Aj2f8A4K3f8E6fiD+0l8XvDPxL+GPiS20fxR4f0oaVLBLey2cjRJNN
        MjxSIhG4tK6srkAjb718meDv+CjnxC/Zf8Yr4P8Aj74P1GG4hOwapb24iuGUcbygxFMv+3EVwP4WNftt
        rfgi6uds0eJF24KHqPpXmfxz/ZA8L/tI+DptD8XeGrHXLCQcJcRfPC395HHzIw9VINQqitZl2Pzt+L/7
        Q3gj4teGvD+s+H/FGj6hp7LMGcXAjaFj5Z2yI2Gjb/ZYA10muftc/C3wHYxrq3xE8F2csaDdC2rwNN0/
        55qxb9K+V/8Agp7/AMG+fxO8Ba7byfB3wlrfjjwnqrKGQSxyXukzbidjJw7xbdpEgBx8wbGAW8z+Gn/B
        vX+0P4ttIpdY8MHwvvODHdlJJEGepCMRz1xnPPODkVp7ttyNbn1f4q/4K1fAnwtuWPxdcatMn/LOw0q6
        kz9HaNUP/fVea+K/+C6Xw9sSy6L4S8Y6o69GuVt7SNvoRI7fmoqz4F/4NnPFE2xtf8QXUfqtrbgf+hf4
        17J4K/4NuPBOmKv9q/29qTd9115IP4Lmj3QPkvxN/wAF3fEF8zLoPw50mx7K9/qkl3n3KpHFj6ZP1rhd
        Y/4Kt/H7xlB/xLl0fR47suYxpmhmbcCTnaZjLnGfev1K8E/8EIPg/wCE9Ot4l8FeZc2tyt3Heyarefao
        3UcbXWVcAHnpmvavB/8AwT78JeCfLbT/AA7ZwXCoI3uZnkuriUD+9LMzv+AIHtRzxQWPw/T4q/tXfGo/
        6Pq3xGl83vp8f9mj/wAgrHj8KLj/AIJ0/tHfFuPztU0vWNSdvm/4nOtbmJ9/Ncmv36079maOBVWOy+X2
        Q4/wrZsv2dJk6QxxfUj+maXtF0Hys/D74U/8EJfHHiTS7C88SX6+H1lQ/abfNvLLHIMYEflTSbkPzfO4
        Q8D5euPZvDv/AAQL8IpahdQ8Q+JJZD1ZZIlx9Pk/mDX632n7PSrjzJlX2VM/rWpafAjT4fviab1DNgfo
        BS9qHKfmb8K/+CKXwl8C6a0OpR694mZZxPANQe02xnBBVmjt0ldDnhTJgEZ54x7V4G/YV+HXg4qmk+Dd
        Fjdenl2SO38ia+27P4UabZ/dsYDjoXXeR+ea1IPCaQqFWNVUdABgCp9oPlPmTw38B5NNRVsdD+zqOMGM
        RAD6Niut034UalZXbX1z5G7BL/OWkckd+MfrXua+GlA+7SXvhxWs3G3tU84cp//Z
</value>
  </data>
</root>