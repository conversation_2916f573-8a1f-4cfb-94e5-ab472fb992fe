# 📷 TestSDKCameraController 实现说明

## 🎯 概述

`TestSDKCameraController` 是基于 TestSDK.xaml 界面中已验证可用代码创建的 `ICameraController` 接口实现类。这个实现直接使用了 MvCameraControl SDK，确保了功能的可靠性和稳定性。

## ✨ 主要特点

### 🔧 基于已验证代码
- **直接移植**: 从 TestSDK.xaml.cs 中提取已验证的核心功能代码
- **保持兼容**: 使用相同的 SDK 调用方式和参数设置
- **稳定可靠**: 基于已经在界面中测试通过的功能实现

### 🎛️ 完整接口实现
- **设备管理**: 设备枚举、连接、断开等功能
- **图像采集**: 连续采集、单次拍照、异步采集
- **参数控制**: 曝光时间、增益、帧率、触发模式等
- **高级功能**: 图像保存、录像、参数范围获取等

### 🔄 事件驱动架构
- **图像接收事件**: 实时图像数据回调
- **连接状态事件**: 设备连接状态变化通知
- **错误处理事件**: 统一的错误信息处理

## 🏗️ 架构设计

### 核心组件

```
TestSDKCameraController
├── SDK 封装层 (MvCameraControl)
├── 设备管理模块
├── 图像采集模块
├── 参数控制模块
├── 事件处理模块
└── 资源管理模块
```

### 关键类和接口

1. **ICameraController**: 相机控制接口定义
2. **TestSDKCameraController**: 具体实现类
3. **CameraDeviceInfo**: 设备信息模型
4. **CameraParameters**: 参数配置模型
5. **各种EventArgs**: 事件参数类

## 🚀 使用方法

### 基本使用流程

```csharp
// 1. 创建控制器实例
using var controller = new TestSDKCameraController();

// 2. 订阅事件
controller.ImageReceived += OnImageReceived;
controller.ConnectionStatusChanged += OnConnectionStatusChanged;
controller.ErrorOccurred += OnErrorOccurred;

// 3. 枚举设备
var devices = controller.EnumerateDevices();

// 4. 连接设备
bool connected = controller.Connect(0);

// 5. 设置参数
controller.SetExposureTime(10000);
controller.SetGain(5.0f);

// 6. 开始采集
controller.StartGrabbing();

// 7. 停止采集
controller.StopGrabbing();
```

### 高级功能示例

```csharp
// 触发模式控制
controller.SetTriggerMode(TriggerMode.Software);
controller.SoftwareTrigger();

// 图像保存
controller.SaveImage("image.png", "PNG");

// 参数范围获取
var range = controller.GetParameterRange("ExposureTime");
Console.WriteLine($"曝光时间范围: {range.MinValue} - {range.MaxValue}");

// 设备统计信息
var stats = controller.GetDeviceStatistics();
foreach (var stat in stats)
{
    Console.WriteLine($"{stat.Key}: {stat.Value}");
}
```

## 🔍 功能对比

### 与 TestSDK 界面功能对应关系

| TestSDK 界面功能 | TestSDKCameraController 方法 | 说明 |
|-----------------|----------------------------|------|
| 搜索设备按钮 | `EnumerateDevices()` | 枚举可用设备 |
| 打开设备按钮 | `Connect()` | 连接选定设备 |
| 关闭设备按钮 | `Disconnect()` | 断开设备连接 |
| 开始采集按钮 | `StartGrabbing()` | 开始连续采集 |
| 停止采集按钮 | `StopGrabbing()` | 停止连续采集 |
| 连续模式选择 | `SetTriggerMode(Continuous)` | 设置连续模式 |
| 触发模式选择 | `SetTriggerMode(Software/Hardware)` | 设置触发模式 |
| 软件触发按钮 | `SoftwareTrigger()` | 执行软件触发 |
| 保存图像按钮 | `SaveImage()` | 保存当前图像 |
| 曝光时间设置 | `SetExposureTime()` / `GetExposureTime()` | 曝光时间控制 |
| 增益设置 | `SetGain()` / `GetGain()` | 增益控制 |
| 帧率设置 | `SetFrameRate()` / `GetFrameRate()` | 帧率控制 |
| 像素格式选择 | `SetPixelFormat()` / `GetPixelFormat()` | 像素格式控制 |

### 扩展功能

除了 TestSDK 界面的功能外，还提供了以下扩展功能：

- **多种连接方式**: IP地址、序列号、MAC地址连接
- **异步操作**: 异步图像捕获
- **参数范围查询**: 获取参数的有效范围
- **设备统计**: 获取设备运行统计信息
- **网络诊断**: Ping 测试、网络统计（GigE设备）
- **录像功能**: 视频录制控制

## 🛠️ 技术实现细节

### 线程安全
- 使用 `lock` 机制保护关键资源
- 图像接收线程独立运行
- 事件触发采用异步模式

### 内存管理
- 实现 `IDisposable` 接口
- 自动释放 SDK 资源
- 图像缓冲区及时释放

### 错误处理
- 统一的错误码处理
- 异常信息详细记录
- 事件方式通知错误

### 性能优化
- 图像数据零拷贝传输
- 参数缓存减少SDK调用
- 线程池管理接收线程

## 📋 已实现功能清单

### ✅ 完全实现
- [x] 设备枚举和连接
- [x] 图像采集（连续/单次）
- [x] 基本参数控制（曝光、增益、帧率）
- [x] 触发模式控制
- [x] 图像保存（多格式）
- [x] 事件处理机制
- [x] 资源管理和清理

### 🚧 部分实现
- [x] 分辨率和ROI控制（基本功能）
- [x] 像素格式控制（基本功能）
- [x] 设备统计信息（基本信息）

### ⏳ 待实现
- [ ] 参数配置文件保存/加载
- [ ] 白平衡控制
- [ ] 设备温度监控
- [ ] 网络强制IP配置
- [ ] 设备重启功能
- [ ] 完整的录像功能

## 🔧 使用建议

### 最佳实践
1. **资源管理**: 始终使用 `using` 语句或手动调用 `Dispose()`
2. **事件订阅**: 及时取消不需要的事件订阅
3. **错误处理**: 监听 `ErrorOccurred` 事件处理异常情况
4. **参数设置**: 在连接设备后再设置参数
5. **线程安全**: 在多线程环境中注意同步访问

### 性能优化建议
1. **图像处理**: 在事件处理中快速处理图像，避免阻塞
2. **参数缓存**: 频繁访问的参数可以缓存避免重复SDK调用
3. **内存管理**: 及时释放不需要的图像资源

## 🧪 测试和验证

### 运行示例
```csharp
// 运行完整示例
await TestSDKCameraControllerExample.RunExampleAsync();

// 快速设备枚举
TestSDKCameraControllerExample.QuickDeviceEnumeration();
```

### 单元测试
建议为以下功能编写单元测试：
- 设备枚举功能
- 连接/断开功能
- 参数设置/获取功能
- 图像采集功能
- 事件触发机制

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. **SDK版本**: 确保 MvCameraControl SDK 版本兼容
2. **设备驱动**: 确认相机驱动正确安装
3. **网络配置**: GigE相机需要正确的网络配置
4. **权限设置**: 确保应用程序有足够的系统权限

---

*基于 TestSDK.xaml 界面功能实现，确保稳定可靠的相机控制体验。*
