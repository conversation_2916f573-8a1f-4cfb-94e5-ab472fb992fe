﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Threading.Tasks;
using GemBox.ExcelLite;
using System.IO;

using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.LookAndFeel;
using BingSolhalcon.resources;



namespace BingSolhalcon.UI
{
    public partial class Technology : RibbonForm
    {
        public delegate void delegateGetDatabasename(ref string databasename,ref string numberofdecimal);
        public event delegateGetDatabasename evenGetDatabasename;
        string m_databasename, numberofdecimal;
        string connection;
        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx)); //自定义资源字段

        public string wheelParam = "";

        private Dictionary<string, string[]> HeaderLangMap = new Dictionary<string, string[]> {
            { "序号", new string[]{ "id", "id", "id" } },
            { "轮毂型号", new string[]{ "type", "type", "tipo" } },
            { "车轮直径", new string[]{ "diameter", "Diamètre", "Diámetro" } },
            { "轮辋宽度", new string[]{ "rim width", "Largeur de la jante", "ancho de la rueda" } },
            { "偏距", new string[]{ "et", "et", "et" } },
            { "后距空间", new string[]{ "back distance", "espace à l'arrière ", "distancia de retroceso" } },
            { "总高", new string[]{ "height", "hauteur  ", "altura " } },
            { "轮芯厚度", new string[]{ "core thickness", "Épaisseur du noyau de roue ", "espesor del núcleo de la rueda " } },
            { "帽槽深度", new string[]{ "deepth of cap groove", "profondeur de la fente du capuchon ", "profundidad de la ranura del sombrero " } },
            { "帽槽深度上公差", new string[]{ "up tolerance of cap groove depth", "tolérance sur la profondeur de la fente du chapeau", "tolerancia en la profundidad de la ranura del sombrero" } },
            { "帽槽深度下公差", new string[]{ "lower tolerance of cap groove depth", "tolérance sous la profondeur de la fente du chapeau", "tolerancia a la profundidad de la ranura del sombrero" } },
            { "帽槽倒角", new string[]{ "chamfer of cap groove", "chanfrein de la fente du capuchon ", "achaflanar la ranura del sombrero " } },
            { "孔数", new string[]{ "number of holes", "nombre de trous", "número de agujeros" } },
            { "PCD节圆直径", new string[]{ "PCD", "PCD", "PCD" } },
            { "节圆直径上公差", new string[]{ "up tolerance of PCD", "tolérance sur le diamètre du PCD", "tolerancia en PCD" } },
            { "节圆直径下公差", new string[]{ "lower tolerance of PCD", "tolérance sous le diamètre du PCD", "tolerancia inferior del PCD " } },
            { "PCD位置度", new string[]{ "PCD position", "degré de position PCD", "posición PCD " } },
            { "螺栓孔直径", new string[]{ "bolt hole diameter", "Diamètre du trou de boulon ", "diámetro del agujero del perno " } },
            { "螺栓孔直径上公差", new string[]{ "up tolerance of bolt hole diameter", "Tolérance sur le diamètre du trou de boulon ", "Tolerancia en el diámetro del agujero del perno " } },
            { "螺栓孔直径下公差", new string[]{ "lower tolerance of bolt hole diameter", "Tolérance inférieure au diamètre du trou de boulon ", "Tolerancia bajo el diámetro del agujero del perno " } },
            { "中心孔直径", new string[]{ "center hole diameter", "Diamètre du trou central ", "Diámetro del agujero central " } },
            { "中心孔直径上公差", new string[]{ "up tolerance of center hole diameter", "Tolérance sur le diamètre du trou central ", "Tolerancia en el diámetro del agujero central " } },
            { "中心孔直径下公差", new string[]{ "lower tolerance of center hole diameter", "Tolérance inférieure au diamètre du trou central ", "Tolerancia bajo el diámetro del agujero central " } },
            { "帽止口直径", new string[]{ "cap mouth diameter", "Diamètre du bouchon ", "Diámetro del tapón " } },
            { "帽止口直径上公差", new string[]{ "up tolerance of cap mouth diameter", "Tolérance sur le diamètre du bouchon ", "Tolerancia en el diámetro del tapón " } },
            { "帽止口直径下公差", new string[]{ "lower tolerance of cap mouth diameter", "Tolérance inférieure au diamètre du bouchon ", "Tolerancia bajo el diámetro del tapón " } },
            { "螺栓孔厚度", new string[]{ "bolt hole thickness", "Épaisseur du trou de boulon ", "Espesor del agujero del perno " } },
            { "螺栓孔厚度上公差", new string[]{ "up tolerance of bolt hole thickness", "Tolérance sur l'épaisseur du trou de boulon ", "Tolerancia en el grosor del agujero del perno " } },
            { "螺栓孔厚度下公差", new string[]{ "lower tolerance of bolt hole thickness", "Tolérance sous l'épaisseur du trou de boulon ", "Tolerancia bajo el espesor del agujero del perno " } },
            { "沉头孔直径", new string[]{ "bolt hole counterbore diameter", "Diamètre de la tête de fraisage du trou de boulon ", "Diámetro de la cabeza hundida del agujero del perno " } },
            { "重量", new string[]{ "weight", "Poids ", "Peso " } },
            { "重量上公差", new string[]{ "up tolerance of weight", "Tolérance sur le poids ", "Tolerancia de peso " } },
            { "重量下公差", new string[]{ "lower tolerance of weight", "Tolérance sous poids ", "Tolerancia bajo peso " } },
            { "弦长", new string[]{ "chord length", "Longueur des cordes", "Longitud de la cuerda" } },
            { "弦长上公差", new string[]{ "up tolerance of chord length", "Tolérance sur la longueur de corde", "Tolerancia en la longitud de la cuerda" } },
            { "弦长下公差", new string[]{ "lower tolerance of chord length", "Tolérance sous la longueur de corde", "tolerancia bajo la cuerda" } },
            { "深度检测工位伺服移动", new string[]{ "deep detection station servo movement", "Mouvement servo de station de détection de profondeur", "movimiento servomotor de la estación de detección profunda" } },
            { "深度检测工位伺服水平移动", new string[]{ "deep detection station servo horizontal movement", "Détection de profondeur station servo mouvement horizontal", "movimiento horizontal del servomotor de la estación de detección profunda" } },
            { "中心孔工位伺服移动", new string[]{ "center hole station servo movement", "Servomoteur de station de trou central mobile", "servomotores de la estación de trabajo del agujero central" } },
            { "帽止口工位伺服移动", new string[]{ "cap stop station servo movement", "Cap stop station servo mouvement", "servomotores de la estación de trabajo de la tapa" } },
            { "螺栓孔厚度工位伺服移动", new string[]{ "bolt hole thickness station servo movement", "Trou de boulon Épaisseur station servo mobile", "servomotores de la estación de trabajo de espesor del agujero del perno" } },
            { "标记工位伺服移动", new string[]{ "mark station servo movement", "Marquage station servomoteur mouvement", "servomotores de estaciones marcadas" } },
            { "偏移角度", new string[]{ "offset angle", "Angle de décalage", "ángulo de desplazamiento" } },
            { "轮毂周长", new string[]{ "perimeter", "Circonférence du moyeu", "perímetro del cubo" } },
            { "厚度检测开关", new string[]{ "thickness switch", "Interrupteur d'épaisseur", "interruptor de espesor" } },
            { "最大实体原则开关", new string[]{ "maximum material principle switch", "Commutateur principe entité maximale", "interruptor de principio físico máximo" } },
            { "弦长测量开关", new string[]{ "chord length switch", "Interrupteur de mesure de longueur de corde", "interruptor de medición de longitud de cuerda" } }
        };

        public Technology()
        {
            InitializeComponent();
        }
        private void FormLoad(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(Technology));

            evenGetDatabasename(ref m_databasename,ref numberofdecimal);
             connection = @"Server=" + m_databasename + ";Database=MyDB;Trusted_Connection=SSPI";
            Btn_LoadTechnology.Enabled = false;
        }


        public delegate void OnImportedDelegate();
        public OnImportedDelegate OnImported = null;

        //工艺配方导入到数据库
        private void Btn_ImportExc_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Task t_task = Task.Factory.StartNew(() => {
                    NPOI t_npoi = new NPOI();



                    string xls = Directory.GetCurrentDirectory() + "\\config\\";

                    if (!wheelParam.Equals(""))
                    {
                        xls += wheelParam;
                    }
                    else if (File.Exists(xls + "轮毂参数.xls"))
                    {
                        xls += "轮毂参数.xls";
                    }
                    else if (File.Exists(xls + "HubParameter.xls"))
                    {
                        xls += "HubParameter.xls";
                    }
                    else if (File.Exists(xls + "MoyeuParamètres.xls"))
                    {
                        xls += "MoyeuParamètres.xls";
                    }
                    else if (File.Exists(xls + "HubParámetros.xls"))
                    {
                        xls += "HubParámetros.xls";
                    }

                    
                    if (!File.Exists(xls))
                    {
                        if(!xls.EndsWith(".xls"))
                        {
                            xls += ".xls";
                        }

                        if (!File.Exists(xls))
                        {
                            MessageBox.Show("轮毂参数表不存在\n" + xls);
                            return;
                        }
                    }

                    GridView_Technology.Invoke(new MethodInvoker(delegate
                    {
                        t_npoi.Import(xls, GridView_Technology);

                    }));

                    DataSet dataSet = new System.Data.DataSet();
                    dataSet.Tables.Add(t_npoi.ExcelToTable(xls));
                    if (dataSet.Tables.Count !=0)
                    {
                        
                        dataSet.Tables[0].TableName = "轮毂参数";
                        //先清除现有数据
                        SQlFun m_sqlfun = new SQlFun();
                        m_sqlfun.connection(connection);
                        
                            m_sqlfun.Sql_open();
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {
                            m_sqlfun.Sql_cleartable("轮毂参数");
                            //加载新数据
                            t_npoi.SqlbulkcopyInsert(connection, dataSet);
                            m_sqlfun.conn.Close();
                            if(OnImported != null)
                            {
                                OnImported();
                            }
                            return;
                        }
                        else
                            MessageBoxEX.Show(res.GetString("dbNotOpen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


                    }
                    else
                    {
                        
                        MessageBoxEX.Show(res.GetString("lacktable"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        
                        return;
                    }
                       


                });
            }
            catch
            {
                MessageBoxEX.Show(res.GetString("fileopenClose"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
               
            }
            
        }

        private void UpdateHeaderLanguage()
        {
            int langIndex = -1;
            switch (Properties.Settings.Default.DefaultLanguage)
            {
                case "en":
                    langIndex = 0;
                    break;
                case "fr":
                    langIndex = 1;
                    break;
                case "es":
                    langIndex = 2;
                    break;
            }

            for (int i = 0; i < GridView_Technology.Columns.Count; ++i)
            {
                if (langIndex >= 0 && HeaderLangMap.ContainsKey(GridView_Technology.Columns[i].HeaderText))
                {
                    if (langIndex == 1)
                    {
                        GridView_Technology.Columns[i].HeaderText = HeaderLangMap[GridView_Technology.Columns[i].HeaderText][langIndex] + "\n" + GridView_Technology.Columns[i].HeaderText;
                    }
                    else
                    {
                        GridView_Technology.Columns[i].HeaderText = HeaderLangMap[GridView_Technology.Columns[i].HeaderText][langIndex];
                    }
                }
            }
        }

        //当前数据库的配方显示
        private void Btn_ExportExc_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
             Task t_task = Task.Factory.StartNew(() => {
               
                GridView_Technology.Invoke(new MethodInvoker(delegate
                {
                    SQlFun t_sql = new SQlFun();
                    
                    t_sql.connection(connection);
                    
                        t_sql.Sql_open();
                    if (t_sql.conn.State == ConnectionState.Open)
                    {
                        t_sql.disp_datagridview("轮毂参数", GridView_Technology, this.Width);
                        UpdateHeaderLanguage();
                        t_sql.conn.Close();
                        Btn_LoadTechnology.Enabled = true;
                    }
                    else
                        MessageBoxEX.Show(res.GetString("dbNotOpen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


                }));

              

            });
          
        }
//当前的配方下载到当地
        private void Btn_LoadTechnology_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Task t_task = Task.Factory.StartNew(() => {

                GridView_Technology.Invoke(new MethodInvoker(delegate
                {
                    SQlFun t_sql = new SQlFun();
                    if (GridView_Technology.Rows.Count > 0)
                    {
                        t_sql.Export_DataGridViewtoExcel(GridView_Technology);
                        Btn_LoadTechnology.Enabled = false;
                    }
                    else
                        MessageBoxEX.Show(res.GetString("tableisempty"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                }));



            });


            
        }

        private void zoomTrackBarControl1_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void FormClose(object sender, FormClosedEventArgs e)
        {
           
        }
    }
}
