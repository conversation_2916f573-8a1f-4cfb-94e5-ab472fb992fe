using System;
using System.Collections.Generic;
using System.Drawing;

namespace CameraTechVerify.Interfaces
{
    /// <summary>
    /// 通用视觉算法引擎接口
    /// 支持 Halcon、OpenCV、自定义算法等多种实现
    /// </summary>
    public interface IVisionAlgorithmEngine : IDisposable
    {
        #region 基础属性
        
        /// <summary>
        /// 算法引擎名称
        /// </summary>
        string EngineName { get; }
        
        /// <summary>
        /// 算法引擎版本
        /// </summary>
        string EngineVersion { get; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        #endregion

        #region 初始化与配置
        
        /// <summary>
        /// 初始化算法引擎
        /// </summary>
        /// <param name="config">配置参数</param>
        /// <returns>是否成功</returns>
        bool Initialize(Dictionary<string, object> config = null);
        
        /// <summary>
        /// 设置算法参数
        /// </summary>
        /// <param name="key">参数名</param>
        /// <param name="value">参数值</param>
        void SetParameter(string key, object value);
        
        /// <summary>
        /// 获取算法参数
        /// </summary>
        /// <param name="key">参数名</param>
        /// <returns>参数值</returns>
        T GetParameter<T>(string key);
        
        #endregion

        #region 图像处理基础功能
        
        /// <summary>
        /// 从文件加载图像
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>图像对象</returns>
        IVisionImage LoadImage(string filePath);
        
        /// <summary>
        /// 从内存创建图像
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <returns>图像对象</returns>
        IVisionImage CreateImage(Bitmap bitmap);
        
        /// <summary>
        /// 从原始数据创建图像
        /// </summary>
        /// <param name="data">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="pixelFormat">像素格式</param>
        /// <returns>图像对象</returns>
        IVisionImage CreateImage(IntPtr data, int width, int height, VisionPixelFormat pixelFormat);
        
        /// <summary>
        /// 保存图像到文件
        /// </summary>
        /// <param name="image">图像对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">图像格式</param>
        /// <returns>是否成功</returns>
        bool SaveImage(IVisionImage image, string filePath, VisionImageFormat format);
        
        #endregion

        #region 图像预处理
        
        /// <summary>
        /// 图像阈值分割
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="minThreshold">最小阈值</param>
        /// <param name="maxThreshold">最大阈值</param>
        /// <returns>分割后的区域</returns>
        IVisionRegion Threshold(IVisionImage image, double minThreshold, double maxThreshold);
        
        /// <summary>
        /// 边缘检测
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="method">检测方法</param>
        /// <param name="parameters">算法参数</param>
        /// <returns>边缘轮廓</returns>
        IVisionContour EdgeDetection(IVisionImage image, EdgeDetectionMethod method, Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// 形态学操作
        /// </summary>
        /// <param name="region">输入区域</param>
        /// <param name="operation">操作类型</param>
        /// <param name="structuringElement">结构元素</param>
        /// <returns>处理后的区域</returns>
        IVisionRegion MorphologyOperation(IVisionRegion region, MorphologyOperation operation, StructuringElement structuringElement);
        
        /// <summary>
        /// 图像滤波
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="filterType">滤波类型</param>
        /// <param name="parameters">滤波参数</param>
        /// <returns>滤波后的图像</returns>
        IVisionImage Filter(IVisionImage image, FilterType filterType, Dictionary<string, object> parameters = null);
        
        #endregion

        #region 模板匹配与识别
        
        /// <summary>
        /// 创建形状模板
        /// </summary>
        /// <param name="templateImage">模板图像</param>
        /// <param name="templateRegion">模板区域</param>
        /// <param name="parameters">创建参数</param>
        /// <returns>模板ID</returns>
        string CreateShapeModel(IVisionImage templateImage, IVisionRegion templateRegion = null, Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// 查找形状模板
        /// </summary>
        /// <param name="image">搜索图像</param>
        /// <param name="modelId">模板ID</param>
        /// <param name="parameters">搜索参数</param>
        /// <returns>匹配结果</returns>
        List<MatchResult> FindShapeModel(IVisionImage image, string modelId, Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// 删除形状模板
        /// </summary>
        /// <param name="modelId">模板ID</param>
        void ClearShapeModel(string modelId);
        
        #endregion

        #region 测量功能
        
        /// <summary>
        /// 创建测量工具
        /// </summary>
        /// <param name="measureType">测量类型</param>
        /// <param name="parameters">测量参数</param>
        /// <returns>测量工具ID</returns>
        string CreateMeasureTool(MeasureType measureType, Dictionary<string, object> parameters);
        
        /// <summary>
        /// 执行测量
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="toolId">测量工具ID</param>
        /// <returns>测量结果</returns>
        MeasureResult ExecuteMeasure(IVisionImage image, string toolId);
        
        /// <summary>
        /// 圆形拟合
        /// </summary>
        /// <param name="contour">输入轮廓</param>
        /// <param name="method">拟合方法</param>
        /// <returns>圆形参数</returns>
        CircleResult FitCircle(IVisionContour contour, FittingMethod method = FittingMethod.LeastSquares);
        
        /// <summary>
        /// 直线拟合
        /// </summary>
        /// <param name="contour">输入轮廓</param>
        /// <param name="method">拟合方法</param>
        /// <returns>直线参数</returns>
        LineResult FitLine(IVisionContour contour, FittingMethod method = FittingMethod.LeastSquares);
        
        /// <summary>
        /// 计算两点间距离
        /// </summary>
        /// <param name="point1">点1</param>
        /// <param name="point2">点2</param>
        /// <returns>距离</returns>
        double CalculateDistance(VisionPoint point1, VisionPoint point2);
        
        /// <summary>
        /// 计算角度
        /// </summary>
        /// <param name="center">中心点</param>
        /// <param name="point1">点1</param>
        /// <param name="point2">点2</param>
        /// <returns>角度（弧度）</returns>
        double CalculateAngle(VisionPoint center, VisionPoint point1, VisionPoint point2);
        
        #endregion

        #region 3D 处理功能
        
        /// <summary>
        /// 创建3D对象模型
        /// </summary>
        /// <param name="pointCloud">点云数据</param>
        /// <returns>3D模型ID</returns>
        string Create3DModel(VisionPointCloud pointCloud);
        
        /// <summary>
        /// 3D模型可视化
        /// </summary>
        /// <param name="modelId">模型ID</param>
        /// <param name="pose">姿态参数</param>
        /// <param name="parameters">可视化参数</param>
        /// <returns>可视化结果</returns>
        Visualization3DResult Visualize3D(string modelId, Pose3D pose, Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// 3D平面切割
        /// </summary>
        /// <param name="modelId">模型ID</param>
        /// <param name="planePose">平面姿态</param>
        /// <returns>切割后的模型ID</returns>
        string IntersectPlane3D(string modelId, Pose3D planePose);
        
        #endregion

        #region 显示与标注
        
        /// <summary>
        /// 在窗口显示图像
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        /// <param name="image">图像对象</param>
        void DisplayImage(IntPtr windowHandle, IVisionImage image);
        
        /// <summary>
        /// 显示区域
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        /// <param name="region">区域对象</param>
        /// <param name="color">显示颜色</param>
        void DisplayRegion(IntPtr windowHandle, IVisionRegion region, string color = "red");
        
        /// <summary>
        /// 显示轮廓
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        /// <param name="contour">轮廓对象</param>
        /// <param name="color">显示颜色</param>
        void DisplayContour(IntPtr windowHandle, IVisionContour contour, string color = "green");
        
        /// <summary>
        /// 显示文本
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        /// <param name="text">文本内容</param>
        /// <param name="row">行位置</param>
        /// <param name="column">列位置</param>
        /// <param name="color">文本颜色</param>
        void DisplayText(IntPtr windowHandle, string text, double row, double column, string color = "green");
        
        /// <summary>
        /// 清空窗口
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        void ClearWindow(IntPtr windowHandle);
        
        #endregion

        #region 事件
        
        /// <summary>
        /// 算法执行进度事件
        /// </summary>
        event EventHandler<AlgorithmProgressEventArgs> ProgressChanged;
        
        /// <summary>
        /// 算法执行完成事件
        /// </summary>
        event EventHandler<AlgorithmCompletedEventArgs> AlgorithmCompleted;
        
        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<AlgorithmErrorEventArgs> ErrorOccurred;
        
        #endregion
    }
}
