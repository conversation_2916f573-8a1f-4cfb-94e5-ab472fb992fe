﻿
namespace BingSolhalcon.UI
{
    partial class CircleCaliper
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CircleCaliper));
            this.hWindowControl1 = new HalconDotNet.HWindowControl();
            this.btn_drawcircle = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cennum_numericUpDown = new System.Windows.Forms.ComboBox();
            this.cenmeasurethreshold_textBox = new System.Windows.Forms.ComboBox();
            this.cenlineindex_cboBox = new System.Windows.Forms.ComboBox();
            this.cenpolarity_cboBox = new System.Windows.Forms.ComboBox();
            this.cenminscores_textBox = new System.Windows.Forms.TextBox();
            this.censigmal_textBox = new System.Windows.Forms.TextBox();
            this.cenmeasurewidth_textBox = new System.Windows.Forms.TextBox();
            this.censtdradius_textBox = new System.Windows.Forms.TextBox();
            this.cenmeasurelength_textBox = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.btn_mtrsave = new System.Windows.Forms.Button();
            this.btn_test = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.boltnum_numericUpDown = new System.Windows.Forms.ComboBox();
            this.boltthreshold_textBox = new System.Windows.Forms.ComboBox();
            this.boltstdradius8_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius7_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius6_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius5_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius4_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius3_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius2_textBox = new System.Windows.Forms.TextBox();
            this.boltlineindex_cboBox = new System.Windows.Forms.ComboBox();
            this.boltpolarity_cboBox = new System.Windows.Forms.ComboBox();
            this.label22 = new System.Windows.Forms.Label();
            this.boltminscores_textBox = new System.Windows.Forms.TextBox();
            this.boltsigmal_textBox = new System.Windows.Forms.TextBox();
            this.boltmeasurewidth_textBox = new System.Windows.Forms.TextBox();
            this.boltstdradius1_textBox = new System.Windows.Forms.TextBox();
            this.boltmeasurelength_textBox = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.hatlineindex_cboBox = new System.Windows.Forms.ComboBox();
            this.hatpolarity_cboBox = new System.Windows.Forms.ComboBox();
            this.hatminscores_textBox = new System.Windows.Forms.TextBox();
            this.hatsigmal_textBox = new System.Windows.Forms.TextBox();
            this.hatmeasurewidth_textBox = new System.Windows.Forms.TextBox();
            this.hatstdradius_textBox = new System.Windows.Forms.TextBox();
            this.hatmeasurelength_textBox = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.comboBox_selecttype = new System.Windows.Forms.ComboBox();
            this.label19 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.hatnum_numericUpDown = new System.Windows.Forms.ComboBox();
            this.hatmeasurethreshold_textBox = new System.Windows.Forms.ComboBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.comboBox_wheellist = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btn_readimage = new System.Windows.Forms.Button();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel4 = new System.Windows.Forms.Panel();
            this.panel5 = new System.Windows.Forms.Panel();
            this.panel6 = new System.Windows.Forms.Panel();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel3.SuspendLayout();
            this.panel4.SuspendLayout();
            this.panel5.SuspendLayout();
            this.panel6.SuspendLayout();
            this.SuspendLayout();
            // 
            // hWindowControl1
            // 
            resources.ApplyResources(this.hWindowControl1, "hWindowControl1");
            this.hWindowControl1.BackColor = System.Drawing.Color.Black;
            this.hWindowControl1.BorderColor = System.Drawing.Color.Black;
            this.hWindowControl1.ImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hWindowControl1.Name = "hWindowControl1";
            this.hWindowControl1.WindowSize = new System.Drawing.Size(551, 653);
            this.hWindowControl1.HMouseDown += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseDown);
            this.hWindowControl1.HMouseUp += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseUp);
            this.hWindowControl1.HMouseWheel += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseWheel);
            // 
            // btn_drawcircle
            // 
            resources.ApplyResources(this.btn_drawcircle, "btn_drawcircle");
            this.btn_drawcircle.Name = "btn_drawcircle";
            this.btn_drawcircle.UseVisualStyleBackColor = true;
            this.btn_drawcircle.Click += new System.EventHandler(this.btn_drawcircle_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.cennum_numericUpDown);
            this.groupBox2.Controls.Add(this.cenmeasurethreshold_textBox);
            this.groupBox2.Controls.Add(this.cenlineindex_cboBox);
            this.groupBox2.Controls.Add(this.cenpolarity_cboBox);
            this.groupBox2.Controls.Add(this.cenminscores_textBox);
            this.groupBox2.Controls.Add(this.censigmal_textBox);
            this.groupBox2.Controls.Add(this.cenmeasurewidth_textBox);
            this.groupBox2.Controls.Add(this.censtdradius_textBox);
            this.groupBox2.Controls.Add(this.cenmeasurelength_textBox);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label5);
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // cennum_numericUpDown
            // 
            this.cennum_numericUpDown.FormattingEnabled = true;
            this.cennum_numericUpDown.Items.AddRange(new object[] {
            resources.GetString("cennum_numericUpDown.Items"),
            resources.GetString("cennum_numericUpDown.Items1"),
            resources.GetString("cennum_numericUpDown.Items2"),
            resources.GetString("cennum_numericUpDown.Items3"),
            resources.GetString("cennum_numericUpDown.Items4"),
            resources.GetString("cennum_numericUpDown.Items5")});
            resources.ApplyResources(this.cennum_numericUpDown, "cennum_numericUpDown");
            this.cennum_numericUpDown.Name = "cennum_numericUpDown";
            // 
            // cenmeasurethreshold_textBox
            // 
            this.cenmeasurethreshold_textBox.FormattingEnabled = true;
            this.cenmeasurethreshold_textBox.Items.AddRange(new object[] {
            resources.GetString("cenmeasurethreshold_textBox.Items"),
            resources.GetString("cenmeasurethreshold_textBox.Items1"),
            resources.GetString("cenmeasurethreshold_textBox.Items2"),
            resources.GetString("cenmeasurethreshold_textBox.Items3"),
            resources.GetString("cenmeasurethreshold_textBox.Items4")});
            resources.ApplyResources(this.cenmeasurethreshold_textBox, "cenmeasurethreshold_textBox");
            this.cenmeasurethreshold_textBox.Name = "cenmeasurethreshold_textBox";
            // 
            // cenlineindex_cboBox
            // 
            resources.ApplyResources(this.cenlineindex_cboBox, "cenlineindex_cboBox");
            this.cenlineindex_cboBox.FormattingEnabled = true;
            this.cenlineindex_cboBox.Items.AddRange(new object[] {
            resources.GetString("cenlineindex_cboBox.Items"),
            resources.GetString("cenlineindex_cboBox.Items1"),
            resources.GetString("cenlineindex_cboBox.Items2"),
            resources.GetString("cenlineindex_cboBox.Items3"),
            resources.GetString("cenlineindex_cboBox.Items4")});
            this.cenlineindex_cboBox.Name = "cenlineindex_cboBox";
            // 
            // cenpolarity_cboBox
            // 
            resources.ApplyResources(this.cenpolarity_cboBox, "cenpolarity_cboBox");
            this.cenpolarity_cboBox.FormattingEnabled = true;
            this.cenpolarity_cboBox.Items.AddRange(new object[] {
            resources.GetString("cenpolarity_cboBox.Items"),
            resources.GetString("cenpolarity_cboBox.Items1")});
            this.cenpolarity_cboBox.Name = "cenpolarity_cboBox";
            // 
            // cenminscores_textBox
            // 
            resources.ApplyResources(this.cenminscores_textBox, "cenminscores_textBox");
            this.cenminscores_textBox.Name = "cenminscores_textBox";
            // 
            // censigmal_textBox
            // 
            resources.ApplyResources(this.censigmal_textBox, "censigmal_textBox");
            this.censigmal_textBox.Name = "censigmal_textBox";
            // 
            // cenmeasurewidth_textBox
            // 
            resources.ApplyResources(this.cenmeasurewidth_textBox, "cenmeasurewidth_textBox");
            this.cenmeasurewidth_textBox.Name = "cenmeasurewidth_textBox";
            // 
            // censtdradius_textBox
            // 
            resources.ApplyResources(this.censtdradius_textBox, "censtdradius_textBox");
            this.censtdradius_textBox.Name = "censtdradius_textBox";
            this.censtdradius_textBox.TextChanged += new System.EventHandler(this.censtdradius_textBox_TextChanged);
            this.censtdradius_textBox.Validated += new System.EventHandler(this.censtdradius_textBox_Validated);
            // 
            // cenmeasurelength_textBox
            // 
            resources.ApplyResources(this.cenmeasurelength_textBox, "cenmeasurelength_textBox");
            this.cenmeasurelength_textBox.Name = "cenmeasurelength_textBox";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // label9
            // 
            resources.ApplyResources(this.label9, "label9");
            this.label9.Name = "label9";
            // 
            // label18
            // 
            resources.ApplyResources(this.label18, "label18");
            this.label18.Name = "label18";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // btn_mtrsave
            // 
            resources.ApplyResources(this.btn_mtrsave, "btn_mtrsave");
            this.btn_mtrsave.Name = "btn_mtrsave";
            this.btn_mtrsave.UseVisualStyleBackColor = true;
            this.btn_mtrsave.Click += new System.EventHandler(this.btn_centrehole_Click);
            // 
            // btn_test
            // 
            resources.ApplyResources(this.btn_test, "btn_test");
            this.btn_test.Name = "btn_test";
            this.btn_test.UseVisualStyleBackColor = true;
            this.btn_test.Click += new System.EventHandler(this.btn_test_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.boltnum_numericUpDown);
            this.groupBox3.Controls.Add(this.boltthreshold_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius8_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius7_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius6_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius5_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius4_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius3_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius2_textBox);
            this.groupBox3.Controls.Add(this.boltlineindex_cboBox);
            this.groupBox3.Controls.Add(this.boltpolarity_cboBox);
            this.groupBox3.Controls.Add(this.label22);
            this.groupBox3.Controls.Add(this.boltminscores_textBox);
            this.groupBox3.Controls.Add(this.boltsigmal_textBox);
            this.groupBox3.Controls.Add(this.boltmeasurewidth_textBox);
            this.groupBox3.Controls.Add(this.boltstdradius1_textBox);
            this.groupBox3.Controls.Add(this.boltmeasurelength_textBox);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.label17);
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // boltnum_numericUpDown
            // 
            this.boltnum_numericUpDown.FormattingEnabled = true;
            this.boltnum_numericUpDown.Items.AddRange(new object[] {
            resources.GetString("boltnum_numericUpDown.Items"),
            resources.GetString("boltnum_numericUpDown.Items1"),
            resources.GetString("boltnum_numericUpDown.Items2"),
            resources.GetString("boltnum_numericUpDown.Items3"),
            resources.GetString("boltnum_numericUpDown.Items4"),
            resources.GetString("boltnum_numericUpDown.Items5")});
            resources.ApplyResources(this.boltnum_numericUpDown, "boltnum_numericUpDown");
            this.boltnum_numericUpDown.Name = "boltnum_numericUpDown";
            // 
            // boltthreshold_textBox
            // 
            this.boltthreshold_textBox.FormattingEnabled = true;
            this.boltthreshold_textBox.Items.AddRange(new object[] {
            resources.GetString("boltthreshold_textBox.Items"),
            resources.GetString("boltthreshold_textBox.Items1"),
            resources.GetString("boltthreshold_textBox.Items2"),
            resources.GetString("boltthreshold_textBox.Items3"),
            resources.GetString("boltthreshold_textBox.Items4")});
            resources.ApplyResources(this.boltthreshold_textBox, "boltthreshold_textBox");
            this.boltthreshold_textBox.Name = "boltthreshold_textBox";
            // 
            // boltstdradius8_textBox
            // 
            resources.ApplyResources(this.boltstdradius8_textBox, "boltstdradius8_textBox");
            this.boltstdradius8_textBox.Name = "boltstdradius8_textBox";
            // 
            // boltstdradius7_textBox
            // 
            resources.ApplyResources(this.boltstdradius7_textBox, "boltstdradius7_textBox");
            this.boltstdradius7_textBox.Name = "boltstdradius7_textBox";
            // 
            // boltstdradius6_textBox
            // 
            resources.ApplyResources(this.boltstdradius6_textBox, "boltstdradius6_textBox");
            this.boltstdradius6_textBox.Name = "boltstdradius6_textBox";
            // 
            // boltstdradius5_textBox
            // 
            resources.ApplyResources(this.boltstdradius5_textBox, "boltstdradius5_textBox");
            this.boltstdradius5_textBox.Name = "boltstdradius5_textBox";
            // 
            // boltstdradius4_textBox
            // 
            resources.ApplyResources(this.boltstdradius4_textBox, "boltstdradius4_textBox");
            this.boltstdradius4_textBox.Name = "boltstdradius4_textBox";
            // 
            // boltstdradius3_textBox
            // 
            resources.ApplyResources(this.boltstdradius3_textBox, "boltstdradius3_textBox");
            this.boltstdradius3_textBox.Name = "boltstdradius3_textBox";
            // 
            // boltstdradius2_textBox
            // 
            resources.ApplyResources(this.boltstdradius2_textBox, "boltstdradius2_textBox");
            this.boltstdradius2_textBox.Name = "boltstdradius2_textBox";
            // 
            // boltlineindex_cboBox
            // 
            resources.ApplyResources(this.boltlineindex_cboBox, "boltlineindex_cboBox");
            this.boltlineindex_cboBox.FormattingEnabled = true;
            this.boltlineindex_cboBox.Items.AddRange(new object[] {
            resources.GetString("boltlineindex_cboBox.Items"),
            resources.GetString("boltlineindex_cboBox.Items1"),
            resources.GetString("boltlineindex_cboBox.Items2"),
            resources.GetString("boltlineindex_cboBox.Items3"),
            resources.GetString("boltlineindex_cboBox.Items4")});
            this.boltlineindex_cboBox.Name = "boltlineindex_cboBox";
            // 
            // boltpolarity_cboBox
            // 
            resources.ApplyResources(this.boltpolarity_cboBox, "boltpolarity_cboBox");
            this.boltpolarity_cboBox.FormattingEnabled = true;
            this.boltpolarity_cboBox.Items.AddRange(new object[] {
            resources.GetString("boltpolarity_cboBox.Items"),
            resources.GetString("boltpolarity_cboBox.Items1")});
            this.boltpolarity_cboBox.Name = "boltpolarity_cboBox";
            // 
            // label22
            // 
            resources.ApplyResources(this.label22, "label22");
            this.label22.Name = "label22";
            // 
            // boltminscores_textBox
            // 
            resources.ApplyResources(this.boltminscores_textBox, "boltminscores_textBox");
            this.boltminscores_textBox.Name = "boltminscores_textBox";
            // 
            // boltsigmal_textBox
            // 
            resources.ApplyResources(this.boltsigmal_textBox, "boltsigmal_textBox");
            this.boltsigmal_textBox.Name = "boltsigmal_textBox";
            // 
            // boltmeasurewidth_textBox
            // 
            resources.ApplyResources(this.boltmeasurewidth_textBox, "boltmeasurewidth_textBox");
            this.boltmeasurewidth_textBox.Name = "boltmeasurewidth_textBox";
            // 
            // boltstdradius1_textBox
            // 
            resources.ApplyResources(this.boltstdradius1_textBox, "boltstdradius1_textBox");
            this.boltstdradius1_textBox.Name = "boltstdradius1_textBox";
            this.boltstdradius1_textBox.Validated += new System.EventHandler(this.boltstdradius1_textBox_Validated);
            // 
            // boltmeasurelength_textBox
            // 
            resources.ApplyResources(this.boltmeasurelength_textBox, "boltmeasurelength_textBox");
            this.boltmeasurelength_textBox.Name = "boltmeasurelength_textBox";
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // label13
            // 
            resources.ApplyResources(this.label13, "label13");
            this.label13.Name = "label13";
            // 
            // label14
            // 
            resources.ApplyResources(this.label14, "label14");
            this.label14.Name = "label14";
            // 
            // label15
            // 
            resources.ApplyResources(this.label15, "label15");
            this.label15.Name = "label15";
            // 
            // label20
            // 
            resources.ApplyResources(this.label20, "label20");
            this.label20.Name = "label20";
            // 
            // label16
            // 
            resources.ApplyResources(this.label16, "label16");
            this.label16.Name = "label16";
            // 
            // label17
            // 
            resources.ApplyResources(this.label17, "label17");
            this.label17.Name = "label17";
            // 
            // hatlineindex_cboBox
            // 
            resources.ApplyResources(this.hatlineindex_cboBox, "hatlineindex_cboBox");
            this.hatlineindex_cboBox.FormattingEnabled = true;
            this.hatlineindex_cboBox.Items.AddRange(new object[] {
            resources.GetString("hatlineindex_cboBox.Items"),
            resources.GetString("hatlineindex_cboBox.Items1"),
            resources.GetString("hatlineindex_cboBox.Items2"),
            resources.GetString("hatlineindex_cboBox.Items3"),
            resources.GetString("hatlineindex_cboBox.Items4")});
            this.hatlineindex_cboBox.Name = "hatlineindex_cboBox";
            // 
            // hatpolarity_cboBox
            // 
            resources.ApplyResources(this.hatpolarity_cboBox, "hatpolarity_cboBox");
            this.hatpolarity_cboBox.FormattingEnabled = true;
            this.hatpolarity_cboBox.Items.AddRange(new object[] {
            resources.GetString("hatpolarity_cboBox.Items"),
            resources.GetString("hatpolarity_cboBox.Items1")});
            this.hatpolarity_cboBox.Name = "hatpolarity_cboBox";
            // 
            // hatminscores_textBox
            // 
            resources.ApplyResources(this.hatminscores_textBox, "hatminscores_textBox");
            this.hatminscores_textBox.Name = "hatminscores_textBox";
            // 
            // hatsigmal_textBox
            // 
            resources.ApplyResources(this.hatsigmal_textBox, "hatsigmal_textBox");
            this.hatsigmal_textBox.Name = "hatsigmal_textBox";
            // 
            // hatmeasurewidth_textBox
            // 
            resources.ApplyResources(this.hatmeasurewidth_textBox, "hatmeasurewidth_textBox");
            this.hatmeasurewidth_textBox.Name = "hatmeasurewidth_textBox";
            // 
            // hatstdradius_textBox
            // 
            resources.ApplyResources(this.hatstdradius_textBox, "hatstdradius_textBox");
            this.hatstdradius_textBox.Name = "hatstdradius_textBox";
            this.hatstdradius_textBox.Validated += new System.EventHandler(this.hatstdradius_textBox_Validated);
            // 
            // hatmeasurelength_textBox
            // 
            resources.ApplyResources(this.hatmeasurelength_textBox, "hatmeasurelength_textBox");
            this.hatmeasurelength_textBox.Name = "hatmeasurelength_textBox";
            // 
            // label23
            // 
            resources.ApplyResources(this.label23, "label23");
            this.label23.Name = "label23";
            // 
            // label24
            // 
            resources.ApplyResources(this.label24, "label24");
            this.label24.Name = "label24";
            // 
            // label25
            // 
            resources.ApplyResources(this.label25, "label25");
            this.label25.Name = "label25";
            // 
            // label26
            // 
            resources.ApplyResources(this.label26, "label26");
            this.label26.Name = "label26";
            // 
            // label27
            // 
            resources.ApplyResources(this.label27, "label27");
            this.label27.Name = "label27";
            // 
            // label28
            // 
            resources.ApplyResources(this.label28, "label28");
            this.label28.Name = "label28";
            // 
            // label29
            // 
            resources.ApplyResources(this.label29, "label29");
            this.label29.Name = "label29";
            // 
            // label30
            // 
            resources.ApplyResources(this.label30, "label30");
            this.label30.Name = "label30";
            // 
            // label31
            // 
            resources.ApplyResources(this.label31, "label31");
            this.label31.Name = "label31";
            // 
            // comboBox_selecttype
            // 
            this.comboBox_selecttype.AutoCompleteCustomSource.AddRange(new string[] {
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource"),
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource1"),
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource2")});
            resources.ApplyResources(this.comboBox_selecttype, "comboBox_selecttype");
            this.comboBox_selecttype.FormattingEnabled = true;
            this.comboBox_selecttype.Items.AddRange(new object[] {
            resources.GetString("comboBox_selecttype.Items"),
            resources.GetString("comboBox_selecttype.Items1"),
            resources.GetString("comboBox_selecttype.Items2"),
            resources.GetString("comboBox_selecttype.Items3")});
            this.comboBox_selecttype.Name = "comboBox_selecttype";
            this.comboBox_selecttype.SelectedIndexChanged += new System.EventHandler(this.comboBox_selecttype_SelectedIndexChanged);
            // 
            // label19
            // 
            resources.ApplyResources(this.label19, "label19");
            this.label19.Name = "label19";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.hatnum_numericUpDown);
            this.groupBox5.Controls.Add(this.hatmeasurethreshold_textBox);
            this.groupBox5.Controls.Add(this.label30);
            this.groupBox5.Controls.Add(this.hatlineindex_cboBox);
            this.groupBox5.Controls.Add(this.label29);
            this.groupBox5.Controls.Add(this.label28);
            this.groupBox5.Controls.Add(this.hatpolarity_cboBox);
            this.groupBox5.Controls.Add(this.label31);
            this.groupBox5.Controls.Add(this.label27);
            this.groupBox5.Controls.Add(this.label26);
            this.groupBox5.Controls.Add(this.label25);
            this.groupBox5.Controls.Add(this.label24);
            this.groupBox5.Controls.Add(this.label23);
            this.groupBox5.Controls.Add(this.hatminscores_textBox);
            this.groupBox5.Controls.Add(this.hatmeasurelength_textBox);
            this.groupBox5.Controls.Add(this.hatstdradius_textBox);
            this.groupBox5.Controls.Add(this.hatsigmal_textBox);
            this.groupBox5.Controls.Add(this.hatmeasurewidth_textBox);
            resources.ApplyResources(this.groupBox5, "groupBox5");
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.TabStop = false;
            // 
            // hatnum_numericUpDown
            // 
            this.hatnum_numericUpDown.FormattingEnabled = true;
            this.hatnum_numericUpDown.Items.AddRange(new object[] {
            resources.GetString("hatnum_numericUpDown.Items"),
            resources.GetString("hatnum_numericUpDown.Items1"),
            resources.GetString("hatnum_numericUpDown.Items2"),
            resources.GetString("hatnum_numericUpDown.Items3"),
            resources.GetString("hatnum_numericUpDown.Items4"),
            resources.GetString("hatnum_numericUpDown.Items5")});
            resources.ApplyResources(this.hatnum_numericUpDown, "hatnum_numericUpDown");
            this.hatnum_numericUpDown.Name = "hatnum_numericUpDown";
            // 
            // hatmeasurethreshold_textBox
            // 
            this.hatmeasurethreshold_textBox.FormattingEnabled = true;
            this.hatmeasurethreshold_textBox.Items.AddRange(new object[] {
            resources.GetString("hatmeasurethreshold_textBox.Items"),
            resources.GetString("hatmeasurethreshold_textBox.Items1"),
            resources.GetString("hatmeasurethreshold_textBox.Items2"),
            resources.GetString("hatmeasurethreshold_textBox.Items3"),
            resources.GetString("hatmeasurethreshold_textBox.Items4")});
            resources.ApplyResources(this.hatmeasurethreshold_textBox, "hatmeasurethreshold_textBox");
            this.hatmeasurethreshold_textBox.Name = "hatmeasurethreshold_textBox";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.comboBox_wheellist);
            this.groupBox4.Controls.Add(this.btn_drawcircle);
            this.groupBox4.Controls.Add(this.label3);
            this.groupBox4.Controls.Add(this.btn_mtrsave);
            this.groupBox4.Controls.Add(this.label19);
            this.groupBox4.Controls.Add(this.comboBox_selecttype);
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            this.groupBox4.Enter += new System.EventHandler(this.groupBox4_Enter);
            // 
            // comboBox_wheellist
            // 
            resources.ApplyResources(this.comboBox_wheellist, "comboBox_wheellist");
            this.comboBox_wheellist.FormattingEnabled = true;
            this.comboBox_wheellist.Name = "comboBox_wheellist";
            this.comboBox_wheellist.SelectedIndexChanged += new System.EventHandler(this.comboBox_wheellist_SelectedIndexChanged);
            this.comboBox_wheellist.TextUpdate += new System.EventHandler(this.comboBox_wheellistUpdate);
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // btn_readimage
            // 
            resources.ApplyResources(this.btn_readimage, "btn_readimage");
            this.btn_readimage.Name = "btn_readimage";
            this.btn_readimage.UseVisualStyleBackColor = true;
            this.btn_readimage.Click += new System.EventHandler(this.btn_readimage_Click);
            // 
            // tableLayoutPanel1
            // 
            resources.ApplyResources(this.tableLayoutPanel1, "tableLayoutPanel1");
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel2, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel3, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.panel4, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.panel5, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.panel6, 1, 4);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.hWindowControl1);
            resources.ApplyResources(this.panel1, "panel1");
            this.panel1.Name = "panel1";
            this.tableLayoutPanel1.SetRowSpan(this.panel1, 5);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.groupBox4);
            resources.ApplyResources(this.panel2, "panel2");
            this.panel2.Name = "panel2";
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.groupBox2);
            resources.ApplyResources(this.panel3, "panel3");
            this.panel3.Name = "panel3";
            // 
            // panel4
            // 
            this.panel4.Controls.Add(this.groupBox3);
            resources.ApplyResources(this.panel4, "panel4");
            this.panel4.Name = "panel4";
            // 
            // panel5
            // 
            this.panel5.Controls.Add(this.groupBox5);
            resources.ApplyResources(this.panel5, "panel5");
            this.panel5.Name = "panel5";
            // 
            // panel6
            // 
            this.panel6.Controls.Add(this.btn_readimage);
            this.panel6.Controls.Add(this.btn_test);
            resources.ApplyResources(this.panel6, "panel6");
            this.panel6.Name = "panel6";
            // 
            // CircleCaliper
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tableLayoutPanel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CircleCaliper";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.CircleCaliper_FormClosing);
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.CircleCaliperFormClosed);
            this.Load += new System.EventHandler(this.FormLoad);
            this.Resize += new System.EventHandler(this.Form1_Resize);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            this.panel4.ResumeLayout(false);
            this.panel5.ResumeLayout(false);
            this.panel6.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private HalconDotNet.HWindowControl hWindowControl1;
        private System.Windows.Forms.Button btn_drawcircle;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.ComboBox cenpolarity_cboBox;
        private System.Windows.Forms.TextBox censigmal_textBox;
        private System.Windows.Forms.TextBox cenmeasurewidth_textBox;
        private System.Windows.Forms.TextBox cenmeasurelength_textBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox cenlineindex_cboBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btn_mtrsave;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.ComboBox boltlineindex_cboBox;
        private System.Windows.Forms.ComboBox boltpolarity_cboBox;
        private System.Windows.Forms.TextBox boltsigmal_textBox;
        private System.Windows.Forms.TextBox boltmeasurewidth_textBox;
        private System.Windows.Forms.TextBox boltmeasurelength_textBox;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox cenminscores_textBox;
        private System.Windows.Forms.TextBox boltminscores_textBox;
        private System.Windows.Forms.TextBox censtdradius_textBox;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Button btn_test;
        private System.Windows.Forms.TextBox boltstdradius1_textBox;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.ComboBox hatlineindex_cboBox;
        private System.Windows.Forms.ComboBox hatpolarity_cboBox;
        private System.Windows.Forms.TextBox hatminscores_textBox;
        private System.Windows.Forms.TextBox hatsigmal_textBox;
        private System.Windows.Forms.TextBox hatmeasurewidth_textBox;
        private System.Windows.Forms.TextBox hatstdradius_textBox;
        private System.Windows.Forms.TextBox hatmeasurelength_textBox;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.ComboBox comboBox_selecttype;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.ComboBox comboBox_wheellist;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btn_readimage;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.Panel panel6;
        private System.Windows.Forms.TextBox boltstdradius6_textBox;
        private System.Windows.Forms.TextBox boltstdradius5_textBox;
        private System.Windows.Forms.TextBox boltstdradius4_textBox;
        private System.Windows.Forms.TextBox boltstdradius3_textBox;
        private System.Windows.Forms.TextBox boltstdradius2_textBox;
        private System.Windows.Forms.TextBox boltstdradius8_textBox;
        private System.Windows.Forms.TextBox boltstdradius7_textBox;
        private System.Windows.Forms.ComboBox cenmeasurethreshold_textBox;
        private System.Windows.Forms.ComboBox cennum_numericUpDown;
        private System.Windows.Forms.ComboBox boltnum_numericUpDown;
        private System.Windows.Forms.ComboBox boltthreshold_textBox;
        private System.Windows.Forms.ComboBox hatnum_numericUpDown;
        private System.Windows.Forms.ComboBox hatmeasurethreshold_textBox;
    }
}