using System.Windows;

namespace HalconAlgorithmManager.Dialogs
{
    public partial class NewVersionDialog : Window
    {
        public string VersionNumber { get; private set; }
        public string VersionType { get; private set; }
        public string BaseVersion { get; private set; }
        public string Description { get; private set; }

        public NewVersionDialog()
        {
            InitializeComponent();
        }

        private void Create_Click(object sender, RoutedEventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(VersionNumberTextBox.Text))
            {
                MessageBox.Show("请输入版本号！", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                VersionNumberTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("请输入版本描述！", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                DescriptionTextBox.Focus();
                return;
            }

            // 保存数据
            VersionNumber = VersionNumberTextBox.Text.Trim();
            VersionType = ((System.Windows.Controls.ComboBoxItem)VersionTypeComboBox.SelectedItem)?.Content?.ToString() ?? "";
            BaseVersion = ((System.Windows.Controls.ComboBoxItem)BaseVersionComboBox.SelectedItem)?.Content?.ToString() ?? "";
            Description = DescriptionTextBox.Text.Trim();

            // 确认创建
            var result = MessageBox.Show(
                $"确定要创建以下版本吗？\n\n" +
                $"版本号: {VersionNumber}\n" +
                $"版本类型: {VersionType}\n" +
                $"基于版本: {BaseVersion}",
                "创建确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = true;
                Close();
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
