# 📷 MVS 工业相机控制器使用说明

## 🎯 概述

我已经为您创建了一个功能完整的 MVS 工业相机控制窗体应用程序！这个应用程序提供了直观的图形界面来控制您的海康威视工业相机。

## ✨ 主要功能

### 🔌 设备管理
- **设备枚举**：自动发现网络中的相机设备
- **设备连接**：一键连接到选定的相机
- **设备信息显示**：显示相机型号、序列号等详细信息
- **连接状态监控**：实时显示连接状态

### 📸 图像采集
- **连续采集**：启动/停止连续图像采集
- **单次拍照**：手动触发单次拍照
- **图像显示**：实时显示采集的图像
- **图像保存**：将图像保存为 PNG/JPEG/BMP 格式

### ⚙️ 参数控制
- **曝光时间**：调整相机曝光时间（微秒）
- **增益控制**：设置相机增益值（dB）
- **帧率设置**：控制图像采集帧率
- **触发模式**：支持连续、软件触发、硬件触发模式
- **软件触发**：在软件触发模式下手动触发拍照

### 🔧 高级功能
- **参数配置**：保存/加载相机参数配置文件
- **SDK 分析**：分析您的 MVS SDK API 结构
- **实时统计**：显示图像计数、帧率等统计信息
- **操作日志**：记录所有操作和错误信息

## 🚀 快速开始

### 1. 启动应用程序
```bash
dotnet run --project CameraTechVerify/CameraTechVerify.csproj
```

### 2. 连接相机
1. 点击 **"刷新设备"** 按钮搜索可用相机
2. 在下拉列表中选择要连接的相机
3. 点击 **"连接"** 按钮建立连接

### 3. 开始采集
1. 连接成功后，点击 **"开始采集"** 开始连续采集
2. 或者点击 **"单次拍照"** 进行单次拍照
3. 图像将显示在中央区域

### 4. 调整参数
1. 在左侧参数面板中调整曝光时间、增益等参数
2. 点击对应的 **"设置"** 按钮应用参数
3. 参数变化会实时反映在右侧的当前参数显示中

## 🎛️ 界面说明

### 左侧控制面板
- **设备管理区域**：设备搜索、选择和连接控制
- **图像采集区域**：采集控制和图像保存功能
- **相机参数区域**：各种相机参数的设置控制
- **SDK 分析区域**：用于分析和测试 MVS SDK

### 中央显示区域
- **图像显示**：实时显示采集的图像
- **图像信息栏**：显示图像尺寸、格式等信息
- **缩放适应**：图像自动缩放以适应显示区域

### 右侧信息面板
- **设备信息**：显示当前连接设备的详细信息
- **当前参数**：显示相机的当前参数设置
- **统计信息**：显示图像计数、帧率等统计数据
- **操作日志**：记录所有操作和系统消息

### 底部状态栏
- **系统状态**：显示当前系统状态
- **SDK 状态**：显示 MVS SDK 的加载状态

## 🔧 功能详解

### 设备连接流程
1. **刷新设备** → 搜索网络中的相机
2. **选择设备** → 从下拉列表选择目标相机
3. **建立连接** → 点击连接按钮
4. **状态确认** → 查看连接状态指示器

### 图像采集模式
- **连续模式**：相机持续采集图像
- **软件触发**：通过软件命令触发单次采集
- **硬件触发**：通过外部硬件信号触发采集

### 参数设置技巧
- **曝光时间**：较长的曝光时间适合低光环境
- **增益设置**：增加增益可提高亮度但会增加噪声
- **帧率控制**：根据应用需求平衡帧率和图像质量

## 🛠️ SDK 分析功能

### API 分析工具
- **分析 MVS API**：分析您的 SDK 结构并在控制台输出
- **生成 API 报告**：生成详细的 API 分析报告到桌面
- **测试基本功能**：测试 SDK 的基本功能可用性

### 使用步骤
1. 点击 **"分析 MVS API"** 按钮
2. 查看控制台输出的 API 信息
3. 点击 **"生成 API 报告"** 生成详细报告
4. 根据报告信息完善相机控制器实现

## 📊 状态指示器

### 连接状态
- 🟢 **绿色**：相机已连接
- 🟡 **黄色**：正在连接
- 🔴 **红色**：连接失败
- ⚪ **灰色**：未连接

### 按钮状态
- **启用**：功能可用
- **禁用**：功能不可用（通常因为相机未连接）

## 🚨 故障排除

### 常见问题

1. **找不到设备**
   - 检查相机电源和网络连接
   - 确认相机 IP 地址设置正确
   - 检查防火墙设置

2. **连接失败**
   - 确认相机未被其他程序占用
   - 检查 MVS SDK 是否正确安装
   - 查看操作日志中的错误信息

3. **图像显示异常**
   - 检查相机参数设置
   - 确认图像格式支持
   - 查看错误日志

4. **参数设置无效**
   - 确认参数值在有效范围内
   - 检查相机是否支持该参数
   - 查看 API 分析报告

### 调试步骤
1. 查看右侧操作日志中的错误信息
2. 使用 SDK 分析功能检查 API 状态
3. 检查底部状态栏的 SDK 状态
4. 参考生成的 API 报告进行问题定位

## 📝 注意事项

1. **首次使用**：建议先运行 SDK 分析功能了解您的 API 结构
2. **参数设置**：修改参数后需要点击"设置"按钮才能生效
3. **图像保存**：只有在获取到图像后才能保存
4. **资源管理**：关闭应用程序时会自动断开相机连接

## 🎉 完成状态

✅ **已完成的功能**
- 完整的图形用户界面
- 设备管理和连接控制
- 图像采集和显示
- 参数设置和控制
- SDK 分析和调试工具
- 实时状态监控和日志记录

🔄 **需要完善的部分**
- 根据您的具体 MVS SDK API 完善实现细节
- 根据 API 分析报告调整方法调用
- 添加特定相机型号的专用功能

现在您可以使用这个功能完整的相机控制器来管理您的 MVS 工业相机了！如果遇到任何问题，请查看操作日志或使用 SDK 分析功能进行诊断。
