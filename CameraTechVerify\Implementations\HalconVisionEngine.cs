using System.Drawing;
using CameraTechVerify.Interfaces;
using CameraTechVerify.Models;
using HalconDotNet;

namespace CameraTechVerify.Implementations
{
    /// <summary>
    /// Halcon 视觉算法引擎实现
    /// </summary>
    public class HalconVisionEngine : IVisionAlgorithmEngine
    {
        #region 私有字段

        private bool _disposed = false;
        private bool _initialized = false;
        private Dictionary<string, object> _parameters = new Dictionary<string, object>();
        private Dictionary<string, HTuple> _shapeModels = new Dictionary<string, HTuple>();
        private Dictionary<string, HTuple> _measureTools = new Dictionary<string, HTuple>();
        private Dictionary<string, HTuple> _3dModels = new Dictionary<string, HTuple>();

        #endregion

        #region 基础属性

        public string EngineName => "Halcon Vision Engine";
        public string EngineVersion => "19.11";
        public bool IsInitialized => _initialized;

        #endregion

        #region 事件

        public event EventHandler<AlgorithmProgressEventArgs> ProgressChanged;
        public event EventHandler<AlgorithmCompletedEventArgs> AlgorithmCompleted;
        public event EventHandler<AlgorithmErrorEventArgs> ErrorOccurred;

        #endregion

        #region 初始化与配置

        public bool Initialize(Dictionary<string, object> config = null)
        {
            try
            {
                // 设置 Halcon 系统参数
                HOperatorSet.SetSystem("width", 512);
                HOperatorSet.SetSystem("height", 512);
                
                if (config != null)
                {
                    foreach (var kvp in config)
                    {
                        _parameters[kvp.Key] = kvp.Value;
                        
                        // 应用 Halcon 系统参数
                        if (kvp.Key == "width" || kvp.Key == "height")
                        {
                            HOperatorSet.SetSystem(kvp.Key, kvp.Value.ToString());
                        }
                        else if (kvp.Key == "use_window_thread" && kvp.Value is bool useWindowThread)
                        {
                            HOperatorSet.SetSystem("use_window_thread", useWindowThread ? "true" : "false");
                        }
                    }
                }

                _initialized = true;
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("Initialize", ex);
                return false;
            }
        }

        public void SetParameter(string key, object value)
        {
            _parameters[key] = value;
        }

        public T GetParameter<T>(string key)
        {
            if (_parameters.TryGetValue(key, out object value))
            {
                return (T)value;
            }
            return default(T);
        }

        #endregion

        #region 图像处理基础功能

        public IVisionImage LoadImage(string filePath)
        {
            try
            {
                HObject hImage;
                HOperatorSet.ReadImage(out hImage, filePath);
                return new HalconVisionImage(hImage);
            }
            catch (Exception ex)
            {
                OnErrorOccurred("LoadImage", ex);
                return null;
            }
        }

        public IVisionImage CreateImage(Bitmap bitmap)
        {
            try
            {
                // 将 Bitmap 转换为 Halcon 图像
                HObject hImage = BitmapToHalconImage(bitmap);
                return new HalconVisionImage(hImage);
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CreateImage", ex);
                return null;
            }
        }

        public IVisionImage CreateImage(IntPtr data, int width, int height, VisionPixelFormat pixelFormat)
        {
            try
            {
                HObject hImage;
                string halconPixelType = ConvertPixelFormat(pixelFormat);
                HOperatorSet.GenImage1Extern(out hImage, halconPixelType, width, height, data, IntPtr.Zero);
                return new HalconVisionImage(hImage);
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CreateImage", ex);
                return null;
            }
        }

        public bool SaveImage(IVisionImage image, string filePath, VisionImageFormat format)
        {
            try
            {
                if (image is HalconVisionImage halconImage)
                {
                    string halconFormat = ConvertImageFormat(format);
                    HOperatorSet.WriteImage(halconImage.HObject, halconFormat, 0, filePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("SaveImage", ex);
                return false;
            }
        }

        #endregion

        #region 图像预处理

        public IVisionRegion Threshold(IVisionImage image, double minThreshold, double maxThreshold)
        {
            try
            {
                if (image is HalconVisionImage halconImage)
                {
                    HObject region;
                    HOperatorSet.Threshold(halconImage.HObject, out region, minThreshold, maxThreshold);
                    return new HalconVisionRegion(region);
                }
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("Threshold", ex);
                return null;
            }
        }

        public IVisionContour EdgeDetection(IVisionImage image, EdgeDetectionMethod method, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (image is HalconVisionImage halconImage)
                {
                    HObject edges;
                    
                    switch (method)
                    {
                        case EdgeDetectionMethod.Canny:
                            double alpha = parameters?.ContainsKey("alpha") == true ? (double)parameters["alpha"] : 1.0;
                            double low = parameters?.ContainsKey("low") == true ? (double)parameters["low"] : 20.0;
                            double high = parameters?.ContainsKey("high") == true ? (double)parameters["high"] : 40.0;
                            HOperatorSet.EdgesSubPix(halconImage.HObject, out edges, "canny", alpha, low, high);
                            break;
                        
                        case EdgeDetectionMethod.Sobel:
                            HOperatorSet.EdgesSubPix(halconImage.HObject, out edges, "sobel", 1.0, 20.0, 40.0);
                            break;
                        
                        default:
                            HOperatorSet.EdgesSubPix(halconImage.HObject, out edges, "canny", 1.0, 20.0, 40.0);
                            break;
                    }
                    
                    return new HalconVisionContour(edges);
                }
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("EdgeDetection", ex);
                return null;
            }
        }

        public IVisionRegion MorphologyOperation(IVisionRegion region, MorphologyOperation operation, StructuringElement structuringElement)
        {
            try
            {
                if (region is HalconVisionRegion halconRegion)
                {
                    HObject result;
                    double radius = 3.5; // 默认半径
                    
                    switch (operation)
                    {
                        case MorphologyOperation.Erosion:
                            if (structuringElement == StructuringElement.Circle)
                                HOperatorSet.ErosionCircle(halconRegion.HObject, out result, radius);
                            else
                                HOperatorSet.ErosionRectangle1(halconRegion.HObject, out result, 3, 3);
                            break;
                        
                        case MorphologyOperation.Dilation:
                            if (structuringElement == StructuringElement.Circle)
                                HOperatorSet.DilationCircle(halconRegion.HObject, out result, radius);
                            else
                                HOperatorSet.DilationRectangle1(halconRegion.HObject, out result, 3, 3);
                            break;
                        
                        case MorphologyOperation.Opening:
                            if (structuringElement == StructuringElement.Circle)
                                HOperatorSet.OpeningCircle(halconRegion.HObject, out result, radius);
                            else
                                HOperatorSet.OpeningRectangle1(halconRegion.HObject, out result, 3, 3);
                            break;
                        
                        case MorphologyOperation.Closing:
                            if (structuringElement == StructuringElement.Circle)
                                HOperatorSet.ClosingCircle(halconRegion.HObject, out result, radius);
                            else
                                HOperatorSet.ClosingRectangle1(halconRegion.HObject, out result, 3, 3);
                            break;
                        
                        default:
                            result = halconRegion.HObject;
                            break;
                    }
                    
                    return new HalconVisionRegion(result);
                }
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("MorphologyOperation", ex);
                return null;
            }
        }

        public IVisionImage Filter(IVisionImage image, FilterType filterType, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (image is HalconVisionImage halconImage)
                {
                    HObject result;
                    
                    switch (filterType)
                    {
                        case FilterType.Gaussian:
                            double sigma = parameters?.ContainsKey("sigma") == true ? (double)parameters["sigma"] : 1.0;
                            HOperatorSet.GaussFilter(halconImage.HObject, out result, sigma);
                            break;
                        
                        case FilterType.Mean:
                            int maskWidth = parameters?.ContainsKey("maskWidth") == true ? (int)parameters["maskWidth"] : 5;
                            int maskHeight = parameters?.ContainsKey("maskHeight") == true ? (int)parameters["maskHeight"] : 5;
                            HOperatorSet.MeanImage(halconImage.HObject, out result, maskWidth, maskHeight);
                            break;
                        
                        case FilterType.Median:
                            string maskType = parameters?.ContainsKey("maskType") == true ? (string)parameters["maskType"] : "circle";
                            int radius = parameters?.ContainsKey("radius") == true ? (int)parameters["radius"] : 3;
                            HOperatorSet.MedianImage(halconImage.HObject, out result, maskType, radius, "mirrored");
                            break;
                        
                        default:
                            result = halconImage.HObject;
                            break;
                    }
                    
                    return new HalconVisionImage(result);
                }
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("Filter", ex);
                return null;
            }
        }

        #endregion

        #region 模板匹配与识别

        public string CreateShapeModel(IVisionImage templateImage, IVisionRegion templateRegion = null, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (templateImage is HalconVisionImage halconImage)
                {
                    HTuple modelId;
                    HObject imageReduced = halconImage.HObject;

                    // 如果有模板区域，则裁剪图像
                    if (templateRegion is HalconVisionRegion halconRegion)
                    {
                        HOperatorSet.ReduceDomain(halconImage.HObject, halconRegion.HObject, out imageReduced);
                    }

                    // 获取参数
                    double angleStart = parameters?.ContainsKey("angleStart") == true ? (double)parameters["angleStart"] : 0.0;
                    double angleExtent = parameters?.ContainsKey("angleExtent") == true ? (double)parameters["angleExtent"] : 6.28;
                    double minContrast = parameters?.ContainsKey("minContrast") == true ? (double)parameters["minContrast"] : 30.0;

                    HOperatorSet.CreateShapeModel(imageReduced, "auto", angleStart, angleExtent,
                        "auto", "auto", "use_polarity", minContrast, "auto", out modelId);

                    string modelIdStr = Guid.NewGuid().ToString();
                    _shapeModels[modelIdStr] = modelId;

                    if (imageReduced != halconImage.HObject)
                        imageReduced.Dispose();

                    return modelIdStr;
                }
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CreateShapeModel", ex);
                return null;
            }
        }

        public List<MatchResult> FindShapeModel(IVisionImage image, string modelId, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (image is HalconVisionImage halconImage && _shapeModels.ContainsKey(modelId))
                {
                    HTuple row, column, angle, score;

                    // 获取搜索参数
                    double angleStart = parameters?.ContainsKey("angleStart") == true ? (double)parameters["angleStart"] : 0.0;
                    double angleExtent = parameters?.ContainsKey("angleExtent") == true ? (double)parameters["angleExtent"] : 6.28;
                    double minScore = parameters?.ContainsKey("minScore") == true ? (double)parameters["minScore"] : 0.5;
                    int numMatches = parameters?.ContainsKey("numMatches") == true ? (int)parameters["numMatches"] : 1;

                    HOperatorSet.FindShapeModel(halconImage.HObject, _shapeModels[modelId],
                        angleStart, angleExtent, minScore, numMatches, 0.5, "least_squares",
                        0, 0.9, out row, out column, out angle, out score);

                    var results = new List<MatchResult>();
                    for (int i = 0; i < row.Length; i++)
                    {
                        results.Add(new MatchResult
                        {
                            Row = row[i].D,
                            Column = column[i].D,
                            Angle = angle[i].D,
                            Score = score[i].D,
                            ModelId = modelId
                        });
                    }

                    return results;
                }
                return new List<MatchResult>();
            }
            catch (Exception ex)
            {
                OnErrorOccurred("FindShapeModel", ex);
                return new List<MatchResult>();
            }
        }

        public void ClearShapeModel(string modelId)
        {
            try
            {
                if (_shapeModels.ContainsKey(modelId))
                {
                    HOperatorSet.ClearShapeModel(_shapeModels[modelId]);
                    _shapeModels.Remove(modelId);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred("ClearShapeModel", ex);
            }
        }

        #endregion

        #region 测量功能

        public string CreateMeasureTool(MeasureType measureType, Dictionary<string, object> parameters)
        {
            try
            {
                HTuple metrologyHandle;
                HOperatorSet.CreateMetrologyModel(out metrologyHandle);

                string toolId = Guid.NewGuid().ToString();
                _measureTools[toolId] = metrologyHandle;

                // 根据测量类型添加测量对象
                switch (measureType)
                {
                    case MeasureType.Circle:
                        AddCircleMeasure(metrologyHandle, parameters);
                        break;
                    case MeasureType.Line:
                        AddLineMeasure(metrologyHandle, parameters);
                        break;
                    case MeasureType.Caliper:
                        AddCaliperMeasure(metrologyHandle, parameters);
                        break;
                }

                return toolId;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CreateMeasureTool", ex);
                return null;
            }
        }

        public MeasureResult ExecuteMeasure(IVisionImage image, string toolId)
        {
            try
            {
                if (image is HalconVisionImage halconImage && _measureTools.ContainsKey(toolId))
                {
                    HTuple metrologyHandle = _measureTools[toolId];

                    // 执行测量
                    HOperatorSet.ApplyMetrologyModel(halconImage.HObject, metrologyHandle);

                    // 获取测量结果
                    HTuple parameter, value;
                    HOperatorSet.GetMetrologyObjectResult(metrologyHandle, "all", "all",
                        "result_type", "all_param", out parameter, out value);

                    var result = new MeasureResult
                    {
                        IsValid = true,
                        Values = new Dictionary<string, double>()
                    };

                    // 解析结果
                    for (int i = 0; i < parameter.Length; i++)
                    {
                        result.Values[parameter[i].S] = value[i].D;
                    }

                    return result;
                }

                return new MeasureResult { IsValid = false, ErrorMessage = "Invalid tool or image" };
            }
            catch (Exception ex)
            {
                OnErrorOccurred("ExecuteMeasure", ex);
                return new MeasureResult { IsValid = false, ErrorMessage = ex.Message };
            }
        }

        #endregion

        #region 几何拟合与计算

        public CircleResult FitCircle(IVisionContour contour, FittingMethod method = FittingMethod.LeastSquares)
        {
            try
            {
                if (contour is HalconVisionContour halconContour)
                {
                    HTuple row, column, radius;
                    string algorithm = method == FittingMethod.LeastSquares ? "algebraic" : "geometric";

                    HOperatorSet.FitCircleContourXld(halconContour.HObject, algorithm, -1, 0, 0, 3, 2,
                        out row, out column, out radius, out HTuple startPhi, out HTuple endPhi, out HTuple pointOrder);

                    return new CircleResult
                    {
                        IsValid = true,
                        CenterX = column.D,
                        CenterY = row.D,
                        Radius = radius.D
                    };
                }

                return new CircleResult { IsValid = false, ErrorMessage = "Invalid contour" };
            }
            catch (Exception ex)
            {
                OnErrorOccurred("FitCircle", ex);
                return new CircleResult { IsValid = false, ErrorMessage = ex.Message };
            }
        }

        public LineResult FitLine(IVisionContour contour, FittingMethod method = FittingMethod.LeastSquares)
        {
            try
            {
                if (contour is HalconVisionContour halconContour)
                {
                    HTuple rowBegin, colBegin, rowEnd, colEnd, nr, nc, dist;
                    string algorithm = method == FittingMethod.LeastSquares ? "tukey" : "huber";

                    HOperatorSet.FitLineContourXld(halconContour.HObject, algorithm, -1, 0, 5, 2,
                        out rowBegin, out colBegin, out rowEnd, out colEnd, out nr, out nc, out dist);

                    return new LineResult
                    {
                        IsValid = true,
                        StartX = colBegin.D,
                        StartY = rowBegin.D,
                        EndX = colEnd.D,
                        EndY = rowEnd.D
                    };
                }

                return new LineResult { IsValid = false, ErrorMessage = "Invalid contour" };
            }
            catch (Exception ex)
            {
                OnErrorOccurred("FitLine", ex);
                return new LineResult { IsValid = false, ErrorMessage = ex.Message };
            }
        }

        public double CalculateDistance(VisionPoint point1, VisionPoint point2)
        {
            try
            {
                HTuple distance;
                HOperatorSet.DistancePp(point1.Y, point1.X, point2.Y, point2.X, out distance);
                return distance.D;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CalculateDistance", ex);
                return 0.0;
            }
        }

        public double CalculateAngle(VisionPoint center, VisionPoint point1, VisionPoint point2)
        {
            try
            {
                HTuple angle;
                HOperatorSet.AngleLx(center.Y, center.X, point1.Y, point1.X, out HTuple angle1);
                HOperatorSet.AngleLx(center.Y, center.X, point2.Y, point2.X, out HTuple angle2);
                angle = angle2.D - angle1.D;

                // 规范化角度到 [-π, π]
                while (angle.D > Math.PI) angle = angle.D - 2 * Math.PI;
                while (angle.D < -Math.PI) angle = angle.D + 2 * Math.PI;

                return angle.D;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("CalculateAngle", ex);
                return 0.0;
            }
        }

        #endregion

        #region 3D 处理功能

        public string Create3DModel(VisionPointCloud pointCloud)
        {
            try
            {
                if (pointCloud?.Points3D?.Count > 0)
                {
                    // 准备点云数据
                    HTuple x = new HTuple();
                    HTuple y = new HTuple();
                    HTuple z = new HTuple();

                    foreach (var point in pointCloud.Points3D)
                    {
                        x = x.TupleConcat(point.X);
                        y = y.TupleConcat(point.Y);
                        z = z.TupleConcat(point.Z);
                    }

                    HTuple objectModel3D;
                    HOperatorSet.GenObjectModel3dFromPoints(x, y, z, out objectModel3D);

                    string modelId = Guid.NewGuid().ToString();
                    _3dModels[modelId] = objectModel3D;

                    return modelId;
                }

                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("Create3DModel", ex);
                return null;
            }
        }

        public AlgorithmResult Visualize3D(string modelId, Pose3D viewPose)
        {
            try
            {
                if (_3dModels.ContainsKey(modelId))
                {
                    HTuple objectModel3D = _3dModels[modelId];

                    // 设置3D可视化参数
                    HTuple pose = new HTuple(new double[] { viewPose.X, viewPose.Y, viewPose.Z,
                                                          viewPose.Rx, viewPose.Ry, viewPose.Rz });

                    // 这里可以添加具体的3D可视化实现
                    // HOperatorSet.VisualizeObjectModel3d(...);

                    return new AlgorithmResult { IsSuccess = true, Message = "3D visualization completed" };
                }

                return new AlgorithmResult { IsSuccess = false, Message = "Model not found" };
            }
            catch (Exception ex)
            {
                OnErrorOccurred("Visualize3D", ex);
                return new AlgorithmResult { IsSuccess = false, Message = ex.Message };
            }
        }

        public string IntersectPlane3D(string modelId, Pose3D planePose)
        {
            try
            {
                if (_3dModels.ContainsKey(modelId))
                {
                    HTuple objectModel3D = _3dModels[modelId];
                    HTuple intersectionModel;

                    // 定义平面参数
                    HTuple pose = new HTuple(new double[] { planePose.X, planePose.Y, planePose.Z,
                                                          planePose.Rx, planePose.Ry, planePose.Rz });

                    HOperatorSet.IntersectPlaneObjectModel3d(objectModel3D, pose, out intersectionModel);

                    string intersectionId = Guid.NewGuid().ToString();
                    _3dModels[intersectionId] = intersectionModel;

                    return intersectionId;
                }

                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred("IntersectPlane3D", ex);
                return null;
            }
        }

        #endregion

        #region 显示与标注功能

        public void DisplayImage(IVisionImage image, string windowId = "default")
        {
            try
            {
                if (image is HalconVisionImage halconImage)
                {
                    // 打开或设置窗口
                    HTuple windowHandle;
                    try
                    {
                        HOperatorSet.OpenWindow(0, 0, 512, 512, 0, "visible", "", out windowHandle);
                    }
                    catch
                    {
                        // 如果窗口已存在，获取当前窗口句柄
                        HOperatorSet.GetWindowHandle(out windowHandle);
                    }

                    HOperatorSet.DispObj(halconImage.HObject, windowHandle);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred("DisplayImage", ex);
            }
        }

        public void DisplayRegion(IVisionRegion region, string color = "red", string windowId = "default")
        {
            try
            {
                if (region is HalconVisionRegion halconRegion)
                {
                    HTuple windowHandle;
                    HOperatorSet.GetWindowHandle(out windowHandle);
                    HOperatorSet.SetColor(windowHandle, color);
                    HOperatorSet.DispObj(halconRegion.HObject, windowHandle);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred("DisplayRegion", ex);
            }
        }

        public void DisplayContour(IVisionContour contour, string color = "green", string windowId = "default")
        {
            try
            {
                if (contour is HalconVisionContour halconContour)
                {
                    HTuple windowHandle;
                    HOperatorSet.GetWindowHandle(out windowHandle);
                    HOperatorSet.SetColor(windowHandle, color);
                    HOperatorSet.DispObj(halconContour.HObject, windowHandle);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred("DisplayContour", ex);
            }
        }

        public void DisplayText(string text, VisionPoint position, string color = "yellow", string windowId = "default")
        {
            try
            {
                HTuple windowHandle;
                HOperatorSet.GetWindowHandle(out windowHandle);
                HOperatorSet.SetColor(windowHandle, color);
                HOperatorSet.SetTposition(windowHandle, position.Y, position.X);
                HOperatorSet.WriteString(windowHandle, text);
            }
            catch (Exception ex)
            {
                OnErrorOccurred("DisplayText", ex);
            }
        }

        public void ClearWindow(string windowId = "default")
        {
            try
            {
                HTuple windowHandle;
                HOperatorSet.GetWindowHandle(out windowHandle);
                HOperatorSet.ClearWindow(windowHandle);
            }
            catch (Exception ex)
            {
                OnErrorOccurred("ClearWindow", ex);
            }
        }

        #endregion

        #region 辅助方法

        private void AddCircleMeasure(HTuple metrologyHandle, Dictionary<string, object> parameters)
        {
            double row = parameters?.ContainsKey("row") == true ? (double)parameters["row"] : 100;
            double column = parameters?.ContainsKey("column") == true ? (double)parameters["column"] : 100;
            double radius = parameters?.ContainsKey("radius") == true ? (double)parameters["radius"] : 50;
            double measureLength1 = parameters?.ContainsKey("measureLength1") == true ? (double)parameters["measureLength1"] : 20;
            double measureLength2 = parameters?.ContainsKey("measureLength2") == true ? (double)parameters["measureLength2"] : 5;
            double sigma = parameters?.ContainsKey("sigma") == true ? (double)parameters["sigma"] : 1.0;
            double threshold = parameters?.ContainsKey("threshold") == true ? (double)parameters["threshold"] : 30;

            HTuple index;
            HOperatorSet.AddMetrologyObjectCircleMeasure(metrologyHandle, row, column, radius,
                measureLength1, measureLength2, sigma, threshold, "all", "all", out index);
        }

        private void AddLineMeasure(HTuple metrologyHandle, Dictionary<string, object> parameters)
        {
            double rowBegin = parameters?.ContainsKey("rowBegin") == true ? (double)parameters["rowBegin"] : 100;
            double columnBegin = parameters?.ContainsKey("columnBegin") == true ? (double)parameters["columnBegin"] : 100;
            double rowEnd = parameters?.ContainsKey("rowEnd") == true ? (double)parameters["rowEnd"] : 200;
            double columnEnd = parameters?.ContainsKey("columnEnd") == true ? (double)parameters["columnEnd"] : 200;
            double measureLength1 = parameters?.ContainsKey("measureLength1") == true ? (double)parameters["measureLength1"] : 20;
            double measureLength2 = parameters?.ContainsKey("measureLength2") == true ? (double)parameters["measureLength2"] : 5;
            double sigma = parameters?.ContainsKey("sigma") == true ? (double)parameters["sigma"] : 1.0;
            double threshold = parameters?.ContainsKey("threshold") == true ? (double)parameters["threshold"] : 30;

            HTuple index;
            HOperatorSet.AddMetrologyObjectLineMeasure(metrologyHandle, rowBegin, columnBegin, rowEnd, columnEnd,
                measureLength1, measureLength2, sigma, threshold, "all", "all", out index);
        }

        private void AddCaliperMeasure(HTuple metrologyHandle, Dictionary<string, object> parameters)
        {
            // 卡尺测量的具体实现
            AddCircleMeasure(metrologyHandle, parameters);
        }

        #endregion

        #region 图像转换和格式处理

        private HObject BitmapToHalconImage(Bitmap bitmap)
        {
            HObject hImage;

            // 将 Bitmap 转换为 Halcon 图像的实现
            Rectangle rect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
            System.Drawing.Imaging.BitmapData bmpData = bitmap.LockBits(rect,
                System.Drawing.Imaging.ImageLockMode.ReadOnly, bitmap.PixelFormat);

            try
            {
                if (bitmap.PixelFormat == System.Drawing.Imaging.PixelFormat.Format24bppRgb)
                {
                    HOperatorSet.GenImageInterleaved(out hImage, bmpData.Scan0, "rgb",
                        bitmap.Width, bitmap.Height, 0, "byte", bitmap.Width, bitmap.Height, 0, 0, -1, 0);
                }
                else if (bitmap.PixelFormat == System.Drawing.Imaging.PixelFormat.Format8bppIndexed)
                {
                    HOperatorSet.GenImage1Extern(out hImage, "byte", bitmap.Width, bitmap.Height,
                        bmpData.Scan0, IntPtr.Zero);
                }
                else
                {
                    // 默认处理为灰度图像
                    HOperatorSet.GenImage1Extern(out hImage, "byte", bitmap.Width, bitmap.Height,
                        bmpData.Scan0, IntPtr.Zero);
                }
            }
            finally
            {
                bitmap.UnlockBits(bmpData);
            }

            return hImage;
        }

        private string ConvertPixelFormat(VisionPixelFormat pixelFormat)
        {
            switch (pixelFormat)
            {
                case VisionPixelFormat.Mono8:
                    return "byte";
                case VisionPixelFormat.Mono16:
                    return "uint2";
                case VisionPixelFormat.RGB24:
                    return "byte";
                case VisionPixelFormat.BGR24:
                    return "byte";
                default:
                    return "byte";
            }
        }

        private string ConvertImageFormat(VisionImageFormat format)
        {
            switch (format)
            {
                case VisionImageFormat.BMP:
                    return "bmp";
                case VisionImageFormat.JPEG:
                    return "jpeg";
                case VisionImageFormat.PNG:
                    return "png";
                case VisionImageFormat.TIFF:
                    return "tiff";
                default:
                    return "bmp";
            }
        }

        #endregion

        #region 事件处理

        private void OnProgressChanged(string algorithmName, int percentage, string message)
        {
            ProgressChanged?.Invoke(this, new AlgorithmProgressEventArgs
            {
                AlgorithmName = algorithmName,
                ProgressPercentage = percentage,
                StatusMessage = message
            });
        }

        private void OnAlgorithmCompleted(string algorithmName, bool success, object result, TimeSpan executionTime)
        {
            AlgorithmCompleted?.Invoke(this, new AlgorithmCompletedEventArgs
            {
                AlgorithmName = algorithmName,
                IsSuccess = success,
                Result = result,
                ExecutionTime = executionTime
            });
        }

        private void OnErrorOccurred(string algorithmName, Exception exception)
        {
            ErrorOccurred?.Invoke(this, new AlgorithmErrorEventArgs
            {
                AlgorithmName = algorithmName,
                Exception = exception,
                ErrorMessage = exception.Message
            });
        }

        #endregion

        #region 资源清理

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 清理形状模板
                    foreach (var model in _shapeModels.Values)
                    {
                        try
                        {
                            HOperatorSet.ClearShapeModel(model);
                        }
                        catch { }
                    }
                    _shapeModels.Clear();

                    // 清理测量工具
                    foreach (var tool in _measureTools.Values)
                    {
                        try
                        {
                            HOperatorSet.ClearMetrologyModel(tool);
                        }
                        catch { }
                    }
                    _measureTools.Clear();

                    // 清理3D模型
                    foreach (var model in _3dModels.Values)
                    {
                        try
                        {
                            HOperatorSet.ClearObjectModel3d(model);
                        }
                        catch { }
                    }
                    _3dModels.Clear();
                }

                _disposed = true;
            }
        }

        ~HalconVisionEngine()
        {
            Dispose(false);
        }

        #endregion
    }
}
