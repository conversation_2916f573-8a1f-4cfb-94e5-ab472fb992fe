﻿<Window x:Class="CameraTechVerify.Windows.TestSDKControllerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CameraTechVerify.Windows"
        mc:Ignorable="d" Title="TestSDK Controller Demo" Height="800" Width="1280" Closing="Window_Closing">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="3*"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="4*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"></ColumnDefinition>
            <ColumnDefinition Width="1*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <!-- 设备选择下拉框 -->
        <ComboBox Name="cbDeviceList" Grid.Row="0" Grid.Column="0" Margin="5"></ComboBox>
        
        <!-- 状态显示 -->
        <TextBlock Name="tbStatus" Grid.Row="0" Grid.Column="1" Margin="5" 
                   Text="未连接" VerticalAlignment="Center" HorizontalAlignment="Center"
                   Foreground="Red" FontWeight="Bold"/>
        
        <!-- 图像显示区域 -->
        <Image Name="Image1" Grid.Row="1" Grid.RowSpan="4" Margin="10" 
               Stretch="Uniform" StretchDirection="Both"/>
        
        <!-- 设备初始化控制组 -->
        <GroupBox Grid.Row="0" Grid.Column="1" Grid.RowSpan="2" Header="Initialization" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Button Name="bnEnum" Click="bnEnum_Click" Content="Search Devices" Margin="5" Grid.ColumnSpan="2"></Button>
                <Button Name="bnOpen" Click="bnOpen_Click" Content="Open Device" Margin="5" Grid.Row="1"></Button>
                <Button Name="bnClose" Click="bnClose_Click" Content="Close Device" Margin="5" Grid.Row="1" Grid.Column="1"></Button>
            </Grid>
        </GroupBox>
        
        <!-- 图像采集控制组 -->
        <GroupBox Grid.Row="2" Grid.Column="1" Header="Image Acquisition" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <RadioButton Name="bnContinuesMode" Content="Continuous" Checked="bnContinuesMode_Checked" Margin="5"></RadioButton>
                <RadioButton Name="bnTriggerMode" Content="Trigger Mode" Grid.Column="1" Checked="bnTriggerMode_Checked" Margin="5"></RadioButton>
                <Button Name="bnStartGrab" Click="bnStartGrab_Click" Content="Start Grab" Grid.Row="1" Margin="5"></Button>
                <Button Name="bnStopGrab" Click="bnStopGrab_Click" Content="Stop Grab" Grid.Row="1" Grid.Column="1" Margin="5"></Button>
                <CheckBox Name="cbSoftTrigger" Grid.Row="2" Content="Trigger by Software" 
                          Checked="cbSoftTrigger_Checked" Unchecked="cbSoftTrigger_Unchecked" Margin="5"></CheckBox>
                <Button Name="bnTriggerExec" Content="Trigger Once" Grid.Row="2" Grid.Column="1" 
                        Click="bnTriggerExec_Click" Margin="5"></Button>
                <Button Name="bnStartRecord" Click="bnStartRecord_Click" Grid.Row="3" Content="Start Record" Margin="5"></Button>
                <Button Name="bnStopRecord" Click="bnStopRecord_Click" Grid.Row="3" Grid.Column="1" Content="Stop Record" Margin="5"></Button>
            </Grid>
        </GroupBox>
        
        <!-- 图像保存控制组 -->
        <GroupBox Grid.Row="3" Grid.Column="1" Header="Picture Storage" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Button Name="bnSaveBmp" Click="bnSaveBmp_Click" Grid.Row="0" Grid.Column="0" Content="Save Bmp" Margin="5"></Button>
                <Button Name="bnSaveJpg" Click="bnSaveJpg_Click" Grid.Row="0" Grid.Column="1" Content="Save Jpg" Margin="5"></Button>
                <Button Name="bnSaveTiff" Click="bnSaveTiff_Click" Grid.Row="1" Grid.Column="0" Content="Save Tiff" Margin="5"></Button>
                <Button Name="bnSavePng" Click="bnSavePng_Click" Grid.Row="1" Grid.Column="1" Content="Save Png" Margin="5"></Button>
            </Grid>
        </GroupBox>
        
        <!-- 参数控制组 -->
        <GroupBox Grid.Row="4" Grid.Column="1" Header="Parameters" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Label Content="Exposure Time" Grid.Row="0" Grid.Column="0" Margin="2"></Label>
                <Label Content="Gain" Grid.Row="1" Grid.Column="0" Margin="2"></Label>
                <Label Content="Frame Rate" Grid.Row="2" Grid.Column="0" Margin="2"></Label>
                <Label Content="Pixel Format" Grid.Row="3" Grid.Column="0" Margin="2"></Label>
                <TextBox Name="tbExposure" Grid.Row="0" Grid.Column="1" Margin="2" Text="10000"></TextBox>
                <TextBox Name="tbGain" Grid.Row="1" Grid.Column="1" Margin="2" Text="0"></TextBox>
                <TextBox Name="tbFrameRate" Grid.Row="2" Grid.Column="1" Margin="2" Text="30"></TextBox>
                <ComboBox Name="cbPixelFormat" Grid.Row="3" Grid.Column="1" Margin="2" 
                          SelectionChanged="cbPixelFormat_SelectionChanged">
                    <ComboBoxItem Content="Mono8" IsSelected="True"/>
                    <ComboBoxItem Content="Mono10"/>
                    <ComboBoxItem Content="Mono12"/>
                    <ComboBoxItem Content="RGB8"/>
                    <ComboBoxItem Content="BGR8"/>
                    <ComboBoxItem Content="YUV422"/>
                </ComboBox>
                <Button Name="bnGetParam" Content="Get Parameter" Grid.Row="4" Grid.Column="0" 
                        Click="bnGetParam_Click" Margin="2"></Button>
                <Button Name="bnSetParam" Content="Set Parameter" Grid.Row="4" Grid.Column="1" 
                        Click="bnSetParam_Click" Margin="2"></Button>
            </Grid>
        </GroupBox>
    </Grid>
</Window>
