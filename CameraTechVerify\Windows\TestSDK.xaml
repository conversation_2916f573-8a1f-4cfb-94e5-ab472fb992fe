﻿<Window x:Class="CameraTechVerify.Windows.TestSDK"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CameraTechVerify.Windows"
        mc:Ignorable="d" Title="TestSDK" Height="800" Width="1280" Closing="Window_Closing">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="3*"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="4*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"></ColumnDefinition>
            <ColumnDefinition Width="1*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <ComboBox Name="cbDeviceList"></ComboBox>
        <Image Name="Image1" Grid.Row="1" Grid.RowSpan="4" Margin="100"></Image>
        <GroupBox Grid.Row="0" Grid.Column="1" Grid.RowSpan="2" Header="Initialization">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Button Name="bnEnum" Click="bnEnum_Click" Content="Search Devices" Margin="10" Grid.ColumnSpan="2"></Button>
                <Button Name="bnOpen" Click="bnOpen_Click" Content="Open Device" Margin="10" Grid.Row="1"></Button>
                <Button Name="bnClose" Click="bnClose_Click" Content="Close Device" Margin="10" Grid.Row="1" Grid.Column="1"></Button>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Row="2" Grid.Column="1" Header="Image Acquisition">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <RadioButton Name="bnContinuesMode" Content="Continuous" Checked="bnContinuesMode_Checked"></RadioButton>
                <RadioButton Name="bnTriggerMode" Content="Trigger Mode" Grid.Column="1" Checked="bnTriggerMode_Checked"></RadioButton>
                <Button Name="bnStartGrab" Click="bnStartGrab_Click" Content="Start Grab" Grid.Row="1"></Button>
                <Button Name="bnStopGrab" Click="bnStopGrab_Click" Content="Stop Grab" Grid.Row="1" Grid.Column="1"></Button>
                <CheckBox Name="cbSoftTrigger" Grid.Row="2" Content="Trigger by Software" Checked="cbSoftTrigger_Checked" Unchecked="cbSoftTrigger_Unchecked" ></CheckBox>
                <Button Name="bnTriggerExec" Content="Trigger Once" Grid.Row="2" Grid.Column="1" Click="bnTriggerExec_Click"></Button>
                <Button Name="bnStartRecord" Click="bnStartRecord_Click" Grid.Row="3" Content="Start Record"></Button>
                <Button Name="bnStopRecord" Click="bnStopRecord_Click" Grid.Row="3" Grid.Column="1" Content="Stop Record"></Button>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Row="3" Grid.Column="1" Header="Picture Storage">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Button Name="bnSaveBmp" Click="bnSaveBmp_Click" Grid.Row="0" Grid.Column="0" Content="Save Bmp"></Button>
                <Button Name="bnSaveJpg" Click="bnSaveJpg_Click" Grid.Row="0" Grid.Column="1" Content="Save Jpg"></Button>
                <Button Name="bnSaveTiff" Click="bnSaveTiff_Click" Grid.Row="1" Grid.Column="0" Content="Save Tiff"></Button>
                <Button Name="bnSavePng" Click="bnSavePng_Click" Grid.Row="1" Grid.Column="1" Content="Save Png"></Button>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Row="4" Grid.Column="1" Header="Parameters">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Label Content="Exposure Time" Grid.Row="0" Grid.Column="0"></Label>
                <Label Content="Gain" Grid.Row="1" Grid.Column="0"></Label>
                <Label Content="Frame Rate" Grid.Row="2" Grid.Column="0"></Label>
                <Label Content="Pixel Format" Grid.Row="3" Grid.Column="0"></Label>
                <TextBox Name="tbExposure" Grid.Row="0" Grid.Column="1"></TextBox>
                <TextBox Name="tbGain" Grid.Row="1" Grid.Column="1"></TextBox>
                <TextBox Name="tbFrameRate" Grid.Row="2" Grid.Column="1"></TextBox>
                <ComboBox Name="cbPixelFormat" Grid.Row="3" Grid.Column="1" SelectionChanged="cbPixelFormat_SelectionChanged"></ComboBox>
                <Button Name="bnGetParam" Content="Get Parameter" Grid.Row="4" Grid.Column="0" Click="bnGetParam_Click"></Button>
                <Button Name="bnSetParam" Content="Set Parameter" Grid.Row="4" Grid.Column="1" Click="bnSetParam_Click"></Button>
            </Grid>
        </GroupBox>
    </Grid>
</Window>
