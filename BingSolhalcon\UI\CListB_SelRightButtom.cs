﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.LookAndFeel;

namespace BingSolhalcon.UI
{
    public partial class CListB_SelRightButtom: RibbonForm
    {
        public CListBAdd t_CListBAdd;

        public delegate void delegateDelCListB();

        public event delegateDelCListB evenDelCListB;
        public CListB_SelRightButtom()
        {
            InitializeComponent();
        }

        private void Btm_Add_Click(object sender, EventArgs e)
        {

            t_CListBAdd.StartPosition = FormStartPosition.Manual;
            t_CListBAdd.Show();
        }

        private void Btm_Del_Click(object sender, EventArgs e)
        {

            evenDelCListB();
        }

        private void CListB_SelRightButtom_Load(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(CListB_SelRightButtom));
        }
    }
}
