# 🔧 真实 MVS 相机调试指南

## 📋 准备工作

### 1. 确认您的相机信息
请提供以下信息：
- **相机型号**: _________________ (例如: MV-CA050-10GC)
- **连接方式**: _________________ (GigE 网络 / USB3)
- **相机 IP 地址**: _____________ (例如: *************)
- **您的电脑 IP**: _____________ (例如: ************)
- **MVS SDK 安装路径**: ________ (例如: D:\MVS\)

### 2. 网络配置检查（GigE 相机）
```bash
# 1. 检查网络连通性
ping *************

# 2. 检查网络配置
ipconfig

# 3. 确保相机和电脑在同一网段
```

## 🚀 第一步：运行应用程序并查看日志

```bash
dotnet run --project CameraTechVerify/CameraTechVerify.csproj
```

### 关键日志信息
在右侧的"操作日志"中查找以下信息：

#### ✅ 成功的日志示例：
```
[时间] 开始初始化 MVS API...
[时间] ✓ 找到 MVS 程序集: MvCameraControl.Net
[时间] ✓ 找到相机类型: MyCamera
[时间] ✓ MVS API 初始化成功
```

#### ❌ 失败的日志示例：
```
[时间] 在已加载程序集中未找到 MVS SDK，尝试强制加载...
[时间] 警告：未找到 MvCameraControl.Net 程序集，将使用模拟模式
```

## 🔍 第二步：设备枚举测试

### 1. 选择设备类型
- 在界面左上角选择 "GigE" 或 "USB3"
- 点击 "刷新设备" 按钮

### 2. 查看枚举结果
#### ✅ 成功找到真实设备：
```
[时间] 开始枚举设备...
[时间] 尝试调用真实的 MVS API 枚举设备...
[时间] 找到 1 个 GigE 设备
[时间] 设备 0: IP=*************, 型号=MV-CA050-10GC, 序列号=12345678
```

#### ❌ 只找到模拟设备：
```
[时间] MVS API 方法未找到，使用模拟设备进行测试
[时间] 找到 2 个设备
```

## 🔗 第三步：连接测试

### 1. 通过 IP 连接
- 在 "IP 连接" 标签页输入相机 IP 地址
- 点击 "连接" 按钮

### 2. 查看连接日志
#### ✅ 成功连接：
```
[时间] 开始连接到相机 *************:3956
[时间] 检查网络连通性...
[时间] ✓ 网络连通性检查通过
[时间] 开始枚举 MVS 设备...
[时间] 找到 1 个 GigE 设备
[时间] ✓ 找到目标设备: MV-CA050-10GC (*************)
[时间] 创建相机实例...
[时间] 创建设备连接...
[时间] 打开设备...
[时间] ✓ 真实相机连接成功: MV-CA050-10GC
```

#### ❌ 连接失败的常见情况：

**网络不通：**
```
[时间] 无法 Ping 通 *************，请检查网络连接和相机状态
```

**找不到设备：**
```
[时间] 未找到 IP 地址为 ************* 的相机设备
```

**API 调用失败：**
```
[时间] 创建设备失败，错误码: -2147483648
```

## 📸 第四步：图像采集测试

### 1. 单次拍照
- 连接成功后，点击 "单次拍照" 按钮

### 2. 查看拍照日志
#### ✅ 成功获取真实图像：
```
[时间] 开始单次拍照...
[时间] 分配图像缓冲区...
[时间] 调用 MVS API 获取图像，缓冲区大小: 6220800 字节
[时间] MVS API 调用结果: 0
[时间] ✓ 成功获取图像数据: 1920x1080, 像素格式: 0x01080001
[时间] ✓ 图像转换成功，帧号: 1
```

#### ❌ 获取图像失败：
```
[时间] 获取图像方法未找到，请检查 MVS SDK
```
或
```
[时间] 获取图像失败，MVS 错误码: -2147483647
```

## 🛠️ 故障排除

### 问题 1: 找不到 MVS 程序集
**症状**: 日志显示 "未找到 MvCameraControl.Net 程序集"

**解决方案**:
1. 检查 MVS SDK 是否正确安装
2. 确认项目引用了正确的 DLL 文件
3. 检查 DLL 文件路径：
   ```
   D:\MVS\Development\DotNet\win64\MvCameraControl.Net.dll
   ```

### 问题 2: 网络连接失败
**症状**: 无法 Ping 通相机 IP

**解决方案**:
1. 检查网线连接
2. 确认相机和电脑在同一网段
3. 检查防火墙设置
4. 使用 MVS Viewer 测试相机连接

### 问题 3: 设备枚举失败
**症状**: 枚举不到真实设备

**解决方案**:
1. 确认相机电源已开启
2. 检查相机 IP 配置
3. 使用 MVS Viewer 确认相机可见
4. 重启相机和应用程序

### 问题 4: 图像获取失败
**症状**: 连接成功但无法获取图像

**解决方案**:
1. 检查相机是否支持当前像素格式
2. 确认图像缓冲区大小足够
3. 检查相机触发模式设置
4. 尝试先开始连续采集再获取图像

## 📞 调试步骤

### 第一轮调试：基础连接
1. 运行应用程序
2. 查看 MVS API 初始化日志
3. 尝试设备枚举
4. 尝试 IP 连接

### 第二轮调试：图像采集
1. 确认连接成功
2. 尝试单次拍照
3. 查看详细的 API 调用日志
4. 分析错误码

### 第三轮调试：参数调整
1. 调整曝光时间
2. 调整增益
3. 检查触发模式
4. 测试连续采集

## 📝 请提供的调试信息

当遇到问题时，请提供以下信息：

1. **完整的操作日志** (右侧日志面板的所有内容)
2. **相机型号和 IP 地址**
3. **MVS SDK 版本和安装路径**
4. **网络配置信息** (ipconfig 结果)
5. **错误截图**

## 🎯 预期结果

成功调通后，您应该看到：
- ✅ MVS API 正确初始化
- ✅ 能够枚举到真实相机设备
- ✅ 能够成功连接相机
- ✅ 能够获取真实的相机图像
- ✅ 图像显示在界面中央，不是模拟图像

---

**准备好了吗？让我们开始一步步调试您的真实相机！** 🚀
