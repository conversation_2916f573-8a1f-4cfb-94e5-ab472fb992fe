﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>717, 534</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 105</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="dataGridView1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="dataGridView1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 12</value>
  </data>
  <data name="dataGridView1.Size" type="System.Drawing.Size, System.Drawing">
    <value>967, 384</value>
  </data>
  <data name="dataGridView1.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Name" xml:space="preserve">
    <value>dataGridView1</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Type" xml:space="preserve">
    <value>System.Windows.Forms.DataGridView, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dataGridView1.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="btn_check.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btn_check.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btn_check.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_check.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 523</value>
  </data>
  <data name="btn_check.Size" type="System.Drawing.Size, System.Drawing">
    <value>359, 70</value>
  </data>
  <data name="btn_check.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="btn_check.Text" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="&gt;&gt;btn_check.Name" xml:space="preserve">
    <value>btn_check</value>
  </data>
  <data name="&gt;&gt;btn_check.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_check.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_check.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="btn_readdatabase.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btn_readdatabase.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btn_readdatabase.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_readdatabase.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 523</value>
  </data>
  <data name="btn_readdatabase.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 70</value>
  </data>
  <data name="btn_readdatabase.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="btn_readdatabase.Text" xml:space="preserve">
    <value>读取数据库</value>
  </data>
  <data name="&gt;&gt;btn_readdatabase.Name" xml:space="preserve">
    <value>btn_readdatabase</value>
  </data>
  <data name="&gt;&gt;btn_readdatabase.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_readdatabase.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_readdatabase.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="btn_toexcel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btn_toexcel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btn_toexcel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_toexcel.Location" type="System.Drawing.Point, System.Drawing">
    <value>739, 523</value>
  </data>
  <data name="btn_toexcel.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 70</value>
  </data>
  <data name="btn_toexcel.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="btn_toexcel.Text" xml:space="preserve">
    <value>数据导出</value>
  </data>
  <data name="&gt;&gt;btn_toexcel.Name" xml:space="preserve">
    <value>btn_toexcel</value>
  </data>
  <data name="&gt;&gt;btn_toexcel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_toexcel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_toexcel.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="btn_deletedatabase.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btn_deletedatabase.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btn_deletedatabase.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_deletedatabase.Location" type="System.Drawing.Point, System.Drawing">
    <value>468, 523</value>
  </data>
  <data name="btn_deletedatabase.Size" type="System.Drawing.Size, System.Drawing">
    <value>270, 70</value>
  </data>
  <data name="btn_deletedatabase.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="btn_deletedatabase.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btn_deletedatabase.Name" xml:space="preserve">
    <value>btn_deletedatabase</value>
  </data>
  <data name="&gt;&gt;btn_deletedatabase.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_deletedatabase.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_deletedatabase.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="btn_queryWeight.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btn_queryWeight.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btn_queryWeight.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_queryWeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>296, 523</value>
  </data>
  <data name="btn_queryWeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 70</value>
  </data>
  <data name="btn_queryWeight.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="btn_queryWeight.Text" xml:space="preserve">
    <value>查询重量</value>
  </data>
  <data name="&gt;&gt;btn_queryWeight.Name" xml:space="preserve">
    <value>btn_queryWeight</value>
  </data>
  <data name="&gt;&gt;btn_queryWeight.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_queryWeight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_queryWeight.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="btn_connectdatabase.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btn_connectdatabase.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btn_connectdatabase.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_connectdatabase.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 402</value>
  </data>
  <data name="btn_connectdatabase.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 115</value>
  </data>
  <data name="btn_connectdatabase.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="btn_connectdatabase.Text" xml:space="preserve">
    <value>数据库连接</value>
  </data>
  <data name="&gt;&gt;btn_connectdatabase.Name" xml:space="preserve">
    <value>btn_connectdatabase</value>
  </data>
  <data name="&gt;&gt;btn_connectdatabase.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_connectdatabase.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_connectdatabase.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="groupBox4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="dateTimePicker3.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 22</value>
  </data>
  <data name="dateTimePicker3.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 23</value>
  </data>
  <data name="dateTimePicker3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;dateTimePicker3.Name" xml:space="preserve">
    <value>dateTimePicker3</value>
  </data>
  <data name="&gt;&gt;dateTimePicker3.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dateTimePicker3.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;dateTimePicker3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 402</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>523, 55</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>结束时间</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="groupBox3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="dateTimePicker2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 22</value>
  </data>
  <data name="dateTimePicker2.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 23</value>
  </data>
  <data name="dateTimePicker2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dateTimePicker2.Name" xml:space="preserve">
    <value>dateTimePicker2</value>
  </data>
  <data name="&gt;&gt;dateTimePicker2.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dateTimePicker2.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;dateTimePicker2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 402</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>334, 55</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="label7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>849, 466</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 14</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>轮毂型号</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="textBox_wheeltype.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textBox_wheeltype.Location" type="System.Drawing.Point, System.Drawing">
    <value>852, 482</value>
  </data>
  <data name="textBox_wheeltype.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 23</value>
  </data>
  <data name="textBox_wheeltype.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="textBox_wheeltype.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;textBox_wheeltype.Name" xml:space="preserve">
    <value>textBox_wheeltype</value>
  </data>
  <data name="&gt;&gt;textBox_wheeltype.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_wheeltype.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox_wheeltype.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeH1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeH1.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeH1.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeH1.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeH1.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeH1.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeH1.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeH1.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeH1.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeH1.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeH1.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeH1.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeH1.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeH1.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeH1.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeH1.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeH1.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeH1.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeH1.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeH1.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeH1.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeH1.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeH1.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeH1.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeH1.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeH1.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 483</value>
  </data>
  <data name="cbox_timeH1.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeH1.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="cbox_timeH1.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeH1.Name" xml:space="preserve">
    <value>cbox_timeH1</value>
  </data>
  <data name="&gt;&gt;cbox_timeH1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeH1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeH1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeM1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeM1.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeM1.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeM1.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeM1.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeM1.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeM1.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeM1.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeM1.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeM1.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeM1.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeM1.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeM1.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeM1.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeM1.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeM1.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeM1.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeM1.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeM1.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeM1.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeM1.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeM1.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeM1.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeM1.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeM1.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeM1.Items24" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="cbox_timeM1.Items25" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="cbox_timeM1.Items26" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="cbox_timeM1.Items27" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="cbox_timeM1.Items28" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="cbox_timeM1.Items29" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="cbox_timeM1.Items30" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="cbox_timeM1.Items31" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="cbox_timeM1.Items32" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="cbox_timeM1.Items33" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="cbox_timeM1.Items34" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="cbox_timeM1.Items35" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="cbox_timeM1.Items36" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="cbox_timeM1.Items37" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="cbox_timeM1.Items38" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="cbox_timeM1.Items39" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="cbox_timeM1.Items40" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cbox_timeM1.Items41" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="cbox_timeM1.Items42" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="cbox_timeM1.Items43" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="cbox_timeM1.Items44" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="cbox_timeM1.Items45" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="cbox_timeM1.Items46" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="cbox_timeM1.Items47" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="cbox_timeM1.Items48" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="cbox_timeM1.Items49" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="cbox_timeM1.Items50" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="cbox_timeM1.Items51" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="cbox_timeM1.Items52" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="cbox_timeM1.Items53" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="cbox_timeM1.Items54" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="cbox_timeM1.Items55" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="cbox_timeM1.Items56" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="cbox_timeM1.Items57" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="cbox_timeM1.Items58" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="cbox_timeM1.Items59" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="cbox_timeM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 483</value>
  </data>
  <data name="cbox_timeM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeM1.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="cbox_timeM1.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeM1.Name" xml:space="preserve">
    <value>cbox_timeM1</value>
  </data>
  <data name="&gt;&gt;cbox_timeM1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeM1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeM1.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeS1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeS1.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeS1.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeS1.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeS1.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeS1.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeS1.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeS1.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeS1.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeS1.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeS1.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeS1.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeS1.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeS1.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeS1.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeS1.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeS1.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeS1.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeS1.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeS1.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeS1.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeS1.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeS1.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeS1.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeS1.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeS1.Items24" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="cbox_timeS1.Items25" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="cbox_timeS1.Items26" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="cbox_timeS1.Items27" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="cbox_timeS1.Items28" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="cbox_timeS1.Items29" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="cbox_timeS1.Items30" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="cbox_timeS1.Items31" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="cbox_timeS1.Items32" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="cbox_timeS1.Items33" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="cbox_timeS1.Items34" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="cbox_timeS1.Items35" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="cbox_timeS1.Items36" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="cbox_timeS1.Items37" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="cbox_timeS1.Items38" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="cbox_timeS1.Items39" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="cbox_timeS1.Items40" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cbox_timeS1.Items41" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="cbox_timeS1.Items42" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="cbox_timeS1.Items43" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="cbox_timeS1.Items44" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="cbox_timeS1.Items45" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="cbox_timeS1.Items46" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="cbox_timeS1.Items47" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="cbox_timeS1.Items48" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="cbox_timeS1.Items49" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="cbox_timeS1.Items50" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="cbox_timeS1.Items51" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="cbox_timeS1.Items52" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="cbox_timeS1.Items53" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="cbox_timeS1.Items54" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="cbox_timeS1.Items55" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="cbox_timeS1.Items56" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="cbox_timeS1.Items57" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="cbox_timeS1.Items58" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="cbox_timeS1.Items59" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="cbox_timeS1.Location" type="System.Drawing.Point, System.Drawing">
    <value>328, 483</value>
  </data>
  <data name="cbox_timeS1.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeS1.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="cbox_timeS1.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeS1.Name" xml:space="preserve">
    <value>cbox_timeS1</value>
  </data>
  <data name="&gt;&gt;cbox_timeS1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeS1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeS1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeS2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeS2.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeS2.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeS2.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeS2.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeS2.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeS2.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeS2.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeS2.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeS2.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeS2.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeS2.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeS2.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeS2.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeS2.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeS2.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeS2.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeS2.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeS2.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeS2.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeS2.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeS2.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeS2.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeS2.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeS2.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeS2.Items24" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="cbox_timeS2.Items25" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="cbox_timeS2.Items26" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="cbox_timeS2.Items27" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="cbox_timeS2.Items28" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="cbox_timeS2.Items29" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="cbox_timeS2.Items30" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="cbox_timeS2.Items31" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="cbox_timeS2.Items32" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="cbox_timeS2.Items33" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="cbox_timeS2.Items34" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="cbox_timeS2.Items35" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="cbox_timeS2.Items36" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="cbox_timeS2.Items37" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="cbox_timeS2.Items38" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="cbox_timeS2.Items39" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="cbox_timeS2.Items40" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cbox_timeS2.Items41" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="cbox_timeS2.Items42" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="cbox_timeS2.Items43" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="cbox_timeS2.Items44" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="cbox_timeS2.Items45" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="cbox_timeS2.Items46" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="cbox_timeS2.Items47" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="cbox_timeS2.Items48" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="cbox_timeS2.Items49" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="cbox_timeS2.Items50" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="cbox_timeS2.Items51" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="cbox_timeS2.Items52" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="cbox_timeS2.Items53" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="cbox_timeS2.Items54" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="cbox_timeS2.Items55" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="cbox_timeS2.Items56" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="cbox_timeS2.Items57" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="cbox_timeS2.Items58" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="cbox_timeS2.Items59" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="cbox_timeS2.Location" type="System.Drawing.Point, System.Drawing">
    <value>682, 483</value>
  </data>
  <data name="cbox_timeS2.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeS2.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="cbox_timeS2.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeS2.Name" xml:space="preserve">
    <value>cbox_timeS2</value>
  </data>
  <data name="&gt;&gt;cbox_timeS2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeS2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeS2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeM2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeM2.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeM2.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeM2.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeM2.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeM2.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeM2.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeM2.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeM2.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeM2.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeM2.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeM2.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeM2.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeM2.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeM2.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeM2.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeM2.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeM2.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeM2.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeM2.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeM2.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeM2.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeM2.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeM2.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeM2.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeM2.Items24" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="cbox_timeM2.Items25" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="cbox_timeM2.Items26" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="cbox_timeM2.Items27" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="cbox_timeM2.Items28" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="cbox_timeM2.Items29" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="cbox_timeM2.Items30" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="cbox_timeM2.Items31" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="cbox_timeM2.Items32" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="cbox_timeM2.Items33" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="cbox_timeM2.Items34" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="cbox_timeM2.Items35" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="cbox_timeM2.Items36" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="cbox_timeM2.Items37" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="cbox_timeM2.Items38" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="cbox_timeM2.Items39" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="cbox_timeM2.Items40" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cbox_timeM2.Items41" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="cbox_timeM2.Items42" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="cbox_timeM2.Items43" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="cbox_timeM2.Items44" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="cbox_timeM2.Items45" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="cbox_timeM2.Items46" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="cbox_timeM2.Items47" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="cbox_timeM2.Items48" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="cbox_timeM2.Items49" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="cbox_timeM2.Items50" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="cbox_timeM2.Items51" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="cbox_timeM2.Items52" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="cbox_timeM2.Items53" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="cbox_timeM2.Items54" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="cbox_timeM2.Items55" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="cbox_timeM2.Items56" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="cbox_timeM2.Items57" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="cbox_timeM2.Items58" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="cbox_timeM2.Items59" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="cbox_timeM2.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 483</value>
  </data>
  <data name="cbox_timeM2.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeM2.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="cbox_timeM2.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeM2.Name" xml:space="preserve">
    <value>cbox_timeM2</value>
  </data>
  <data name="&gt;&gt;cbox_timeM2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeM2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeM2.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeH2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cbox_timeH2.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbox_timeH2.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_timeH2.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbox_timeH2.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbox_timeH2.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbox_timeH2.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbox_timeH2.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_timeH2.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbox_timeH2.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbox_timeH2.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cbox_timeH2.Items10" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbox_timeH2.Items11" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbox_timeH2.Items12" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbox_timeH2.Items13" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbox_timeH2.Items14" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="cbox_timeH2.Items15" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cbox_timeH2.Items16" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="cbox_timeH2.Items17" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cbox_timeH2.Items18" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbox_timeH2.Items19" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbox_timeH2.Items20" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cbox_timeH2.Items21" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cbox_timeH2.Items22" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbox_timeH2.Items23" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cbox_timeH2.Location" type="System.Drawing.Point, System.Drawing">
    <value>468, 483</value>
  </data>
  <data name="cbox_timeH2.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="cbox_timeH2.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="cbox_timeH2.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cbox_timeH2.Name" xml:space="preserve">
    <value>cbox_timeH2</value>
  </data>
  <data name="&gt;&gt;cbox_timeH2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_timeH2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbox_timeH2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label_H1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_H1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_H1.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 466</value>
  </data>
  <data name="label_H1.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_H1.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="label_H1.Text" xml:space="preserve">
    <value>时</value>
  </data>
  <data name="&gt;&gt;label_H1.Name" xml:space="preserve">
    <value>label_H1</value>
  </data>
  <data name="&gt;&gt;label_H1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_H1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_H1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label_M1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_M1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_M1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label_M1.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 466</value>
  </data>
  <data name="label_M1.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_M1.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="label_M1.Text" xml:space="preserve">
    <value>分</value>
  </data>
  <data name="&gt;&gt;label_M1.Name" xml:space="preserve">
    <value>label_M1</value>
  </data>
  <data name="&gt;&gt;label_M1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_M1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_M1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label_S1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_S1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_S1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label_S1.Location" type="System.Drawing.Point, System.Drawing">
    <value>325, 466</value>
  </data>
  <data name="label_S1.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_S1.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="label_S1.Text" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="&gt;&gt;label_S1.Name" xml:space="preserve">
    <value>label_S1</value>
  </data>
  <data name="&gt;&gt;label_S1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_S1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_S1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label_M2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_M2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_M2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label_M2.Location" type="System.Drawing.Point, System.Drawing">
    <value>576, 466</value>
  </data>
  <data name="label_M2.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_M2.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="label_M2.Text" xml:space="preserve">
    <value>分</value>
  </data>
  <data name="&gt;&gt;label_M2.Name" xml:space="preserve">
    <value>label_M2</value>
  </data>
  <data name="&gt;&gt;label_M2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_M2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_M2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label_H2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_H2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_H2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label_H2.Location" type="System.Drawing.Point, System.Drawing">
    <value>465, 466</value>
  </data>
  <data name="label_H2.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_H2.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="label_H2.Text" xml:space="preserve">
    <value>时</value>
  </data>
  <data name="&gt;&gt;label_H2.Name" xml:space="preserve">
    <value>label_H2</value>
  </data>
  <data name="&gt;&gt;label_H2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_H2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_H2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label_S2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="label_S2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_S2.Location" type="System.Drawing.Point, System.Drawing">
    <value>679, 466</value>
  </data>
  <data name="label_S2.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 14</value>
  </data>
  <data name="label_S2.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="label_S2.Text" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="&gt;&gt;label_S2.Name" xml:space="preserve">
    <value>label_S2</value>
  </data>
  <data name="&gt;&gt;label_S2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_S2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label_S2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>991, 605</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>DataBase</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DataBase</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>