﻿#pragma checksum "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BC5CCE1F3D378E3ED0512744EFF75AAF1E04AB9C"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HalconAlgorithmManager.Dialogs {
    
    
    /// <summary>
    /// NewAlgorithmDialog
    /// </summary>
    public partial class NewAlgorithmDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 50 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EngineComboBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlgorithmTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PrecisionComboBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SpeedComboBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ImageSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ColorModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VersionTextBox;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CreatorTextBox;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreateTimeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VisionAlgorithmManager;component/dialogs/newalgorithmdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AlgorithmIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.AlgorithmNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.EngineComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.AlgorithmTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.PrecisionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.SpeedComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.ImageSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.ColorModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.VersionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.CreatorTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.CreateTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            
            #line 244 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Create_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 247 "..\..\..\..\Dialogs\NewAlgorithmDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

