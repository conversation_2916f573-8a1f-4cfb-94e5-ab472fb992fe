using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using CameraTechVerify.Interfaces;
using CameraTechVerify.Implementations;
using CameraTechVerify.Models;
using CameraTechVerify.Test;
using Microsoft.Win32;

namespace CameraTechVerify.Windows
{
    /// <summary>
    /// MVS 相机控制窗体
    /// </summary>
    public partial class CameraControlWindow : Window
    {
        #region 私有字段

        private ICameraController _cameraController;
        private List<CameraDeviceInfo> _availableDevices;
        private long _totalImageCount = 0;
        private DateTime _lastImageTime = DateTime.MinValue;
        private DispatcherTimer _statsTimer;
        private Bitmap _currentImage;

        #endregion

        #region 构造函数

        public CameraControlWindow()
        {
            InitializeComponent();
            InitializeSystem();
        }

        #endregion

        #region 初始化

        private void InitializeSystem()
        {
            try
            {
                // 创建 MVS 相机控制器
                _cameraController = new MVSCameraController();

                // 订阅事件
                _cameraController.ImageReceived += OnImageReceived;
                _cameraController.ConnectionStatusChanged += OnConnectionStatusChanged;
                _cameraController.ErrorOccurred += OnErrorOccurred;

                // 初始化统计定时器
                _statsTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(1)
                };
                _statsTimer.Tick += UpdateStatistics;
                _statsTimer.Start();

                // 初始化界面
                UpdateUI();
                LogMessage("MVS 相机控制器初始化完成");

                // 检查 SDK 状态
                CheckSDKStatus();

                // 自动刷新设备列表
                RefreshDevices();
            }
            catch (Exception ex)
            {
                LogMessage($"初始化失败: {ex.Message}");
                MessageBox.Show($"系统初始化失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CheckSDKStatus()
        {
            try
            {
                // 这里可以添加 SDK 状态检查逻辑
                SDKStatusText.Text = "已加载";
                SDKStatusText.Foreground = System.Windows.Media.Brushes.LightGreen;
            }
            catch
            {
                SDKStatusText.Text = "未检测到";
                SDKStatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        #endregion

        #region 设备管理事件

        private void RefreshDevicesBtn_Click(object sender, RoutedEventArgs e)
        {
            RefreshDevices();
        }

        private void DeviceTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 当设备类型改变时自动刷新设备列表
            if (DeviceTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                RefreshDevices();
            }
        }

        private void RefreshGigEBtn_Click(object sender, RoutedEventArgs e)
        {
            RefreshGigEDevices();
        }

        private void RefreshDevices()
        {
            try
            {
                // 检查相机控制器是否已初始化
                if (_cameraController == null)
                {
                    LogMessage("相机控制器未初始化，跳过设备刷新");
                    return;
                }

                LogMessage("正在刷新设备列表...");

                string deviceType = null;
                if (DeviceTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    deviceType = selectedItem.Tag?.ToString();
                }

                _availableDevices = string.IsNullOrEmpty(deviceType) ?
                    _cameraController.EnumerateDevices() :
                    _cameraController.EnumerateDevices(deviceType);

                UpdateDeviceList();
            }
            catch (Exception ex)
            {
                LogMessage($"刷新设备列表失败: {ex.Message}");
            }
        }

        private void RefreshGigEDevices()
        {
            try
            {
                // 检查相机控制器是否已初始化
                if (_cameraController == null)
                {
                    LogMessage("相机控制器未初始化，跳过 GigE 设备刷新");
                    return;
                }

                LogMessage("正在刷新 GigE 设备列表...");
                _availableDevices = _cameraController.EnumerateGigEDevices();
                UpdateDeviceList();
            }
            catch (Exception ex)
            {
                LogMessage($"刷新 GigE 设备列表失败: {ex.Message}");
            }
        }

        private void UpdateDeviceList()
        {
            DeviceComboBox.Items.Clear();
            foreach (var device in _availableDevices)
            {
                string displayText = $"[{device.DeviceIndex}] {device.DisplayName}";
                if (device.IsNetworkCamera)
                {
                    displayText += $" - {device.IpAddress}";
                }
                DeviceComboBox.Items.Add(displayText);
            }

            if (_availableDevices.Count > 0)
            {
                DeviceComboBox.SelectedIndex = 0;
                LogMessage($"找到 {_availableDevices.Count} 个设备");
            }
            else
            {
                LogMessage("未找到可用设备");
            }

            UpdateUI();
        }

        private void DeviceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateUI();
        }

        private void ConnectBtn_Click(object sender, RoutedEventArgs e)
        {
            ConnectToCamera();
        }

        private void DisconnectBtn_Click(object sender, RoutedEventArgs e)
        {
            DisconnectFromCamera();
        }

        private void PingBtn_Click(object sender, RoutedEventArgs e)
        {
            PingCamera();
        }

        private void ConnectByIPBtn_Click(object sender, RoutedEventArgs e)
        {
            ConnectByIP();
        }

        private void ConnectBySerialBtn_Click(object sender, RoutedEventArgs e)
        {
            ConnectBySerialNumber();
        }

        private void ConnectByMacBtn_Click(object sender, RoutedEventArgs e)
        {
            ConnectByMacAddress();
        }

        private void ForceIPBtn_Click(object sender, RoutedEventArgs e)
        {
            ForceIPConfiguration();
        }

        private void ConnectToCamera()
        {
            try
            {
                if (DeviceComboBox.SelectedIndex < 0 || DeviceComboBox.SelectedIndex >= _availableDevices.Count)
                {
                    MessageBox.Show("请选择一个设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var selectedDevice = _availableDevices[DeviceComboBox.SelectedIndex];
                LogMessage($"正在连接到设备: {selectedDevice.ModelName}");

                bool success = _cameraController.Connect(selectedDevice);
                if (success)
                {
                    LogMessage("设备连接成功");
                    UpdateDeviceInfo(selectedDevice);
                    ReadCurrentParameters();
                }
                else
                {
                    LogMessage("设备连接失败");
                }

                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"连接设备异常: {ex.Message}");
                MessageBox.Show($"连接设备失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PingCamera()
        {
            try
            {
                string ipAddress = IpAddressTextBox.Text.Trim();
                if (string.IsNullOrEmpty(ipAddress))
                {
                    MessageBox.Show("请输入 IP 地址", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                LogMessage($"正在 Ping {ipAddress}...");
                bool isReachable = _cameraController.PingCamera(ipAddress, 3000);

                if (isReachable)
                {
                    LogMessage($"✓ {ipAddress} 可达");
                    MessageBox.Show($"相机 {ipAddress} 可达", "Ping 结果", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    LogMessage($"✗ {ipAddress} 不可达");
                    MessageBox.Show($"相机 {ipAddress} 不可达，请检查网络连接", "Ping 结果", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Ping 异常: {ex.Message}");
            }
        }

        private void ConnectByIP()
        {
            try
            {
                string ipAddress = IpAddressTextBox.Text.Trim();
                if (string.IsNullOrEmpty(ipAddress))
                {
                    MessageBox.Show("请输入 IP 地址", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!int.TryParse(PortTextBox.Text, out int port))
                {
                    MessageBox.Show("请输入有效的端口号", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                LogMessage($"正在通过 IP 连接: {ipAddress}:{port}");
                bool success = _cameraController.ConnectByIP(ipAddress, port);

                if (success)
                {
                    LogMessage("IP 连接成功");
                    UpdateDeviceInfo(_cameraController.DeviceInfo);
                    ReadCurrentParameters();
                }
                else
                {
                    LogMessage("IP 连接失败");
                }

                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"IP 连接异常: {ex.Message}");
                MessageBox.Show($"IP 连接失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConnectBySerialNumber()
        {
            try
            {
                string serialNumber = SerialNumberTextBox.Text.Trim();
                if (string.IsNullOrEmpty(serialNumber))
                {
                    MessageBox.Show("请输入序列号", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                LogMessage($"正在通过序列号连接: {serialNumber}");
                bool success = _cameraController.ConnectBySerialNumber(serialNumber);

                if (success)
                {
                    LogMessage("序列号连接成功");
                    UpdateDeviceInfo(_cameraController.DeviceInfo);
                    ReadCurrentParameters();
                }
                else
                {
                    LogMessage("序列号连接失败");
                }

                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"序列号连接异常: {ex.Message}");
                MessageBox.Show($"序列号连接失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConnectByMacAddress()
        {
            try
            {
                string macAddress = MacAddressTextBox.Text.Trim();
                if (string.IsNullOrEmpty(macAddress))
                {
                    MessageBox.Show("请输入 MAC 地址", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                LogMessage($"正在通过 MAC 地址连接: {macAddress}");
                bool success = _cameraController.ConnectByMacAddress(macAddress);

                if (success)
                {
                    LogMessage("MAC 地址连接成功");
                    UpdateDeviceInfo(_cameraController.DeviceInfo);
                    ReadCurrentParameters();
                }
                else
                {
                    LogMessage("MAC 地址连接失败");
                }

                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"MAC 地址连接异常: {ex.Message}");
                MessageBox.Show($"MAC 地址连接失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ForceIPConfiguration()
        {
            try
            {
                string macAddress = ForceIPMacTextBox.Text.Trim();
                string ipAddress = ForceIPAddressTextBox.Text.Trim();
                string subnetMask = ForceSubnetMaskTextBox.Text.Trim();
                string gateway = ForceGatewayTextBox.Text.Trim();

                if (string.IsNullOrEmpty(macAddress) || string.IsNullOrEmpty(ipAddress))
                {
                    MessageBox.Show("MAC 地址和 IP 地址不能为空", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"确定要强制配置以下 IP 设置吗？\n\n" +
                    $"MAC 地址: {macAddress}\n" +
                    $"IP 地址: {ipAddress}\n" +
                    $"子网掩码: {subnetMask}\n" +
                    $"网关: {gateway}\n\n" +
                    $"此操作将修改相机的网络配置！",
                    "确认强制 IP 配置",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    LogMessage($"正在强制配置 IP: {macAddress} -> {ipAddress}");
                    bool success = _cameraController.ForceIP(macAddress, ipAddress, subnetMask, gateway);

                    if (success)
                    {
                        LogMessage("强制 IP 配置成功");
                        MessageBox.Show("IP 配置成功！请等待相机重启后重新连接。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        LogMessage("强制 IP 配置失败");
                        MessageBox.Show("IP 配置失败，请检查 MAC 地址是否正确。", "失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"强制 IP 配置异常: {ex.Message}");
                MessageBox.Show($"强制 IP 配置失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        private void DisconnectFromCamera()
        {
            try
            {
                LogMessage("正在断开设备连接...");
                _cameraController.Disconnect();
                LogMessage("设备已断开连接");
                ClearDeviceInfo();
                ClearImage();
                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"断开连接异常: {ex.Message}");
            }
        }

        #endregion

        #region 图像采集事件

        private void StartGrabbingBtn_Click(object sender, RoutedEventArgs e)
        {
            StartGrabbing();
        }

        private void StopGrabbingBtn_Click(object sender, RoutedEventArgs e)
        {
            StopGrabbing();
        }

        private void CaptureBtn_Click(object sender, RoutedEventArgs e)
        {
            CaptureImage();
        }

        private void SaveImageBtn_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentImage();
        }

        private void StartGrabbing()
        {
            try
            {
                LogMessage("开始连续采集...");
                bool success = _cameraController.StartGrabbing();
                if (success)
                {
                    LogMessage("连续采集已开始");
                }
                else
                {
                    LogMessage("开始连续采集失败");
                }
                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"开始采集异常: {ex.Message}");
            }
        }

        private void StopGrabbing()
        {
            try
            {
                LogMessage("停止连续采集...");
                bool success = _cameraController.StopGrabbing();
                if (success)
                {
                    LogMessage("连续采集已停止");
                }
                else
                {
                    LogMessage("停止连续采集失败");
                }
                UpdateUI();
            }
            catch (Exception ex)
            {
                LogMessage($"停止采集异常: {ex.Message}");
            }
        }

        private async void CaptureImage()
        {
            try
            {
                LogMessage("正在拍照...");
                var image = await _cameraController.CaptureImageAsync();
                if (image != null)
                {
                    DisplayImage(image);
                    LogMessage($"拍照成功 - 尺寸: {image.Width}x{image.Height}");
                }
                else
                {
                    LogMessage("拍照失败");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"拍照异常: {ex.Message}");
            }
        }

        private void SaveCurrentImage()
        {
            try
            {
                if (_currentImage == null)
                {
                    MessageBox.Show("没有可保存的图像", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "PNG 图像|*.png|JPEG 图像|*.jpg|BMP 图像|*.bmp|所有文件|*.*",
                    DefaultExt = "png",
                    FileName = $"Camera_Image_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    _currentImage.Save(saveDialog.FileName);
                    LogMessage($"图像已保存: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"保存图像失败: {ex.Message}");
                MessageBox.Show($"保存图像失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 参数控制事件

        private void SetExposureBtn_Click(object sender, RoutedEventArgs e)
        {
            SetExposureTime();
        }

        private void SetGainBtn_Click(object sender, RoutedEventArgs e)
        {
            SetGain();
        }

        private void SetFrameRateBtn_Click(object sender, RoutedEventArgs e)
        {
            SetFrameRate();
        }

        private void TriggerModeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SetTriggerMode();
        }

        private void SoftwareTriggerBtn_Click(object sender, RoutedEventArgs e)
        {
            ExecuteSoftwareTrigger();
        }

        private void ReadParametersBtn_Click(object sender, RoutedEventArgs e)
        {
            ReadCurrentParameters();
        }

        private void SaveParametersBtn_Click(object sender, RoutedEventArgs e)
        {
            SaveParameters();
        }

        private void LoadParametersBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadParameters();
        }

        private void SetExposureTime()
        {
            try
            {
                if (float.TryParse(ExposureTimeTextBox.Text, out float exposureTime))
                {
                    bool success = _cameraController.SetExposureTime(exposureTime);
                    if (success)
                    {
                        LogMessage($"曝光时间设置成功: {exposureTime} μs");
                        UpdateCurrentParameters();
                    }
                    else
                    {
                        LogMessage("曝光时间设置失败");
                    }
                }
                else
                {
                    MessageBox.Show("请输入有效的曝光时间值", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"设置曝光时间异常: {ex.Message}");
            }
        }

        private void SetGain()
        {
            try
            {
                if (float.TryParse(GainTextBox.Text, out float gain))
                {
                    bool success = _cameraController.SetGain(gain);
                    if (success)
                    {
                        LogMessage($"增益设置成功: {gain} dB");
                        UpdateCurrentParameters();
                    }
                    else
                    {
                        LogMessage("增益设置失败");
                    }
                }
                else
                {
                    MessageBox.Show("请输入有效的增益值", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"设置增益异常: {ex.Message}");
            }
        }

        private void SetFrameRate()
        {
            try
            {
                if (float.TryParse(FrameRateTextBox.Text, out float frameRate))
                {
                    bool success = _cameraController.SetFrameRate(frameRate);
                    if (success)
                    {
                        LogMessage($"帧率设置成功: {frameRate} fps");
                        UpdateCurrentParameters();
                    }
                    else
                    {
                        LogMessage("帧率设置失败");
                    }
                }
                else
                {
                    MessageBox.Show("请输入有效的帧率值", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"设置帧率异常: {ex.Message}");
            }
        }

        private void SetTriggerMode()
        {
            try
            {
                if (_cameraController == null || !_cameraController.IsConnected)
                    return;

                if (TriggerModeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    string modeTag = selectedItem.Tag?.ToString() ?? "Continuous";
                    TriggerMode triggerMode = Enum.Parse<TriggerMode>(modeTag);

                    bool success = _cameraController.SetTriggerMode(triggerMode);
                    if (success)
                    {
                        LogMessage($"触发模式设置成功: {selectedItem.Content}");
                        UpdateCurrentParameters();
                        UpdateUI(); // 更新软件触发按钮状态
                    }
                    else
                    {
                        LogMessage("触发模式设置失败");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"设置触发模式异常: {ex.Message}");
            }
        }

        private void ExecuteSoftwareTrigger()
        {
            try
            {
                bool success = _cameraController.SoftwareTrigger();
                if (success)
                {
                    LogMessage("软件触发执行成功");
                }
                else
                {
                    LogMessage("软件触发执行失败");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"软件触发异常: {ex.Message}");
            }
        }

        private void ReadCurrentParameters()
        {
            try
            {
                if (!_cameraController.IsConnected)
                {
                    return;
                }

                LogMessage("正在读取当前参数...");

                // 读取参数并更新界面
                float exposure = _cameraController.GetExposureTime();
                float gain = _cameraController.GetGain();
                float frameRate = _cameraController.GetFrameRate();
                var resolution = _cameraController.GetResolution();
                var triggerMode = _cameraController.GetTriggerMode();

                // 更新界面显示
                ExposureTimeTextBox.Text = exposure.ToString("F1");
                GainTextBox.Text = gain.ToString("F1");
                FrameRateTextBox.Text = frameRate.ToString("F1");

                // 更新触发模式选择
                foreach (ComboBoxItem item in TriggerModeComboBox.Items)
                {
                    if (item.Tag?.ToString() == triggerMode.ToString())
                    {
                        TriggerModeComboBox.SelectedItem = item;
                        break;
                    }
                }

                UpdateCurrentParameters();
                LogMessage("参数读取完成");
            }
            catch (Exception ex)
            {
                LogMessage($"读取参数异常: {ex.Message}");
            }
        }

        #endregion

        #region SDK 分析和辅助方法

        private void AnalyzeAPIBtn_Click(object sender, RoutedEventArgs e)
        {
            AnalyzeAPI();
        }

        private void GenerateReportBtn_Click(object sender, RoutedEventArgs e)
        {
            GenerateAPIReport();
        }

        private void TestBasicBtn_Click(object sender, RoutedEventArgs e)
        {
            TestBasicFunctionality();
        }

        private void SaveParameters()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "相机配置文件|*.xml|所有文件|*.*",
                    DefaultExt = "xml",
                    FileName = $"Camera_Config_{DateTime.Now:yyyyMMdd_HHmmss}.xml"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    bool success = _cameraController.SaveParametersToFile(saveDialog.FileName);
                    if (success)
                    {
                        LogMessage($"参数配置已保存: {saveDialog.FileName}");
                    }
                    else
                    {
                        LogMessage("保存参数配置失败");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"保存参数配置异常: {ex.Message}");
            }
        }

        private void LoadParameters()
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "相机配置文件|*.xml|所有文件|*.*",
                    DefaultExt = "xml"
                };

                if (openDialog.ShowDialog() == true)
                {
                    bool success = _cameraController.LoadParametersFromFile(openDialog.FileName);
                    if (success)
                    {
                        LogMessage($"参数配置已加载: {openDialog.FileName}");
                        ReadCurrentParameters(); // 重新读取参数更新界面
                    }
                    else
                    {
                        LogMessage("加载参数配置失败");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"加载参数配置异常: {ex.Message}");
            }
        }

        private void AnalyzeAPI()
        {
            try
            {
                LogMessage("正在分析 MVS API...");

                // 在后台线程中执行 API 分析
                Task.Run(() =>
                {
                    try
                    {
                        MVSApiExplorer.ExploreAPI();

                        Dispatcher.Invoke(() =>
                        {
                            LogMessage("MVS API 分析完成，请查看控制台输出");
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            LogMessage($"API 分析失败: {ex.Message}");
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage($"启动 API 分析失败: {ex.Message}");
            }
        }

        private void GenerateAPIReport()
        {
            try
            {
                LogMessage("正在生成 API 报告...");

                Task.Run(() =>
                {
                    try
                    {
                        MVSApiExplorer.GenerateAPIReport();

                        Dispatcher.Invoke(() =>
                        {
                            LogMessage("API 报告生成完成，已保存到桌面");
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            LogMessage($"生成 API 报告失败: {ex.Message}");
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage($"启动报告生成失败: {ex.Message}");
            }
        }

        private void TestBasicFunctionality()
        {
            try
            {
                LogMessage("正在测试基本功能...");

                Task.Run(() =>
                {
                    try
                    {
                        MVSApiExplorer.TestBasicFunctionality();

                        Dispatcher.Invoke(() =>
                        {
                            LogMessage("基本功能测试完成，请查看控制台输出");
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            LogMessage($"基本功能测试失败: {ex.Message}");
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage($"启动功能测试失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        private void OnImageReceived(object sender, ImageReceivedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    DisplayImage(e.Image);
                    _totalImageCount++;
                    _lastImageTime = DateTime.Now;

                    LogMessage($"接收图像 #{e.ImageNumber} - {e.Width}x{e.Height}");
                });
            }
            catch (Exception ex)
            {
                LogMessage($"处理接收图像异常: {ex.Message}");
            }
        }

        private void OnConnectionStatusChanged(object sender, ConnectionStatusChangedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    UpdateConnectionStatus(e.Status, e.Message);
                    LogMessage($"连接状态: {e.Message}");
                    UpdateUI();
                });
            }
            catch (Exception ex)
            {
                LogMessage($"处理连接状态变化异常: {ex.Message}");
            }
        }

        private void OnErrorOccurred(object sender, ErrorOccurredEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    LogMessage($"错误 [{e.ErrorCode}]: {e.ErrorMessage}");

                    // 对于严重错误，显示消息框
                    if (e.ErrorCode < 0 && !e.ErrorMessage.Contains("需要根据实际"))
                    {
                        MessageBox.Show($"相机错误:\n\n{e.ErrorMessage}", "相机错误",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage($"处理错误事件异常: {ex.Message}");
            }
        }

        #endregion

        #region 界面更新方法

        private void UpdateUI()
        {
            try
            {
                bool isConnected = _cameraController?.IsConnected ?? false;
                bool isGrabbing = _cameraController?.IsGrabbing ?? false;
                bool hasDevices = _availableDevices?.Count > 0;
                bool hasSelectedDevice = DeviceComboBox.SelectedIndex >= 0;

                // 设备管理按钮
                ConnectBtn.IsEnabled = hasDevices && hasSelectedDevice && !isConnected;
                DisconnectBtn.IsEnabled = isConnected;

                // 图像采集按钮
                StartGrabbingBtn.IsEnabled = isConnected && !isGrabbing;
                StopGrabbingBtn.IsEnabled = isConnected && isGrabbing;
                CaptureBtn.IsEnabled = isConnected;
                SaveImageBtn.IsEnabled = _currentImage != null;

                // 参数控制
                var parameterControls = new Control[]
                {
                    ExposureTimeTextBox, GainTextBox, FrameRateTextBox,
                    TriggerModeComboBox
                };

                var parameterButtons = new Button[]
                {
                    (Button)FindName("SetExposureBtn"),
                    (Button)FindName("SetGainBtn"),
                    (Button)FindName("SetFrameRateBtn")
                };

                foreach (var control in parameterControls)
                {
                    if (control != null)
                        control.IsEnabled = isConnected;
                }

                foreach (var button in parameterButtons)
                {
                    if (button != null)
                        button.IsEnabled = isConnected;
                }

                // 软件触发按钮
                var triggerMode = TriggerModeComboBox.SelectedItem as ComboBoxItem;
                bool isSoftwareTrigger = triggerMode?.Tag?.ToString() == "Software";
                SoftwareTriggerBtn.IsEnabled = isConnected && isSoftwareTrigger;

                // 参数操作按钮
                var paramOperationButtons = new string[]
                {
                    "ReadParametersBtn", "SaveParametersBtn", "LoadParametersBtn"
                };

                foreach (var buttonName in paramOperationButtons)
                {
                    var button = FindName(buttonName) as Button;
                    if (button != null)
                        button.IsEnabled = isConnected;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新界面异常: {ex.Message}");
            }
        }

        private void UpdateConnectionStatus(ConnectionStatus status, string message)
        {
            try
            {
                ConnectionStatusText.Text = message;

                switch (status)
                {
                    case ConnectionStatus.Connected:
                        ConnectionStatusText.Foreground = System.Windows.Media.Brushes.LightGreen;
                        StatusText.Text = "相机已连接";
                        break;
                    case ConnectionStatus.Connecting:
                        ConnectionStatusText.Foreground = System.Windows.Media.Brushes.Yellow;
                        StatusText.Text = "正在连接...";
                        break;
                    case ConnectionStatus.Disconnected:
                        ConnectionStatusText.Foreground = System.Windows.Media.Brushes.LightGray;
                        StatusText.Text = "相机未连接";
                        break;
                    case ConnectionStatus.Failed:
                        ConnectionStatusText.Foreground = System.Windows.Media.Brushes.Red;
                        StatusText.Text = "连接失败";
                        break;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新连接状态异常: {ex.Message}");
            }
        }

        private void UpdateDeviceInfo(CameraDeviceInfo deviceInfo)
        {
            try
            {
                if (deviceInfo != null)
                {
                    DeviceNameText.Text = $"设备名称: {deviceInfo.DeviceName ?? "未知"}";
                    ModelNameText.Text = $"型号: {deviceInfo.ModelName ?? "未知"}";
                    SerialNumberText.Text = $"序列号: {deviceInfo.SerialNumber ?? "未知"}";
                    ConnectionTypeText.Text = $"连接类型: {deviceInfo.ConnectionType ?? "未知"}";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新设备信息异常: {ex.Message}");
            }
        }

        private void ClearDeviceInfo()
        {
            try
            {
                DeviceNameText.Text = "设备名称: 未连接";
                ModelNameText.Text = "型号: 未知";
                SerialNumberText.Text = "序列号: 未知";
                ConnectionTypeText.Text = "连接类型: 未知";

                ClearCurrentParameters();
            }
            catch (Exception ex)
            {
                LogMessage($"清除设备信息异常: {ex.Message}");
            }
        }

        private void UpdateCurrentParameters()
        {
            try
            {
                if (_cameraController?.IsConnected == true)
                {
                    CurrentExposureText.Text = $"曝光: {ExposureTimeTextBox.Text} μs";
                    CurrentGainText.Text = $"增益: {GainTextBox.Text} dB";
                    CurrentFrameRateText.Text = $"帧率: {FrameRateTextBox.Text} fps";

                    var resolution = _cameraController.GetResolution();
                    CurrentResolutionText.Text = $"分辨率: {resolution.Width}x{resolution.Height}";

                    var triggerItem = TriggerModeComboBox.SelectedItem as ComboBoxItem;
                    CurrentTriggerModeText.Text = $"触发模式: {triggerItem?.Content ?? "未知"}";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新当前参数异常: {ex.Message}");
            }
        }

        private void ClearCurrentParameters()
        {
            try
            {
                CurrentExposureText.Text = "曝光: 未知";
                CurrentGainText.Text = "增益: 未知";
                CurrentFrameRateText.Text = "帧率: 未知";
                CurrentResolutionText.Text = "分辨率: 未知";
                CurrentTriggerModeText.Text = "触发模式: 未知";
            }
            catch (Exception ex)
            {
                LogMessage($"清除当前参数异常: {ex.Message}");
            }
        }

        #endregion

        #region 图像处理和显示

        private void DisplayImage(Bitmap bitmap)
        {
            try
            {
                if (bitmap == null)
                {
                    ClearImage();
                    return;
                }

                _currentImage = new Bitmap(bitmap); // 创建副本

                // 转换为 WPF 可显示的格式
                using (var memory = new MemoryStream())
                {
                    bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                    memory.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.StreamSource = memory;
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze();

                    CameraImage.Source = bitmapImage;
                    NoImageText.Visibility = Visibility.Hidden;
                    CameraImage.Visibility = Visibility.Visible;

                    // 更新图像信息
                    ImageInfoText.Text = $"图像尺寸: {bitmap.Width}x{bitmap.Height} | 格式: {bitmap.PixelFormat}";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"显示图像异常: {ex.Message}");
            }
        }

        private void ClearImage()
        {
            try
            {
                CameraImage.Source = null;
                CameraImage.Visibility = Visibility.Hidden;
                NoImageText.Visibility = Visibility.Visible;
                ImageInfoText.Text = "未获取图像";

                _currentImage?.Dispose();
                _currentImage = null;
            }
            catch (Exception ex)
            {
                LogMessage($"清除图像异常: {ex.Message}");
            }
        }

        #endregion

        #region 统计和日志

        private void UpdateStatistics(object sender, EventArgs e)
        {
            try
            {
                TotalImagesText.Text = $"总图像数: {_totalImageCount}";

                if (_lastImageTime != DateTime.MinValue)
                {
                    var timeDiff = DateTime.Now - _lastImageTime;
                    if (timeDiff.TotalSeconds < 60)
                    {
                        FrameRateStatsText.Text = "实际帧率: 活跃";
                    }
                    else
                    {
                        FrameRateStatsText.Text = "实际帧率: 0 fps";
                    }

                    LastImageTimeText.Text = $"最后图像: {_lastImageTime:HH:mm:ss}";
                }
                else
                {
                    FrameRateStatsText.Text = "实际帧率: 0 fps";
                    LastImageTimeText.Text = "最后图像: 无";
                }

                ImageCountText.Text = $"图像计数: {_totalImageCount}";
            }
            catch (Exception ex)
            {
                LogMessage($"更新统计信息异常: {ex.Message}");
            }
        }

        private void LogMessage(string message)
        {
            try
            {
                // 在初始化阶段使用 Debug 输出，避免 UI 空引用异常
                System.Diagnostics.Debug.WriteLine($"[UI] {message}");
                Console.WriteLine($"[UI] {message}");

                Dispatcher.Invoke(() =>
                {
                    // 检查 LogTextBox 是否已初始化
                    if (LogTextBox == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[UI] LogTextBox 未初始化，跳过 UI 更新: {message}");
                        return;
                    }

                    string timestamp = DateTime.Now.ToString("HH:mm:ss");
                    string logEntry = $"[{timestamp}] {message}";

                    LogTextBox.Text += logEntry + Environment.NewLine;

                    // 保持日志在合理长度
                    var lines = LogTextBox.Text.Split('\n');
                    if (lines.Length > 100)
                    {
                        LogTextBox.Text = string.Join("\n", lines.Skip(lines.Length - 80));
                    }

                    // 滚动到底部
                    LogTextBox.ScrollToEnd();
                });
            }
            catch
            {
                // 忽略日志记录异常
            }
        }

        #endregion

        #region 窗体生命周期

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 停止统计定时器
                _statsTimer?.Stop();

                // 断开相机连接
                if (_cameraController?.IsConnected == true)
                {
                    _cameraController.Disconnect();
                }

                // 释放资源
                _cameraController?.Dispose();
                _currentImage?.Dispose();

                LogMessage("应用程序正在关闭...");
            }
            catch (Exception ex)
            {
                LogMessage($"关闭应用程序异常: {ex.Message}");
            }

            base.OnClosing(e);
        }

        #endregion
    }
}
