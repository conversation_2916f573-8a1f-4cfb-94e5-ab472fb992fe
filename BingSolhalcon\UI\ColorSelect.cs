﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.LookAndFeel;


namespace BingSolhalcon.UI
{
    public partial class ColorSelect : RibbonForm
    {
        public delegate void delegateSelectColor(string color);

        public event delegateSelectColor evenSelectColor;
        public ColorSelect()
        {
            InitializeComponent();
            
        }

        private void Btn_ColorSelect_Click(object sender, EventArgs e)
        {
            evenSelectColor(cbBox_Color.Text);
            this.Close();
        }

        private void ColorSelect_Load(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(ColorSelect));
        }
    }
}
