﻿#pragma checksum "..\..\..\..\Views\AlgorithmTestView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A9E7351D65F714D96C7B29759898F1D7D8FAF56D"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HalconAlgorithmManager.Views {
    
    
    /// <summary>
    /// AlgorithmTestView
    /// </summary>
    public partial class AlgorithmTestView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 65 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageCountText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox TestImageListBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentImageText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CurrentTestImage;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultTestImagePanel;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TestResultOverlay;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestResultText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestValueText;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestTimeText;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TestResultPanel;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TestAlgorithmComboBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ToleranceModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CustomTolerancePanel;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomUpperToleranceTextBox;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomLowerToleranceTextBox;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomWarningTextBox;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BatchTestPanel;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton StrictModeRadio;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RelaxedModeRadio;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CustomModeRadio;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ToleranceFactorSlider;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToleranceFactorText;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TestScenarioComboBox;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTestText;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PassedTestText;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailedTestText;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarningTestText;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PassRateText;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgTimeText;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaxTimeText;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinTimeText;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StabilityText;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccuracyText;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RepeatabilityText;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReliabilityGradeText;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityScoreText;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar QualityProgressBar;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RepeatCountComboBox;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ConcurrentCountComboBox;
        
        #line default
        #line hidden
        
        
        #line 442 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar TestProgressBar;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestProgressText;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider TestSpeedSlider;
        
        #line default
        #line hidden
        
        
        #line 454 "..\..\..\..\Views\AlgorithmTestView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestSpeedText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VisionAlgorithmManager;component/views/algorithmtestview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlgorithmTestView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadTestImages_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 33 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StartTest_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 36 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateReport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ImageCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TestImageListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 71 "..\..\..\..\Views\AlgorithmTestView.xaml"
            this.TestImageListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TestImageListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 110 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddTestImage_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 113 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveTestImage_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 116 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearTestImages_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CurrentImageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CurrentTestImage = ((System.Windows.Controls.Image)(target));
            return;
            case 11:
            this.DefaultTestImagePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.TestResultOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.TestResultText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TestValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TestTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TestResultPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.TestAlgorithmComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.ToleranceModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 245 "..\..\..\..\Views\AlgorithmTestView.xaml"
            this.ToleranceModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ToleranceModeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CustomTolerancePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 20:
            this.CustomUpperToleranceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.CustomLowerToleranceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.CustomWarningTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.BatchTestPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 24:
            this.StrictModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 25:
            this.RelaxedModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 26:
            this.CustomModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 27:
            this.ToleranceFactorSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 28:
            this.ToleranceFactorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.TestScenarioComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 30:
            this.TotalTestText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.PassedTestText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.FailedTestText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.WarningTestText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.PassRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.AvgTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.MaxTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.MinTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.StabilityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.AccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.RepeatabilityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.ReliabilityGradeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.QualityScoreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.QualityProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 44:
            this.RepeatCountComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 45:
            this.ConcurrentCountComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 46:
            
            #line 431 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestAllImages_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 434 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SmartTest_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            
            #line 437 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PauseTest_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            
            #line 440 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StopTest_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.TestProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 51:
            this.TestProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.TestSpeedSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 53:
            this.TestSpeedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            
            #line 464 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportReport_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            
            #line 467 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportImages_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            
            #line 470 "..\..\..\..\Views\AlgorithmTestView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportData_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

