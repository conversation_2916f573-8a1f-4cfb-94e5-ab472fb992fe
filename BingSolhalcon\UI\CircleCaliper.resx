﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="hWindowControl1.LayoutBitmap" type="System.Resources.ResXNullRef, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value />
  </metadata>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="hWindowControl1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="hWindowControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="hWindowControl1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hWindowControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="hWindowControl1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="hWindowControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>551, 653</value>
  </data>
  <data name="hWindowControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Name" xml:space="preserve">
    <value>hWindowControl1</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Type" xml:space="preserve">
    <value>HalconDotNet.HWindowControl, halcondotnet, Version=19.11.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btn_drawcircle.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="btn_drawcircle.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btn_drawcircle.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 8</value>
  </data>
  <data name="btn_drawcircle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="btn_drawcircle.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 33</value>
  </data>
  <data name="btn_drawcircle.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btn_drawcircle.Text" xml:space="preserve">
    <value>画测量圆</value>
  </data>
  <data name="&gt;&gt;btn_drawcircle.Name" xml:space="preserve">
    <value>btn_drawcircle</value>
  </data>
  <data name="&gt;&gt;btn_drawcircle.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_drawcircle.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;btn_drawcircle.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Name" xml:space="preserve">
    <value>cennum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Name" xml:space="preserve">
    <value>cenmeasurethreshold_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Name" xml:space="preserve">
    <value>cenlineindex_cboBox</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Name" xml:space="preserve">
    <value>cenpolarity_cboBox</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Name" xml:space="preserve">
    <value>cenminscores_textBox</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Name" xml:space="preserve">
    <value>censigmal_textBox</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Name" xml:space="preserve">
    <value>cenmeasurewidth_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Name" xml:space="preserve">
    <value>censtdradius_textBox</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Name" xml:space="preserve">
    <value>cenmeasurelength_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;label18.Name" xml:space="preserve">
    <value>label18</value>
  </data>
  <data name="&gt;&gt;label18.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label18.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label18.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="groupBox2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 151</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>中心孔参数设置</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cennum_numericUpDown.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cennum_numericUpDown.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cennum_numericUpDown.Items2" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cennum_numericUpDown.Items3" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="cennum_numericUpDown.Items4" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="cennum_numericUpDown.Items5" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="cennum_numericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 97</value>
  </data>
  <data name="cennum_numericUpDown.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="cennum_numericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 20</value>
  </data>
  <data name="cennum_numericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cennum_numericUpDown.Text" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Name" xml:space="preserve">
    <value>cennum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cennum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Items2" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Items3" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Items4" xml:space="preserve">
    <value>120</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 46</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="cenmeasurethreshold_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="cenmeasurethreshold_textBox.Text" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Name" xml:space="preserve">
    <value>cenmeasurethreshold_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurethreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cenlineindex_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cenlineindex_cboBox.Items" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="cenlineindex_cboBox.Items1" xml:space="preserve">
    <value>last</value>
  </data>
  <data name="cenlineindex_cboBox.Items2" xml:space="preserve">
    <value>all</value>
  </data>
  <data name="cenlineindex_cboBox.Items3" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="cenlineindex_cboBox.Items4" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="cenlineindex_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 96</value>
  </data>
  <data name="cenlineindex_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="cenlineindex_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="cenlineindex_cboBox.Text" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Name" xml:space="preserve">
    <value>cenlineindex_cboBox</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenlineindex_cboBox.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cenpolarity_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cenpolarity_cboBox.Items" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="cenpolarity_cboBox.Items1" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="cenpolarity_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>108, 97</value>
  </data>
  <data name="cenpolarity_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="cenpolarity_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="cenpolarity_cboBox.Text" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Name" xml:space="preserve">
    <value>cenpolarity_cboBox</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenpolarity_cboBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cenminscores_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cenminscores_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 96</value>
  </data>
  <data name="cenminscores_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 21</value>
  </data>
  <data name="cenminscores_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="cenminscores_textBox.Text" xml:space="preserve">
    <value>0.7</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Name" xml:space="preserve">
    <value>cenminscores_textBox</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenminscores_textBox.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="censigmal_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="censigmal_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>204, 45</value>
  </data>
  <data name="censigmal_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 21</value>
  </data>
  <data name="censigmal_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="censigmal_textBox.Text" xml:space="preserve">
    <value>0.4</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Name" xml:space="preserve">
    <value>censigmal_textBox</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;censigmal_textBox.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cenmeasurewidth_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cenmeasurewidth_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 45</value>
  </data>
  <data name="cenmeasurewidth_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 21</value>
  </data>
  <data name="cenmeasurewidth_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="cenmeasurewidth_textBox.Text" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Name" xml:space="preserve">
    <value>cenmeasurewidth_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurewidth_textBox.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="censtdradius_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="censtdradius_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 124</value>
  </data>
  <data name="censtdradius_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 21</value>
  </data>
  <data name="censtdradius_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="censtdradius_textBox.Text" xml:space="preserve">
    <value>100</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Name" xml:space="preserve">
    <value>censtdradius_textBox</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;censtdradius_textBox.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cenmeasurelength_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cenmeasurelength_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 45</value>
  </data>
  <data name="cenmeasurelength_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 21</value>
  </data>
  <data name="cenmeasurelength_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="cenmeasurelength_textBox.Text" xml:space="preserve">
    <value>150</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Name" xml:space="preserve">
    <value>cenmeasurelength_textBox</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cenmeasurelength_textBox.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label4.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 13</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 29</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label6.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 19</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 23</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 71</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 33</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>205, 71</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 33</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label9.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 71</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 33</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="label9.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label18.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label18.Location" type="System.Drawing.Point, System.Drawing">
    <value>-4, 125</value>
  </data>
  <data name="label18.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 14</value>
  </data>
  <data name="label18.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>样轮直径</value>
  </data>
  <data name="label18.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label18.Name" xml:space="preserve">
    <value>label18</value>
  </data>
  <data name="&gt;&gt;label18.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label18.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label18.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label8.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 71</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 22</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label8.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label7.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 19</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 23</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label7.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="label5.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 19</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 23</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="btn_mtrsave.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="btn_mtrsave.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 47</value>
  </data>
  <data name="btn_mtrsave.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="btn_mtrsave.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 33</value>
  </data>
  <data name="btn_mtrsave.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="btn_mtrsave.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="&gt;&gt;btn_mtrsave.Name" xml:space="preserve">
    <value>btn_mtrsave</value>
  </data>
  <data name="&gt;&gt;btn_mtrsave.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_mtrsave.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;btn_mtrsave.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btn_test.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="btn_test.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 15</value>
  </data>
  <data name="btn_test.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="btn_test.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 39</value>
  </data>
  <data name="btn_test.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="btn_test.Text" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="&gt;&gt;btn_test.Name" xml:space="preserve">
    <value>btn_test</value>
  </data>
  <data name="&gt;&gt;btn_test.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_test.Parent" xml:space="preserve">
    <value>panel6</value>
  </data>
  <data name="&gt;&gt;btn_test.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Name" xml:space="preserve">
    <value>boltnum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Name" xml:space="preserve">
    <value>boltthreshold_textBox</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Name" xml:space="preserve">
    <value>boltstdradius8_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Name" xml:space="preserve">
    <value>boltstdradius7_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Name" xml:space="preserve">
    <value>boltstdradius6_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Name" xml:space="preserve">
    <value>boltstdradius5_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Name" xml:space="preserve">
    <value>boltstdradius4_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Name" xml:space="preserve">
    <value>boltstdradius3_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Name" xml:space="preserve">
    <value>boltstdradius2_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Name" xml:space="preserve">
    <value>boltlineindex_cboBox</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Name" xml:space="preserve">
    <value>boltpolarity_cboBox</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;label22.Name" xml:space="preserve">
    <value>label22</value>
  </data>
  <data name="&gt;&gt;label22.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label22.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label22.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Name" xml:space="preserve">
    <value>boltminscores_textBox</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Name" xml:space="preserve">
    <value>boltsigmal_textBox</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Name" xml:space="preserve">
    <value>boltmeasurewidth_textBox</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Name" xml:space="preserve">
    <value>boltstdradius1_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Name" xml:space="preserve">
    <value>boltmeasurelength_textBox</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;label20.Name" xml:space="preserve">
    <value>label20</value>
  </data>
  <data name="&gt;&gt;label20.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label20.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label20.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="groupBox3.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 166</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>螺栓孔参数设置</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="boltnum_numericUpDown.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="boltnum_numericUpDown.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="boltnum_numericUpDown.Items2" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="boltnum_numericUpDown.Items3" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="boltnum_numericUpDown.Items4" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="boltnum_numericUpDown.Items5" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="boltnum_numericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 101</value>
  </data>
  <data name="boltnum_numericUpDown.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="boltnum_numericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 20</value>
  </data>
  <data name="boltnum_numericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="boltnum_numericUpDown.Text" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Name" xml:space="preserve">
    <value>boltnum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltnum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="boltthreshold_textBox.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="boltthreshold_textBox.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="boltthreshold_textBox.Items2" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="boltthreshold_textBox.Items3" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="boltthreshold_textBox.Items4" xml:space="preserve">
    <value>120</value>
  </data>
  <data name="boltthreshold_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>314, 47</value>
  </data>
  <data name="boltthreshold_textBox.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="boltthreshold_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="boltthreshold_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="boltthreshold_textBox.Text" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Name" xml:space="preserve">
    <value>boltthreshold_textBox</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltthreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="boltstdradius8_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>367, 141</value>
  </data>
  <data name="boltstdradius8_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius8_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="boltstdradius8_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Name" xml:space="preserve">
    <value>boltstdradius8_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius8_textBox.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="boltstdradius7_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>316, 141</value>
  </data>
  <data name="boltstdradius7_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius7_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="boltstdradius7_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Name" xml:space="preserve">
    <value>boltstdradius7_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius7_textBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="boltstdradius6_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>260, 141</value>
  </data>
  <data name="boltstdradius6_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius6_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="boltstdradius6_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Name" xml:space="preserve">
    <value>boltstdradius6_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius6_textBox.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="boltstdradius5_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>204, 141</value>
  </data>
  <data name="boltstdradius5_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius5_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="boltstdradius5_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Name" xml:space="preserve">
    <value>boltstdradius5_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius5_textBox.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="boltstdradius4_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 141</value>
  </data>
  <data name="boltstdradius4_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius4_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="boltstdradius4_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Name" xml:space="preserve">
    <value>boltstdradius4_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius4_textBox.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="boltstdradius3_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 141</value>
  </data>
  <data name="boltstdradius3_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius3_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="boltstdradius3_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Name" xml:space="preserve">
    <value>boltstdradius3_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius3_textBox.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="boltstdradius2_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>51, 141</value>
  </data>
  <data name="boltstdradius2_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius2_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="boltstdradius2_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Name" xml:space="preserve">
    <value>boltstdradius2_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius2_textBox.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="boltlineindex_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltlineindex_cboBox.Items" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="boltlineindex_cboBox.Items1" xml:space="preserve">
    <value>last</value>
  </data>
  <data name="boltlineindex_cboBox.Items2" xml:space="preserve">
    <value>all</value>
  </data>
  <data name="boltlineindex_cboBox.Items3" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="boltlineindex_cboBox.Items4" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="boltlineindex_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 101</value>
  </data>
  <data name="boltlineindex_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="boltlineindex_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="boltlineindex_cboBox.Text" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Name" xml:space="preserve">
    <value>boltlineindex_cboBox</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltlineindex_cboBox.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="boltpolarity_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltpolarity_cboBox.Items" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="boltpolarity_cboBox.Items1" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="boltpolarity_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>123, 101</value>
  </data>
  <data name="boltpolarity_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="boltpolarity_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="boltpolarity_cboBox.Text" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Name" xml:space="preserve">
    <value>boltpolarity_cboBox</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltpolarity_cboBox.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label22.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label22.Location" type="System.Drawing.Point, System.Drawing">
    <value>313, 12</value>
  </data>
  <data name="label22.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 31</value>
  </data>
  <data name="label22.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label22.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label22.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label22.Name" xml:space="preserve">
    <value>label22</value>
  </data>
  <data name="&gt;&gt;label22.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label22.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label22.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="boltminscores_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltminscores_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>319, 101</value>
  </data>
  <data name="boltminscores_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 21</value>
  </data>
  <data name="boltminscores_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="boltminscores_textBox.Text" xml:space="preserve">
    <value>0.7</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Name" xml:space="preserve">
    <value>boltminscores_textBox</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltminscores_textBox.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="boltsigmal_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltsigmal_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>222, 45</value>
  </data>
  <data name="boltsigmal_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 21</value>
  </data>
  <data name="boltsigmal_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="boltsigmal_textBox.Text" xml:space="preserve">
    <value>0.4</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Name" xml:space="preserve">
    <value>boltsigmal_textBox</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltsigmal_textBox.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="boltmeasurewidth_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltmeasurewidth_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>121, 45</value>
  </data>
  <data name="boltmeasurewidth_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 21</value>
  </data>
  <data name="boltmeasurewidth_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="boltmeasurewidth_textBox.Text" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Name" xml:space="preserve">
    <value>boltmeasurewidth_textBox</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltmeasurewidth_textBox.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="boltstdradius1_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltstdradius1_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 141</value>
  </data>
  <data name="boltstdradius1_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 21</value>
  </data>
  <data name="boltstdradius1_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="boltstdradius1_textBox.Text" xml:space="preserve">
    <value>13.1</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Name" xml:space="preserve">
    <value>boltstdradius1_textBox</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltstdradius1_textBox.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="boltmeasurelength_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="boltmeasurelength_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 45</value>
  </data>
  <data name="boltmeasurelength_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 21</value>
  </data>
  <data name="boltmeasurelength_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="boltmeasurelength_textBox.Text" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Name" xml:space="preserve">
    <value>boltmeasurelength_textBox</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;boltmeasurelength_textBox.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="label11.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 17</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 21</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label11.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="label12.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 71</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 27</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label12.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="label13.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 71</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 27</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label13.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="label14.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>117, 71</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 27</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="label14.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="label15.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label15.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 71</value>
  </data>
  <data name="label15.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 27</value>
  </data>
  <data name="label15.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label15.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label20.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label20.Location" type="System.Drawing.Point, System.Drawing">
    <value>-2, 128</value>
  </data>
  <data name="label20.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 25</value>
  </data>
  <data name="label20.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label20.Text" xml:space="preserve">
    <value>样轮小孔直径</value>
  </data>
  <data name="&gt;&gt;label20.Name" xml:space="preserve">
    <value>label20</value>
  </data>
  <data name="&gt;&gt;label20.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label20.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label20.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="label16.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label16.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 17</value>
  </data>
  <data name="label16.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 26</value>
  </data>
  <data name="label16.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label16.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="label17.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label17.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 17</value>
  </data>
  <data name="label17.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 26</value>
  </data>
  <data name="label17.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label17.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="hatlineindex_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatlineindex_cboBox.Items" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="hatlineindex_cboBox.Items1" xml:space="preserve">
    <value>last</value>
  </data>
  <data name="hatlineindex_cboBox.Items2" xml:space="preserve">
    <value>all</value>
  </data>
  <data name="hatlineindex_cboBox.Items3" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="hatlineindex_cboBox.Items4" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="hatlineindex_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 101</value>
  </data>
  <data name="hatlineindex_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="hatlineindex_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="hatlineindex_cboBox.Text" xml:space="preserve">
    <value>first</value>
  </data>
  <data name="&gt;&gt;hatlineindex_cboBox.Name" xml:space="preserve">
    <value>hatlineindex_cboBox</value>
  </data>
  <data name="&gt;&gt;hatlineindex_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatlineindex_cboBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatlineindex_cboBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="hatpolarity_cboBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatpolarity_cboBox.Items" xml:space="preserve">
    <value>positive</value>
  </data>
  <data name="hatpolarity_cboBox.Items1" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="hatpolarity_cboBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 99</value>
  </data>
  <data name="hatpolarity_cboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="hatpolarity_cboBox.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="hatpolarity_cboBox.Text" xml:space="preserve">
    <value>negative</value>
  </data>
  <data name="&gt;&gt;hatpolarity_cboBox.Name" xml:space="preserve">
    <value>hatpolarity_cboBox</value>
  </data>
  <data name="&gt;&gt;hatpolarity_cboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatpolarity_cboBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatpolarity_cboBox.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="hatminscores_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatminscores_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>314, 99</value>
  </data>
  <data name="hatminscores_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 21</value>
  </data>
  <data name="hatminscores_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="hatminscores_textBox.Text" xml:space="preserve">
    <value>0.7</value>
  </data>
  <data name="&gt;&gt;hatminscores_textBox.Name" xml:space="preserve">
    <value>hatminscores_textBox</value>
  </data>
  <data name="&gt;&gt;hatminscores_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatminscores_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatminscores_textBox.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="hatsigmal_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatsigmal_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 43</value>
  </data>
  <data name="hatsigmal_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 21</value>
  </data>
  <data name="hatsigmal_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="hatsigmal_textBox.Text" xml:space="preserve">
    <value>0.8</value>
  </data>
  <data name="&gt;&gt;hatsigmal_textBox.Name" xml:space="preserve">
    <value>hatsigmal_textBox</value>
  </data>
  <data name="&gt;&gt;hatsigmal_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatsigmal_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatsigmal_textBox.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="hatmeasurewidth_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatmeasurewidth_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 43</value>
  </data>
  <data name="hatmeasurewidth_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 21</value>
  </data>
  <data name="hatmeasurewidth_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="hatmeasurewidth_textBox.Text" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;hatmeasurewidth_textBox.Name" xml:space="preserve">
    <value>hatmeasurewidth_textBox</value>
  </data>
  <data name="&gt;&gt;hatmeasurewidth_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatmeasurewidth_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatmeasurewidth_textBox.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="hatstdradius_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatstdradius_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 137</value>
  </data>
  <data name="hatstdradius_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 21</value>
  </data>
  <data name="hatstdradius_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="hatstdradius_textBox.Text" xml:space="preserve">
    <value>100</value>
  </data>
  <data name="&gt;&gt;hatstdradius_textBox.Name" xml:space="preserve">
    <value>hatstdradius_textBox</value>
  </data>
  <data name="&gt;&gt;hatstdradius_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatstdradius_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatstdradius_textBox.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="hatmeasurelength_textBox.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="hatmeasurelength_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 43</value>
  </data>
  <data name="hatmeasurelength_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 21</value>
  </data>
  <data name="hatmeasurelength_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="hatmeasurelength_textBox.Text" xml:space="preserve">
    <value>150</value>
  </data>
  <data name="&gt;&gt;hatmeasurelength_textBox.Name" xml:space="preserve">
    <value>hatmeasurelength_textBox</value>
  </data>
  <data name="&gt;&gt;hatmeasurelength_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatmeasurelength_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatmeasurelength_textBox.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label23.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label23.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 12</value>
  </data>
  <data name="label23.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 27</value>
  </data>
  <data name="label23.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label23.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label23.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label23.Name" xml:space="preserve">
    <value>label23</value>
  </data>
  <data name="&gt;&gt;label23.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label23.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label23.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label24.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label24.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 12</value>
  </data>
  <data name="label24.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 27</value>
  </data>
  <data name="label24.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label24.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label24.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label24.Name" xml:space="preserve">
    <value>label24</value>
  </data>
  <data name="&gt;&gt;label24.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label24.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label24.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label25.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label25.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 69</value>
  </data>
  <data name="label25.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 21</value>
  </data>
  <data name="label25.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label25.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label25.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label25.Name" xml:space="preserve">
    <value>label25</value>
  </data>
  <data name="&gt;&gt;label25.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label25.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label25.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label26.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label26.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 69</value>
  </data>
  <data name="label26.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 21</value>
  </data>
  <data name="label26.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label26.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label26.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label26.Name" xml:space="preserve">
    <value>label26</value>
  </data>
  <data name="&gt;&gt;label26.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label26.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label26.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label27.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label27.Location" type="System.Drawing.Point, System.Drawing">
    <value>107, 69</value>
  </data>
  <data name="label27.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 21</value>
  </data>
  <data name="label27.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label27.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="label27.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label27.Name" xml:space="preserve">
    <value>label27</value>
  </data>
  <data name="&gt;&gt;label27.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label27.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label27.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label28.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label28.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 132</value>
  </data>
  <data name="label28.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 26</value>
  </data>
  <data name="label28.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label28.Text" xml:space="preserve">
    <value>样轮直径</value>
  </data>
  <data name="label28.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label28.Name" xml:space="preserve">
    <value>label28</value>
  </data>
  <data name="&gt;&gt;label28.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label28.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label28.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label29.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label29.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 69</value>
  </data>
  <data name="label29.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 21</value>
  </data>
  <data name="label29.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label29.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label29.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label29.Name" xml:space="preserve">
    <value>label29</value>
  </data>
  <data name="&gt;&gt;label29.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label29.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label29.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label30.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label30.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 15</value>
  </data>
  <data name="label30.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 24</value>
  </data>
  <data name="label30.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label30.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label30.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label30.Name" xml:space="preserve">
    <value>label30</value>
  </data>
  <data name="&gt;&gt;label30.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label30.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label30.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label31.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label31.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 12</value>
  </data>
  <data name="label31.Size" type="System.Drawing.Size, System.Drawing">
    <value>107, 27</value>
  </data>
  <data name="label31.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label31.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label31.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label31.Name" xml:space="preserve">
    <value>label31</value>
  </data>
  <data name="&gt;&gt;label31.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label31.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;label31.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource" xml:space="preserve">
    <value>轮型识别</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource1" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource2" xml:space="preserve">
    <value>气门孔</value>
  </data>
  <data name="comboBox_selecttype.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="comboBox_selecttype.Items" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="comboBox_selecttype.Items1" xml:space="preserve">
    <value>螺栓孔</value>
  </data>
  <data name="comboBox_selecttype.Items2" xml:space="preserve">
    <value>帽止口</value>
  </data>
  <data name="comboBox_selecttype.Items3" xml:space="preserve">
    <value>轮型识别&amp;气门孔</value>
  </data>
  <data name="comboBox_selecttype.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 17</value>
  </data>
  <data name="comboBox_selecttype.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="comboBox_selecttype.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="comboBox_selecttype.Text" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="&gt;&gt;comboBox_selecttype.Name" xml:space="preserve">
    <value>comboBox_selecttype</value>
  </data>
  <data name="&gt;&gt;comboBox_selecttype.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBox_selecttype.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;comboBox_selecttype.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label19.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label19.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 9</value>
  </data>
  <data name="label19.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 32</value>
  </data>
  <data name="label19.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="label19.Text" xml:space="preserve">
    <value>模板类型</value>
  </data>
  <data name="label19.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label19.Name" xml:space="preserve">
    <value>label19</value>
  </data>
  <data name="&gt;&gt;label19.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label19.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;label19.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Name" xml:space="preserve">
    <value>hatnum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Name" xml:space="preserve">
    <value>hatmeasurethreshold_textBox</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBox5.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox5.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox5.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="groupBox5.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="groupBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 162</value>
  </data>
  <data name="groupBox5.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>帽止口参数设置</value>
  </data>
  <data name="&gt;&gt;groupBox5.Name" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;groupBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox5.Parent" xml:space="preserve">
    <value>panel5</value>
  </data>
  <data name="&gt;&gt;groupBox5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="hatnum_numericUpDown.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="hatnum_numericUpDown.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="hatnum_numericUpDown.Items2" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="hatnum_numericUpDown.Items3" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="hatnum_numericUpDown.Items4" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="hatnum_numericUpDown.Items5" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="hatnum_numericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 101</value>
  </data>
  <data name="hatnum_numericUpDown.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="hatnum_numericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 20</value>
  </data>
  <data name="hatnum_numericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="hatnum_numericUpDown.Text" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Name" xml:space="preserve">
    <value>hatnum_numericUpDown</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatnum_numericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Items" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Items1" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Items2" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Items3" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Items4" xml:space="preserve">
    <value>120</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 43</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="hatmeasurethreshold_textBox.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="hatmeasurethreshold_textBox.Text" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Name" xml:space="preserve">
    <value>hatmeasurethreshold_textBox</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="&gt;&gt;hatmeasurethreshold_textBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="comboBox_wheellist.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="comboBox_wheellist.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 65</value>
  </data>
  <data name="comboBox_wheellist.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="comboBox_wheellist.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="comboBox_wheellist.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;comboBox_wheellist.Name" xml:space="preserve">
    <value>comboBox_wheellist</value>
  </data>
  <data name="&gt;&gt;comboBox_wheellist.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBox_wheellist.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;comboBox_wheellist.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label3.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 55</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 36</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>轮毂型号</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox4.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="groupBox4.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 94</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btn_readimage.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="btn_readimage.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 15</value>
  </data>
  <data name="btn_readimage.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="btn_readimage.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 39</value>
  </data>
  <data name="btn_readimage.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="btn_readimage.Text" xml:space="preserve">
    <value>载入图片</value>
  </data>
  <data name="&gt;&gt;btn_readimage.Name" xml:space="preserve">
    <value>btn_readimage</value>
  </data>
  <data name="&gt;&gt;btn_readimage.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_readimage.Parent" xml:space="preserve">
    <value>panel6</value>
  </data>
  <data name="&gt;&gt;btn_readimage.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tableLayoutPanel1.ColumnCount" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="panel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>551, 653</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="panel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>557, 2</value>
  </data>
  <data name="panel2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 94</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel3.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>557, 100</value>
  </data>
  <data name="panel3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 151</value>
  </data>
  <data name="panel3.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;panel3.Name" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;panel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel3.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>557, 255</value>
  </data>
  <data name="panel4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 166</value>
  </data>
  <data name="panel4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;panel4.Name" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;panel4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel4.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panel5.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel5.Location" type="System.Drawing.Point, System.Drawing">
    <value>557, 425</value>
  </data>
  <data name="panel5.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel5.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 162</value>
  </data>
  <data name="panel5.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;panel5.Name" xml:space="preserve">
    <value>panel5</value>
  </data>
  <data name="&gt;&gt;panel5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel5.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel5.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="panel6.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel6.Location" type="System.Drawing.Point, System.Drawing">
    <value>557, 591</value>
  </data>
  <data name="panel6.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="panel6.Size" type="System.Drawing.Size, System.Drawing">
    <value>416, 64</value>
  </data>
  <data name="panel6.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;panel6.Name" xml:space="preserve">
    <value>panel6</value>
  </data>
  <data name="&gt;&gt;panel6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel6.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;panel6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tableLayoutPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="tableLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="tableLayoutPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="tableLayoutPanel1.RowCount" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tableLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>975, 657</value>
  </data>
  <data name="tableLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Name" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TableLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tableLayoutPanel1.LayoutSettings" type="System.Windows.Forms.TableLayoutSettings, System.Windows.Forms">
    <value>&lt;?xml version="1.0" encoding="utf-16"?&gt;&lt;TableLayoutSettings&gt;&lt;Controls&gt;&lt;Control Name="panel1" Row="0" RowSpan="5" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="panel2" Row="0" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="panel3" Row="1" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="panel4" Row="2" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="panel5" Row="3" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="panel6" Row="4" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;/Controls&gt;&lt;Columns Styles="Percent,100,Absolute,420" /&gt;&lt;Rows Styles="Percent,15,Percent,23.63083,Percent,25.96349,Percent,25.25355,Percent,10" /&gt;&lt;/TableLayoutSettings&gt;</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>975, 657</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>CircleCaliper</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>CircleCaliper</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>