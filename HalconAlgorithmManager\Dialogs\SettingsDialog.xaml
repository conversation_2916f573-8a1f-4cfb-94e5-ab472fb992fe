<Window x:Class="HalconAlgorithmManager.Dialogs.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="系统设置" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#FF2D2D30"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF1E1E1E" BorderBrush="#FF007ACC" BorderThickness="0,0,0,2" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚙️" FontSize="24" Foreground="#FF007ACC" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="系统设置" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="System Settings Configuration" FontSize="12" Foreground="LightGray" Margin="0,2,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主内容区 -->
        <TabControl Grid.Row="1" Background="#FF2D2D30" BorderThickness="0" Margin="20">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Background" Value="#FF3F3F46"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="Padding" Value="15,10"/>
                    <Setter Property="Margin" Value="2,0"/>
                    <Setter Property="FontSize" Value="12"/>
                </Style>
            </TabControl.Resources>

            <!-- 引擎配置 -->
            <TabItem Header="🔬 检测引擎">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="🔧 Halcon引擎配置" Foreground="White" Margin="0,10">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="安装路径:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <Grid Grid.Row="0" Grid.Column="1" Margin="10,8,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" x:Name="HalconPathTextBox" Height="25" 
                                            Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                            FontSize="11" Text="C:\Program Files\MVTec\HALCON-23.05-Progress" IsReadOnly="True"/>
                                    <Button Grid.Column="1" Content="📁" Width="30" Height="25" Margin="5,0,0,0"
                                           Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10"/>
                                </Grid>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="版本信息:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="Halcon 23.05 Progress Edition" 
                                          Foreground="LightGreen" FontSize="11" VerticalAlignment="Center" Margin="10,8,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="许可证状态:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="✅ 已激活 (到期时间: 2025-12-31)" 
                                          Foreground="LightGreen" FontSize="11" VerticalAlignment="Center" Margin="10,8,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="运行状态:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="10,8,0,8">
                                    <Ellipse Width="10" Height="10" Fill="LightGreen" VerticalAlignment="Center"/>
                                    <TextBlock Text="正常运行" Foreground="LightGreen" FontSize="11" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                    <Button Content="🔄 测试连接" Width="80" Height="20" Margin="20,0,0,0"
                                           Background="#FF007ACC" Foreground="White" BorderThickness="0" FontSize="9"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="🎯 OpenCV引擎配置" Foreground="White" Margin="0,10">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <CheckBox Grid.Row="0" Grid.ColumnSpan="2" Content="启用OpenCV引擎" IsChecked="True" 
                                         Foreground="White" FontSize="12" Margin="0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="版本信息:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="OpenCV 4.8.0" 
                                          Foreground="LightBlue" FontSize="11" VerticalAlignment="Center" Margin="10,8,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="运行状态:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="10,8,0,8">
                                    <Ellipse Width="10" Height="10" Fill="LightBlue" VerticalAlignment="Center"/>
                                    <TextBlock Text="可用" Foreground="LightBlue" FontSize="11" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="🔧 自定义引擎" Foreground="White" Margin="0,10">
                            <StackPanel Margin="15">
                                <CheckBox Content="启用自定义引擎支持" IsChecked="False" Foreground="White" FontSize="12" Margin="0,8"/>
                                <TextBlock Text="自定义引擎配置文件路径:" Foreground="LightGray" FontSize="11" Margin="0,8,0,5"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" Height="25" 
                                            Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                            FontSize="11" Text="" IsEnabled="False"/>
                                    <Button Grid.Column="1" Content="📁" Width="30" Height="25" Margin="5,0,0,0"
                                           Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10" IsEnabled="False"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 文件路径 -->
            <TabItem Header="📁 文件路径">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="📂 算法文件路径" Foreground="White" Margin="0,10">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="算法存储路径:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="D:\AlgorithmData\Algorithms"/>
                                <Button Grid.Row="0" Grid.Column="2" Content="📁" Width="30" Height="25" Margin="0,8"
                                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="图像存储路径:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="D:\AlgorithmData\Images"/>
                                <Button Grid.Row="1" Grid.Column="2" Content="📁" Width="30" Height="25" Margin="0,8"
                                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="配置文件路径:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="D:\AlgorithmData\Configs"/>
                                <Button Grid.Row="2" Grid.Column="2" Content="📁" Width="30" Height="25" Margin="0,8"
                                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="导出文件路径:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="D:\AlgorithmData\Exports"/>
                                <Button Grid.Row="3" Grid.Column="2" Content="📁" Width="30" Height="25" Margin="0,8"
                                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="10"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="🔄 自动备份设置" Foreground="White" Margin="0,10">
                            <StackPanel Margin="15">
                                <CheckBox Content="启用自动备份" IsChecked="True" Foreground="White" FontSize="12" Margin="0,8"/>
                                
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="备份间隔:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center"/>
                                    <ComboBox Grid.Column="1" Height="25" Margin="10,0,0,0"
                                             Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                                        <ComboBox.Resources>
                                            <Style TargetType="ComboBoxItem">
                                                <Setter Property="Background" Value="#FF3F3F46"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Style>
                                        </ComboBox.Resources>
                                        <ComboBoxItem Content="每小时" IsSelected="True"/>
                                        <ComboBoxItem Content="每天"/>
                                        <ComboBoxItem Content="每周"/>
                                    </ComboBox>
                                </Grid>

                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="保留备份数:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox Grid.Column="1" Height="25" Margin="10,0,0,0"
                                            Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                            FontSize="11" Text="10"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 性能设置 -->
            <TabItem Header="⚡ 性能设置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="🖥️ 处理器设置" Foreground="White" Margin="0,10">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="CPU核心数:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" Height="25" Margin="10,8,0,8"
                                         Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                                    <ComboBox.Resources>
                                        <Style TargetType="ComboBoxItem">
                                            <Setter Property="Background" Value="#FF3F3F46"/>
                                            <Setter Property="Foreground" Value="White"/>
                                        </Style>
                                    </ComboBox.Resources>
                                    <ComboBoxItem Content="自动检测"/>
                                    <ComboBoxItem Content="1核心"/>
                                    <ComboBoxItem Content="2核心"/>
                                    <ComboBoxItem Content="4核心" IsSelected="True"/>
                                    <ComboBoxItem Content="8核心"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="内存限制:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <ComboBox Grid.Row="1" Grid.Column="1" Height="25" Margin="10,8,0,8"
                                         Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                                    <ComboBox.Resources>
                                        <Style TargetType="ComboBoxItem">
                                            <Setter Property="Background" Value="#FF3F3F46"/>
                                            <Setter Property="Foreground" Value="White"/>
                                        </Style>
                                    </ComboBox.Resources>
                                    <ComboBoxItem Content="无限制" IsSelected="True"/>
                                    <ComboBoxItem Content="2GB"/>
                                    <ComboBoxItem Content="4GB"/>
                                    <ComboBoxItem Content="8GB"/>
                                </ComboBox>

                                <CheckBox Grid.Row="2" Grid.ColumnSpan="2" Content="启用GPU加速" IsChecked="False" 
                                         Foreground="White" FontSize="12" Margin="0,15,0,5"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="⏱️ 超时设置" Foreground="White" Margin="0,10">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="算法执行超时:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="30"/>
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="秒" Foreground="LightGray" FontSize="11" VerticalAlignment="Center"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="图像加载超时:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="10"/>
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="秒" Foreground="LightGray" FontSize="11" VerticalAlignment="Center"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="网络连接超时:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Height="25" Margin="10,8,5,8"
                                        Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                        FontSize="11" Text="5"/>
                                <TextBlock Grid.Row="2" Grid.Column="2" Text="秒" Foreground="LightGray" FontSize="11" VerticalAlignment="Center"/>
                            </Grid>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

        </TabControl>

        <!-- 按钮区域 -->
        <Border Grid.Row="2" Background="#FF3F3F46" BorderBrush="#FF5A5A5A" BorderThickness="0,1,0,0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="💾 保存设置" Width="100" Height="35" Margin="10,0"
                       Background="#FF28A745" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Save_Click"/>
                <Button Content="🔄 重置" Width="80" Height="35" Margin="10,0"
                       Background="#FFFF6B35" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Reset_Click"/>
                <Button Content="❌ 关闭" Width="80" Height="35" Margin="10,0"
                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
