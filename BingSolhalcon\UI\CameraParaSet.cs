﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace BingSolhalcon
{
    public partial class CameraParaSet : Form
    {


        //硬触发
        public delegate void delegateSetExternTrigger();

        public event delegateSetExternTrigger eventdelegateSetExternTrigger1, eventdelegateSetExternTrigger2, eventdelegateSetExternTrigger3;
        //软触发
        public delegate void delegateSetSoftTrigger();

        public event delegateSetSoftTrigger eventdelegateSetSoftTrigger1, eventdelegateSetSoftTrigger2, eventdelegateSetSoftTrigger3;
        //自由模式
        public delegate void delegateSetFreeRun();

        public event delegateSetFreeRun eventdelegateSetFreeRun1, eventdelegateSetFreeRun2, eventdelegateSetFreeRun3;
        //触发模式
        public delegate void delegateSetTrigger();

        public event delegateSetFreeRun eventdelegateSetTrigger1, eventdelegateSetTrigger2, eventdelegateSetTrigger3;

        //曝光
        public delegate void delegateSetExposure1_Scroll(int value);

        public event delegateSetExposure1_Scroll eventdelegateSetExposure1_Scroll1, eventdelegateSetExposure1_Scroll2, eventdelegateSetExposure1_Scroll3;

        //增益
        public delegate void delegateSetGain1_Scroll(int value);

        public event delegateSetGain1_Scroll eventdelegateSetGain1_Scroll1, eventdelegateSetGain1_Scroll2, eventdelegateSetGain1_Scroll3;
        //单张采集
        public delegate void delegateSingleFrame1();

        public event delegateSingleFrame1 eventdelegateSingleFrame1, eventdelegateSingleFrame2, eventdelegateSingleFrame3;
        //软触发执行
        public delegate void delegateSendSoftwareExecute1();

        public event delegateSendSoftwareExecute1 eventdelegateSoftwareExecute1, eventdelegateSoftwareExecute2, eventdelegateSoftwareExecute3, eventdelegateSoftwareExecute4;

        private void CameraParaSet_Load(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(CameraParaSet));
        }

     

        public CameraParaSet()
        {
            InitializeComponent();



        }



        //private void rdbHWTrigger1_CheckedChanged(object sender, EventArgs e)
        //{
        //    eventdelegateSetExternTrigger();
        //}
        //设置软触发
        //private void rdbSWTrigger1_CheckedChanged(object sender, EventArgs e)
        //{
        //    eventdelegateSetSoftTrigger();
        //}
        //




        //相机1的瀑光
        private void tkbExposure1_Scroll(object sender, EventArgs e)
        {
            int value = tkbExposure2.Value * 10;
            eventdelegateSetExposure1_Scroll1(value);
            txtBExposure2.Text = Convert.ToString(value);
        }
        //相机2的瀑光
        private void tkbExposure2_Scroll(object sender, EventArgs e)
        {
            int value = tkbExposure3.Value * 10;
            eventdelegateSetExposure1_Scroll2(value);
            txtBExposure3.Text = Convert.ToString(value);
        }
        //相机3的瀑光
        private void tkbExposure3_Scroll(object sender, EventArgs e)
        {
            int value = tkbExposure3.Value * 10;
            eventdelegateSetExposure1_Scroll3(value);
            txtBExposure3.Text = Convert.ToString(value);
        }


        //相机1的增益
        private void tkbGain1_Scroll(object sender, EventArgs e)
        {
            int value = tkbGain2.Value;
            eventdelegateSetGain1_Scroll1(value);
            txtBGain2.Text = Convert.ToString(value);
        }



        //相机2的增益
        private void tkbGain2_Scroll(object sender, EventArgs e)
        {
            int value = tkbGain3.Value;
            eventdelegateSetGain1_Scroll2(value);
            txtBGain3.Text = Convert.ToString(value);

        }


        //相机3的增益
        private void tkbGain1_Scroll3(object sender, EventArgs e)
        {
            int value = tkbGain3.Value;
            eventdelegateSetGain1_Scroll3(value);
            txtBGain3.Text = Convert.ToString(value);
        }



        //相机1软触发
        private void btnExecute1_Click1(object sender, EventArgs e)
        {
            eventdelegateSoftwareExecute1();
        }



        //相机2软触发
        private void btnExecute2_Click(object sender, EventArgs e)
        {
            eventdelegateSoftwareExecute2();
        }
        //相机3软触发
        private void btnExecute3_Click(object sender, EventArgs e)
        {
            eventdelegateSoftwareExecute3();
        }
        //相机4软触发
        private void btnExecute4_Click(object sender, EventArgs e)
        {
            eventdelegateSoftwareExecute4();
        }

        //相机1模式选择
        private void model_select1(object sender, EventArgs e)
        {
            if (rdbFreerun1.Checked == true)
            {
                eventdelegateSetFreeRun1();
            }

            if (rdbtrigger1.Checked == true)
            {
                eventdelegateSetTrigger1();
            }
        }
        //相机2模式选择
        private void model_select2(object sender, EventArgs e)
        {
            if (rdbFreerun2.Checked == true)
            {
                eventdelegateSetFreeRun2();
            }

            if (rdbtrigger2.Checked == true)
            {
                eventdelegateSetTrigger2();
            }
        }
        //相机3模式选择
        private void model_select3(object sender, EventArgs e)
        {
            if (rdbFreerun3.Checked == true)
            {
                eventdelegateSetFreeRun3();
            }

            if (rdbtrigger3.Checked == true)
            {
                eventdelegateSetTrigger3();
            }
        }



        //相机1触发模式选择
        private void Extern_SoftSelect1(object sender, EventArgs e)
        {
            if (rdbHWTrigger1.Checked == true)
            {
                eventdelegateSetExternTrigger1();

            }
            if (rdbSWTrigger1.Checked == true)
            {
                eventdelegateSetSoftTrigger1();
            }

        }


        //相机2触发模式选择
        private void Extern_SoftSelect2(object sender, EventArgs e)
        {
            if (rdbHWTrigger2.Checked == true)
            {
                eventdelegateSetExternTrigger2();

            }
            if (rdbSWTrigger2.Checked == true)
            {
                eventdelegateSetSoftTrigger2();
            }

        }
        //相机3触发模式选择
        private void Extern_SoftSelect3(object sender, EventArgs e)
        {
            if (rdbHWTrigger3.Checked == true)
            {
                eventdelegateSetExternTrigger3();

            }
            if (rdbSWTrigger3.Checked == true)
            {
                eventdelegateSetSoftTrigger3();
            }

        }

    }
}
