using System;
using System.Reflection;
using System.Linq;

namespace CameraTechVerify.Test
{
    /// <summary>
    /// MVS API 探测器 - 用于分析真实的 MVS SDK API 结构
    /// </summary>
    public static class MVSApiExplorer
    {
        /// <summary>
        /// 探测 MVS SDK 的 API 结构
        /// </summary>
        public static void ExploreAPI()
        {
            try
            {
                Console.WriteLine("=== MVS SDK API 探测 ===\n");

                // 获取 MvCameraControl.Net 程序集
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                var mvsAssembly = assemblies.FirstOrDefault(a => a.GetName().Name.Contains("MvCameraControl"));

                if (mvsAssembly == null)
                {
                    Console.WriteLine("未找到 MvCameraControl 程序集");
                    return;
                }

                Console.WriteLine($"找到程序集: {mvsAssembly.FullName}\n");

                // 获取所有公共类型
                var types = mvsAssembly.GetExportedTypes();
                Console.WriteLine($"公共类型数量: {types.Length}\n");

                // 分析主要类型
                foreach (var type in types.Take(10)) // 只显示前10个类型
                {
                    Console.WriteLine($"类型: {type.Name}");
                    Console.WriteLine($"  命名空间: {type.Namespace}");
                    Console.WriteLine($"  类型: {(type.IsClass ? "类" : type.IsInterface ? "接口" : type.IsEnum ? "枚举" : "其他")}");

                    if (type.IsClass && !type.IsAbstract)
                    {
                        // 分析构造函数
                        var constructors = type.GetConstructors(BindingFlags.Public | BindingFlags.Instance);
                        if (constructors.Length > 0)
                        {
                            Console.WriteLine("  构造函数:");
                            foreach (var ctor in constructors.Take(3))
                            {
                                var parameters = string.Join(", ", ctor.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"));
                                Console.WriteLine($"    {type.Name}({parameters})");
                            }
                        }

                        // 分析主要方法
                        var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                                         .Where(m => !m.IsSpecialName && m.DeclaringType == type)
                                         .Take(10);
                        if (methods.Any())
                        {
                            Console.WriteLine("  主要方法:");
                            foreach (var method in methods)
                            {
                                var parameters = string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name));
                                Console.WriteLine($"    {method.ReturnType.Name} {method.Name}({parameters})");
                            }
                        }
                    }

                    Console.WriteLine();
                }

                // 查找可能的主要相机类
                var cameraClasses = types.Where(t => t.IsClass && 
                    (t.Name.ToLower().Contains("camera") || 
                     t.Name.ToLower().Contains("device") ||
                     t.Name.ToLower().Contains("mv")))
                    .ToList();

                if (cameraClasses.Any())
                {
                    Console.WriteLine("可能的相机相关类:");
                    foreach (var cameraClass in cameraClasses)
                    {
                        Console.WriteLine($"  {cameraClass.Name} - {cameraClass.Namespace}");
                    }
                    Console.WriteLine();
                }

                // 查找枚举类型
                var enums = types.Where(t => t.IsEnum).ToList();
                if (enums.Any())
                {
                    Console.WriteLine("枚举类型:");
                    foreach (var enumType in enums.Take(5))
                    {
                        Console.WriteLine($"  {enumType.Name}:");
                        var values = Enum.GetNames(enumType).Take(5);
                        foreach (var value in values)
                        {
                            Console.WriteLine($"    {value}");
                        }
                        Console.WriteLine();
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"API 探测失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 尝试创建相机实例并测试基本功能
        /// </summary>
        public static void TestBasicFunctionality()
        {
            Console.WriteLine("=== 基本功能测试 ===\n");

            try
            {
                // 尝试通过反射创建相机实例
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                var mvsAssembly = assemblies.FirstOrDefault(a => a.GetName().Name.Contains("MvCameraControl"));

                if (mvsAssembly == null)
                {
                    Console.WriteLine("未找到 MvCameraControl 程序集");
                    return;
                }

                // 查找可能的相机类
                var types = mvsAssembly.GetExportedTypes();
                var cameraType = types.FirstOrDefault(t => t.IsClass && 
                    (t.Name.Equals("MyCamera", StringComparison.OrdinalIgnoreCase) ||
                     t.Name.Equals("MvCamera", StringComparison.OrdinalIgnoreCase) ||
                     t.Name.Equals("Camera", StringComparison.OrdinalIgnoreCase)));

                if (cameraType == null)
                {
                    Console.WriteLine("未找到相机类，尝试查找所有可能的类:");
                    var possibleTypes = types.Where(t => t.IsClass && !t.IsAbstract).Take(10);
                    foreach (var type in possibleTypes)
                    {
                        Console.WriteLine($"  {type.Name} - {type.Namespace}");
                    }
                    return;
                }

                Console.WriteLine($"找到相机类: {cameraType.Name}");

                // 尝试创建实例
                var cameraInstance = Activator.CreateInstance(cameraType);
                Console.WriteLine("✓ 相机实例创建成功");

                // 查找设备枚举方法
                var enumMethods = cameraType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .Where(m => m.Name.ToLower().Contains("enum") || m.Name.ToLower().Contains("device"))
                    .ToList();

                if (enumMethods.Any())
                {
                    Console.WriteLine("找到设备枚举方法:");
                    foreach (var method in enumMethods)
                    {
                        Console.WriteLine($"  {method.Name}");
                    }
                }

                // 查找实例方法
                var instanceMethods = cameraType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => !m.IsSpecialName && m.DeclaringType == cameraType)
                    .Take(20);

                Console.WriteLine("\n主要实例方法:");
                foreach (var method in instanceMethods)
                {
                    var parameters = string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name));
                    Console.WriteLine($"  {method.ReturnType.Name} {method.Name}({parameters})");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"基本功能测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 生成 API 分析报告
        /// </summary>
        public static void GenerateAPIReport(string filePath = null)
        {
            filePath = filePath ?? System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "MVS_API_Report.txt");

            try
            {
                using (var writer = new System.IO.StreamWriter(filePath))
                {
                    var originalOut = Console.Out;
                    Console.SetOut(writer);

                    ExploreAPI();
                    TestBasicFunctionality();

                    Console.SetOut(originalOut);
                }

                Console.WriteLine($"API 分析报告已生成: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成报告失败: {ex.Message}");
            }
        }
    }
}
