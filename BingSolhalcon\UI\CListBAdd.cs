﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.LookAndFeel;


namespace BingSolhalcon.UI
{
    public partial class CListBAdd : RibbonForm
    {

        /// 计算采集图像时间自定义委托
       
        public delegate void delegateAddCListB(string wheeltype);
        ComponentResourceManager res = new ComponentResourceManager(typeof(CListBAdd)); //自定义资源字段
        public event delegateAddCListB eventAddCListB;
        public CListBAdd()
        {
            InitializeComponent();
        }

        private void Btm_Add_Click(object sender, EventArgs e)
        {
            if (this.tEdit_wheeltype.Text != "")
            {

                eventAddCListB(this.tEdit_wheeltype.Text);
            }
            else
                MessageBoxEX.Show(res.GetString("inputType"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


        }

        private void CListBAdd_Load(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(CListBAdd));
        }
    }
}
