using System;
using System.Threading;
using System.Windows;
using CameraTechVerify.Windows;

namespace CameraTechVerify
{
    /// <summary>
    /// TestSDKController演示程序
    /// 启动基于TestSDKCameraController实现类的窗体界面
    /// </summary>
    public class TestSDKControllerDemo
    {
        /// <summary>
        /// 程序入口点
        /// </summary>
        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== TestSDK Controller Demo ===");
                Console.WriteLine("启动基于TestSDKCameraController的演示窗体...");
                
                // 创建WPF应用程序
                var app = new Application();
                
                // 设置应用程序属性
                app.ShutdownMode = ShutdownMode.OnMainWindowClose;
                
                // 创建主窗体
                var mainWindow = new TestSDKControllerWindow();
                app.MainWindow = mainWindow;
                
                // 显示窗体并运行应用程序
                Console.WriteLine("窗体已启动，请在界面中操作相机功能");
                Console.WriteLine("控制台将显示操作日志信息");
                Console.WriteLine("关闭窗体将退出程序");
                Console.WriteLine("=====================================");
                
                app.Run(mainWindow);
                
                Console.WriteLine("程序已退出");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序启动异常: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
        
        /// <summary>
        /// 控制台模式演示
        /// </summary>
        public static void RunConsoleDemo()
        {
            Console.WriteLine("=== TestSDK Controller Console Demo ===");
            
            try
            {
                using (var controller = new Implementations.TestSDKCameraController())
                {
                    // 订阅事件
                    controller.ImageReceived += (s, e) => 
                        Console.WriteLine($"[IMAGE] 接收图像 #{e.ImageNumber} - {e.Width}x{e.Height}");
                    controller.ConnectionStatusChanged += (s, e) => 
                        Console.WriteLine($"[STATUS] {e.Status}: {e.Message}");
                    controller.ErrorOccurred += (s, e) => 
                        Console.WriteLine($"[ERROR] [{e.ErrorCode}] {e.ErrorMessage}");
                    
                    // 枚举设备
                    Console.WriteLine("\n1. 枚举设备...");
                    var devices = controller.EnumerateDevices();
                    Console.WriteLine($"找到 {devices.Count} 个设备:");
                    
                    for (int i = 0; i < devices.Count; i++)
                    {
                        var device = devices[i];
                        Console.WriteLine($"  [{i}] {device.DisplayName}");
                        Console.WriteLine($"      类型: {device.DeviceType}, 序列号: {device.SerialNumber}");
                    }
                    
                    if (devices.Count == 0)
                    {
                        Console.WriteLine("未找到设备，演示结束");
                        return;
                    }
                    
                    // 连接第一个设备
                    Console.WriteLine("\n2. 连接设备...");
                    bool connected = controller.Connect(0);
                    if (!connected)
                    {
                        Console.WriteLine("连接失败，演示结束");
                        return;
                    }
                    
                    Console.WriteLine("设备连接成功！");
                    Thread.Sleep(1000);
                    
                    // 获取参数
                    Console.WriteLine("\n3. 获取当前参数...");
                    float exposure = controller.GetExposureTime();
                    float gain = controller.GetGain();
                    float frameRate = controller.GetFrameRate();
                    
                    Console.WriteLine($"曝光时间: {exposure:F1} μs");
                    Console.WriteLine($"增益: {gain:F1} dB");
                    Console.WriteLine($"帧率: {frameRate:F1} fps");
                    
                    // 设置参数
                    Console.WriteLine("\n4. 设置参数...");
                    controller.SetExposureTime(15000);
                    controller.SetGain(8.0f);
                    controller.SetFrameRate(20.0f);
                    
                    // 开始采集
                    Console.WriteLine("\n5. 开始采集...");
                    bool grabStarted = controller.StartGrabbing();
                    if (grabStarted)
                    {
                        Console.WriteLine("采集已开始，等待5秒...");
                        Thread.Sleep(5000);
                        
                        // 停止采集
                        Console.WriteLine("\n6. 停止采集...");
                        controller.StopGrabbing();
                    }
                    
                    // 测试触发模式
                    Console.WriteLine("\n7. 测试触发模式...");
                    controller.SetTriggerMode(Models.TriggerMode.Software);
                    Console.WriteLine("已设置为软件触发模式");
                    
                    for (int i = 0; i < 3; i++)
                    {
                        Console.WriteLine($"执行软件触发 {i + 1}...");
                        controller.SoftwareTrigger();
                        Thread.Sleep(1000);
                    }
                    
                    Console.WriteLine("\n演示完成！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制台演示异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 显示帮助信息
        /// </summary>
        public static void ShowHelp()
        {
            Console.WriteLine("=== TestSDK Controller Demo 帮助 ===");
            Console.WriteLine();
            Console.WriteLine("用法:");
            Console.WriteLine("  TestSDKControllerDemo.exe [选项]");
            Console.WriteLine();
            Console.WriteLine("选项:");
            Console.WriteLine("  (无参数)    启动图形界面演示");
            Console.WriteLine("  -console    运行控制台演示");
            Console.WriteLine("  -help       显示此帮助信息");
            Console.WriteLine();
            Console.WriteLine("功能说明:");
            Console.WriteLine("  - 图形界面演示: 启动WPF窗体，提供完整的相机控制界面");
            Console.WriteLine("  - 控制台演示: 在控制台中演示基本的相机控制功能");
            Console.WriteLine();
            Console.WriteLine("注意事项:");
            Console.WriteLine("  - 需要安装相机驱动和MVS SDK");
            Console.WriteLine("  - 确保相机正确连接到计算机");
            Console.WriteLine("  - GigE相机需要正确的网络配置");
            Console.WriteLine();
        }
        
        /// <summary>
        /// 带参数的主入口
        /// </summary>
        public static void MainWithArgs(string[] args)
        {
            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "-console":
                        RunConsoleDemo();
                        break;
                    case "-help":
                    case "--help":
                    case "/?":
                        ShowHelp();
                        break;
                    default:
                        Console.WriteLine($"未知参数: {args[0]}");
                        ShowHelp();
                        break;
                }
            }
            else
            {
                Main(args);
            }
        }
    }
}
