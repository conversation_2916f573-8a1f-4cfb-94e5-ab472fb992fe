using System;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using CameraTechVerify.Implementations;
using CameraTechVerify.Models;

namespace CameraTechVerify.Examples
{
    /// <summary>
    /// TestSDKCameraController使用示例
    /// 展示如何使用基于TestSDK界面功能的相机控制器
    /// </summary>
    public class TestSDKCameraControllerExample
    {
        private TestSDKCameraController _cameraController;

        public async Task RunExample()
        {
            Console.WriteLine("=== TestSDK相机控制器使用示例 ===");

            try
            {
                // 1. 创建相机控制器实例
                _cameraController = new TestSDKCameraController();
                
                // 2. 订阅事件
                SubscribeToEvents();

                // 3. 枚举设备
                Console.WriteLine("\n1. 枚举相机设备...");
                var devices = _cameraController.EnumerateDevices();
                Console.WriteLine($"找到 {devices.Count} 个设备:");
                
                for (int i = 0; i < devices.Count; i++)
                {
                    var device = devices[i];
                    Console.WriteLine($"  [{i}] {device.DisplayName} - {device.ConnectionInfo}");
                    Console.WriteLine($"      型号: {device.ModelName}, 序列号: {device.SerialNumber}");
                    Console.WriteLine($"      类型: {device.DeviceType}, 制造商: {device.ManufacturerName}");
                }

                if (devices.Count == 0)
                {
                    Console.WriteLine("未找到任何相机设备，示例结束。");
                    return;
                }

                // 4. 连接第一个设备
                Console.WriteLine("\n2. 连接到第一个设备...");
                bool connected = _cameraController.Connect(0);
                if (!connected)
                {
                    Console.WriteLine("连接设备失败，示例结束。");
                    return;
                }

                Console.WriteLine("设备连接成功！");
                await Task.Delay(1000); // 等待连接稳定

                // 5. 获取和设置参数
                Console.WriteLine("\n3. 参数控制示例...");
                await DemonstrateParameterControl();

                // 6. 图像采集示例
                Console.WriteLine("\n4. 图像采集示例...");
                await DemonstrateImageCapture();

                // 7. 触发模式示例
                Console.WriteLine("\n5. 触发模式示例...");
                await DemonstrateTriggerMode();

                // 8. 图像保存示例
                Console.WriteLine("\n6. 图像保存示例...");
                await DemonstrateImageSaving();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"示例执行异常: {ex.Message}");
            }
            finally
            {
                // 清理资源
                Console.WriteLine("\n7. 清理资源...");
                _cameraController?.Dispose();
                Console.WriteLine("示例完成。");
            }
        }

        private void SubscribeToEvents()
        {
            _cameraController.ImageReceived += OnImageReceived;
            _cameraController.ConnectionStatusChanged += OnConnectionStatusChanged;
            _cameraController.ErrorOccurred += OnErrorOccurred;
        }

        private async Task DemonstrateParameterControl()
        {
            try
            {
                // 获取当前参数
                float currentExposure = _cameraController.GetExposureTime();
                float currentGain = _cameraController.GetGain();
                float currentFrameRate = _cameraController.GetFrameRate();
                
                Console.WriteLine($"当前参数:");
                Console.WriteLine($"  曝光时间: {currentExposure:F1} μs");
                Console.WriteLine($"  增益: {currentGain:F1} dB");
                Console.WriteLine($"  帧率: {currentFrameRate:F1} fps");

                // 设置新参数
                Console.WriteLine("设置新参数...");
                bool success1 = _cameraController.SetExposureTime(20000); // 20ms
                bool success2 = _cameraController.SetGain(10.0f); // 10dB
                bool success3 = _cameraController.SetFrameRate(15.0f); // 15fps

                Console.WriteLine($"参数设置结果: 曝光={success1}, 增益={success2}, 帧率={success3}");

                // 验证设置结果
                await Task.Delay(500);
                float newExposure = _cameraController.GetExposureTime();
                float newGain = _cameraController.GetGain();
                float newFrameRate = _cameraController.GetFrameRate();
                
                Console.WriteLine($"设置后参数:");
                Console.WriteLine($"  曝光时间: {newExposure:F1} μs");
                Console.WriteLine($"  增益: {newGain:F1} dB");
                Console.WriteLine($"  帧率: {newFrameRate:F1} fps");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"参数控制示例异常: {ex.Message}");
            }
        }

        private async Task DemonstrateImageCapture()
        {
            try
            {
                // 单次拍照
                Console.WriteLine("单次拍照测试...");
                var image = _cameraController.CaptureImage();
                if (image != null)
                {
                    Console.WriteLine($"拍照成功: {image.Width}x{image.Height}");
                    image.Dispose();
                }
                else
                {
                    Console.WriteLine("拍照失败");
                }

                // 连续采集
                Console.WriteLine("开始连续采集...");
                bool startResult = _cameraController.StartGrabbing();
                if (startResult)
                {
                    Console.WriteLine("连续采集已开始，等待5秒...");
                    await Task.Delay(5000);
                    
                    bool stopResult = _cameraController.StopGrabbing();
                    Console.WriteLine($"停止采集: {stopResult}");
                }
                else
                {
                    Console.WriteLine("开始连续采集失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"图像采集示例异常: {ex.Message}");
            }
        }

        private async Task DemonstrateTriggerMode()
        {
            try
            {
                // 测试不同触发模式
                var triggerModes = new[] { TriggerMode.Continuous, TriggerMode.Software, TriggerMode.Hardware };
                
                foreach (var mode in triggerModes)
                {
                    Console.WriteLine($"设置触发模式: {mode}");
                    bool success = _cameraController.SetTriggerMode(mode);
                    if (success)
                    {
                        var currentMode = _cameraController.GetTriggerMode();
                        Console.WriteLine($"当前触发模式: {currentMode}");
                        
                        if (mode == TriggerMode.Software)
                        {
                            Console.WriteLine("测试软件触发...");
                            for (int i = 0; i < 3; i++)
                            {
                                bool triggerResult = _cameraController.SoftwareTrigger();
                                Console.WriteLine($"软件触发 {i + 1}: {triggerResult}");
                                await Task.Delay(1000);
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"设置触发模式 {mode} 失败");
                    }
                    
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"触发模式示例异常: {ex.Message}");
            }
        }

        private async Task DemonstrateImageSaving()
        {
            try
            {
                // 开始采集以获取图像
                Console.WriteLine("开始采集以获取图像用于保存...");
                _cameraController.StartGrabbing();
                await Task.Delay(2000); // 等待获取图像
                _cameraController.StopGrabbing();

                // 保存不同格式的图像
                var formats = new[] { "PNG", "JPG", "BMP", "TIFF" };
                foreach (var format in formats)
                {
                    string fileName = $"test_image.{format.ToLower()}";
                    bool saveResult = _cameraController.SaveImage(fileName, format);
                    Console.WriteLine($"保存 {format} 格式图像: {saveResult} -> {fileName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"图像保存示例异常: {ex.Message}");
            }
        }

        #region 事件处理

        private void OnImageReceived(object sender, ImageReceivedEventArgs e)
        {
            Console.WriteLine($"接收图像: #{e.ImageNumber} - {e.Width}x{e.Height} @ {e.Timestamp:HH:mm:ss.fff}");
        }

        private void OnConnectionStatusChanged(object sender, ConnectionStatusChangedEventArgs e)
        {
            Console.WriteLine($"连接状态变化: {e.Status} - {e.Message}");
        }

        private void OnErrorOccurred(object sender, ErrorOccurredEventArgs e)
        {
            Console.WriteLine($"错误发生: [{e.ErrorCode}] {e.ErrorMessage} @ {e.Timestamp:HH:mm:ss.fff}");
        }

        #endregion

        #region 静态方法

        /// <summary>
        /// 运行示例的静态方法
        /// </summary>
        public static async Task RunExampleAsync()
        {
            var example = new TestSDKCameraControllerExample();
            await example.RunExample();
        }

        /// <summary>
        /// 简单的设备枚举示例
        /// </summary>
        public static void QuickDeviceEnumeration()
        {
            Console.WriteLine("=== 快速设备枚举示例 ===");
            
            using (var controller = new TestSDKCameraController())
            {
                var devices = controller.EnumerateDevices();
                Console.WriteLine($"找到 {devices.Count} 个设备:");
                
                foreach (var device in devices)
                {
                    Console.WriteLine($"- {device.DisplayName} ({device.DeviceType})");
                    Console.WriteLine($"  序列号: {device.SerialNumber}");
                    Console.WriteLine($"  连接: {device.ConnectionInfo}");
                    Console.WriteLine();
                }
            }
        }

        #endregion
    }
}
