﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Sharp7;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;


namespace BingSolhalcon
{
    public partial class S7communication : Form
    {
        public delegate void delegateGetPLCData(string ip,int Rack,int Slot,int readdb,int writedb);
        public event delegateGetPLCData evenGetPLCData;

        public delegate void delegatePLCStatus();
        public event delegatePLCStatus evenDisconnectPLC;


      static  PcComPlc m_pccomplc;
        Task m_task;
        static string m_ip;
        static int m_Rack, m_Slot,m_WriteDB;

        System.Timers.Timer aTimer ; //初始化定时器

        S7Client client;
      static  bool m_heartbeatstatus;
        //窗口缩放用变量
        private float x;//定义当前窗体的宽度
        private float y;//定义当前窗体的高度
    

        private struct DataAssemble
        {
            public int index, datalength, bitnum;
            public string datatype;
        }

        public int result = 3, connectresult = 3;
        int sendnumsort, receivenumsort, recbytenum, sendbytenum;
        byte[] recBuffer;
        byte[] sendBuffer;
        byte[] sendbytedata = new byte[50];
        short[] sendintdata = new short[50];
        uint[] senduintdata = new uint[50];
        bool[] sendbitdata = new bool[50];
        string[] sendstringdata = new string[50];
        float[] sendfloatdata = new float[50];
        double[] senddoubledata = new double[50];

        byte[] recbytedata = new byte[50];
        short[] recintdata = new short[50];
        uint[] recuintdata = new uint[50];
        bool[] recbitdata = new bool[50];
        string[] recstringdata = new string[50];
        float[] recfloatdata = new float[50];
        double[] recdoubledata = new double[50];


        List<DataAssemble> RecData, SendData;
        public S7communication()
        {
            InitializeComponent();
            //窗口缩放用
            x = this.Width;
            y = this.Height;
            setTag(this);
            //窗口缩放用
            m_pccomplc = new PcComPlc();
            aTimer = new System.Timers.Timer(); //初始化定时器


            client = new S7Client();
            RecData = new List<DataAssemble>();
            SendData = new List<DataAssemble>();
            
           // S7Recieve.Enabled = false;
          
            //  cbB_datatype.Enabled = false;
            //  NUD_Numbyte.Enabled = false;
            tB_Status.BackColor = Color.Red;
            DisconnectBtn.Enabled = false;


            m_heartbeatstatus = false;

        }

        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ";" + con.Height + ";" + con.Left + ";" + con.Top + ";" + con.Font.Size;
                if (con.Controls.Count > 0)
                {
                    setTag(con);
                }
            }
        }

        private void setControls(float newx, float newy, Control cons)
        {
            //遍历窗体中的控件，重新设置控件的值
            foreach (Control con in cons.Controls)
            {
                //获取控件的Tag属性值，并分割后存储字符串数组
                if (con.Tag != null)
                {
                    string[] mytag = con.Tag.ToString().Split(new char[] { ';' });
                    //根据窗体缩放的比例确定控件的值
                    con.Width = Convert.ToInt32(System.Convert.ToSingle(mytag[0]) * newx);//宽度
                    con.Height = Convert.ToInt32(System.Convert.ToSingle(mytag[1]) * newy);//高度
                    con.Left = Convert.ToInt32(System.Convert.ToSingle(mytag[2]) * newx);//左边距
                    con.Top = Convert.ToInt32(System.Convert.ToSingle(mytag[3]) * newy);//顶边距
                    Single currentSize = System.Convert.ToSingle(mytag[4]) * newy;//字体大小
                    con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                    if (con.Controls.Count > 0)
                    {
                        setControls(newx, newy, con);
                    }
                }
            }
        }

        public void importconfig(string namecongfig, ListView listView)
        {
            string stFilePath = Application.StartupPath.Trim() + "\\config\\" + namecongfig + ".txt";

            StreamReader srStream = null;

            if (File.Exists(stFilePath))
            {
                srStream = new StreamReader(stFilePath, System.Text.Encoding.UTF8);
                listView.Items.Clear();
                //遍历txt文件里的所有行，并以ListView显示

                while (!srStream.EndOfStream)
                {
                    string _stLineValue = srStream.ReadLine();

                    ListViewItem _item = new ListViewItem();
                    List<string> InData = new List<string>();
                    string[] sArray = _stLineValue.Split(new string[] { "<-->", "<-->", "<-->" }, StringSplitOptions.RemoveEmptyEntries);

                    //foreach(string se in sArray)
                    //{ MessageBox.Show(se); }

                    _item.Text = sArray[0];
                    for (int i = 1; i < 3; i++)
                    {
                        _item.SubItems.Add(sArray[i]);
                    }
                    //   if (congfig == "sendconfig")

                    listView.Items.Add(_item);


                }
                srStream.Close();
            }
            else
                MessageBox.Show("导入文件失败，找不到" + namecongfig + "文件");
            //break;




        }


        public void send(Datainteraction datainter)
        {

            
            if (SendData.Count != 0)
            {
                int start_index = 0;

                int bitnum = -1, bytenum = -1, intnum = -1, unint = -1, floatnum = -1, doublenum = -1, stringnum = -1;

                for (int i = 0; i < SendData.Count; i++)
                {

                    string subitem2;
                    int subitem1 = 0;

                    // subitem1 = SendData[i].datalength;
                    subitem2 = SendData[i].datatype;

                    //       int listviewindex = int.Parse(listView1.Items[i].Text);

                    if (SendData[i].index == 0)
                        start_index = 0;
                    if (SendData[i].index > 0)
                    {
                        if (SendData[i - 1].datatype == "string")
                            start_index += 256;
                        else
                        {
                            subitem1 = SendData[i - 1].datalength;
                            start_index += subitem1;
                        }

                    }




                    switch (subitem2)
                    {
                        case "bit":

                            int floor = SendData[i].bitnum / 8;
                            int leftbir = SendData[i].bitnum % 8;
                            int floornext = start_index;

                            for (int j = 0; j < floor; j++)
                            {

                                for (int n = 0; n < 8; n++)
                                {
                                    bitnum++;
                                    sendbitdata[bitnum] = true;
                                    S7.SetBitAt(ref sendBuffer, (start_index + j), n, sendbitdata[bitnum]);

                                    //  MessageBox.Show(bitnum.ToString() + sendbitdata[bitnum].ToString());
                                }
                                floornext = start_index + j + 1;

                            }
                            for (int m = 0; m < leftbir; m++)
                            {
                                bitnum++;
                                sendbitdata[bitnum] = true;
                                S7.SetBitAt(ref sendBuffer, floornext, m, sendbitdata[bitnum]);

                                // MessageBox.Show(bitnum.ToString() + sendbitdata[bitnum].ToString());

                            }


                            break;
                        case "byte":
                            bytenum++;
                            sendbytedata[bytenum] = 4;
                            S7.SetByteAt(sendBuffer, start_index, sendbytedata[bytenum]);
                            break;
                        case "int":
                            intnum++;
                            sendintdata[intnum] = datainter.sendintdata[intnum];
                            S7.SetIntAt(sendBuffer, start_index, sendintdata[intnum]);
                            //  MessageBox.Show(sendintdata[intnum].ToString());
                            break;
                        case "uint":
                            ;
                            break;
                        case "float":
                            floatnum++;
                            sendfloatdata[floatnum] = datainter.sendfloatdata[0];
                            S7.SetRealAt(sendBuffer, start_index, (float)sendfloatdata[floatnum]);
                            //  MessageBox.Show(sendfloatdata[floatnum].ToString());
                            break;
                        case "double":
                            doublenum++;
                            senddoubledata[doublenum] = 34.5;
                            S7.SetLRealAt(sendBuffer, start_index, senddoubledata[doublenum]);
                            break;
                        case "string":
                            stringnum++;
                            sendstringdata[stringnum] = datainter.sendstringdata[0];
                            S7.SetStringAt(sendBuffer, start_index, 254, sendstringdata[stringnum]);
                            // MessageBox.Show(sendstringdata[stringnum]);
                            break;
                    }
                }



            }
            else
                MessageBox.Show("发送数据区为空，请添加数据！");

            //    S7.SetBitAt(ref sendBuffer, 0, 0, true);
            //     S7.SetByteAt(sendBuffer, 0, 1);
            //    S7.SetIntAt(sendBuffer, 0, 10);
            //     S7.SetRealAt(sendBuffer, 2, (float)4.36);
            int senddb = int.Parse(tB_SendDB.Text.ToString());
            //  int senddb = int.Parse("2");
            result = client.DBWrite(senddb, 0, sendBuffer.Length, sendBuffer);//第二个参数为PLCDATABSEE中的起始位置

        }
        public void sendHeartBeat(bool heartbeat)
        {

            byte[] heartbeatBuffer = new byte[1];
            S7.SetBitAt(ref heartbeatBuffer, 0, 0, heartbeat);
            int senddb = int.Parse(tB_SendDB.Text.ToString());
            client.DBWrite(senddb, 266, heartbeatBuffer.Length, heartbeatBuffer);
        }

        public void receive(Datainteraction datainter)
        {
            
            int recdb = int.Parse(tB_RecDB.Text.ToString());
            result = client.DBRead(recdb, 0, recBuffer.Length, recBuffer);
            if (RecData.Count != 0)
            {
                int start_index = 0;

                int bitnum = -1, bytenum = -1, intnum = -1, unint = -1, floatnum = -1, doublenum = -1, stringnum = -1;

                for (int i = 0; i < RecData.Count; i++)
                {

                    string subitem2;
                    int subitem1 = 0;

                    // subitem1 = SendData[i].datalength;
                    subitem2 = RecData[i].datatype;

                    //       int listviewindex = int.Parse(listView1.Items[i].Text);

                    if (RecData[i].index == 0)
                        start_index = 0;
                    if (RecData[i].index > 0)
                    {
                        if (RecData[i - 1].datatype == "string")
                            start_index += 256;
                        else
                        {
                            subitem1 = RecData[i - 1].datalength;
                            start_index += subitem1;
                        }

                    }




                    switch (subitem2)
                    {
                        case "bit":

                            int floor = RecData[i].bitnum / 8;
                            int leftbir = RecData[i].bitnum % 8;
                            int floornext = start_index;

                            for (int j = 0; j < floor; j++)
                            {

                                for (int n = 0; n < 8; n++)
                                {
                                    bitnum++;
                                    recbitdata[bitnum] = S7.GetBitAt(recBuffer, (start_index + j), n);
                                    datainter.recbitdata[bitnum] = recbitdata[bitnum];
                                    //   MessageBox.Show(bitnum.ToString() + recbitdata[bitnum].ToString());
                                }
                                floornext = start_index + j + 1;

                            }
                            for (int m = 0; m < leftbir; m++)
                            {
                                bitnum++;
                                recbitdata[bitnum] = S7.GetBitAt(recBuffer, floornext, m);
                                datainter.recbitdata[bitnum] = recbitdata[bitnum];
                                //  MessageBox.Show(bitnum.ToString() + recbitdata[bitnum].ToString());

                            }

                            break;
                        case "byte":
                            bytenum++;

                            recbytedata[bytenum] = S7.GetByteAt(recBuffer, start_index);
                            datainter.recbytedata[bytenum] = recbytedata[bytenum];
                            //  MessageBox.Show(recbytedata[bytenum].ToString());
                            break;
                        case "int":
                            intnum++;

                            recintdata[intnum] = (short)S7.GetIntAt(recBuffer, start_index);
                            datainter.recintdata[intnum] = recintdata[intnum];
                            //  MessageBox.Show(recintdata[intnum].ToString());
                            break;
                        case "uint":
                            ;
                            break;
                        case "float":
                            floatnum++;

                            recfloatdata[floatnum] = S7.GetRealAt(recBuffer, start_index);
                            datainter.recfloatdata[floatnum] = recfloatdata[floatnum];
                            //    MessageBox.Show(recfloatdata[floatnum].ToString());
                            break;
                        case "double":
                            doublenum++;

                            recdoubledata[doublenum] = S7.GetLRealAt(recBuffer, start_index);
                            datainter.recdoubledata[doublenum] = recdoubledata[doublenum];
                            //  MessageBox.Show(recdoubledata[doublenum].ToString());
                            break;
                        case "string":
                            stringnum++;
                            recstringdata[stringnum] = S7.GetStringAt(recBuffer, start_index);
                            datainter.recstringdata[stringnum] = recstringdata[stringnum];
                            //    MessageBox.Show(recstringdata[stringnum]);
                            break;
                    }
                }



            }
            else
                MessageBox.Show("接收数据区为空，请添加数据！");



            if (result != 0)
            {
                Console.WriteLine("Error: " + client.ErrorText(result));
            }

        }








        private void ConnectBtn_Click(object sender, EventArgs e)
        {
            int Rack = System.Convert.ToInt32(TxtRack.Text);
            int Slot = System.Convert.ToInt32(TxtSlot.Text);

            int dbnum = int.Parse(tB_RecDB.Text);

            TextError.Text = "Connected to " + TxtIP.Text.ToString() + " PDU Negotiated : " + client.PduSizeNegotiated.ToString();
            TxtIP.Enabled = false;
            TxtRack.Enabled = false;
            TxtSlot.Enabled = false;
            ConnectBtn.Enabled = false;
            DisconnectBtn.Enabled = true;
           
            

            


            //通讯写入到配置
            ConfigIni.WriteIniKey("通讯地址", "IP", TxtIP.Text, Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "导轨", TxtRack.Text, Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "槽号", TxtSlot.Text, Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "工控机发送块", tB_SendDB.Text, Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "工控机接收块", tB_RecDB.Text, Application.StartupPath + "\\config.ini");
          

            //m_thread = new Thread(s7read);
            //m_thread.Start();

            m_task = Task.Factory.StartNew(() =>
            {

                evenGetPLCData(TxtIP.Text, Rack, Slot, int.Parse(tB_RecDB.Text), int.Parse(tB_SendDB.Text));



            });


            m_ip = TxtIP.Text;
            m_Rack = int.Parse(TxtRack.Text);
            m_Slot = int.Parse(TxtSlot.Text);
            m_WriteDB = int.Parse(tB_SendDB.Text);
            

           
               //aTimer.Interval = 800;//配置时间1分钟
               //aTimer.Elapsed += new System.Timers.ElapsedEventHandler(OnTimedEvent);
               //aTimer.AutoReset = true;//每到指定时间Elapsed事件是到时间就触发
               //aTimer.Enabled = true; //指示 Timer 是否应引发 Elapsed 事件。
          

        }
        private static void OnTimedEvent(Object source, ElapsedEventArgs e)
        {
          
            int  connectresult = m_pccomplc.Connect(m_ip, m_Rack, m_Slot);

            if (connectresult != 0)
            {
                return;
            }
            if (m_heartbeatstatus == false)
            {

                m_pccomplc.SetBit(m_WriteDB, 816, 0, true);
                m_heartbeatstatus = true;
            }
            else
            {
                m_pccomplc.SetBit(m_WriteDB, 816, 0, false);
                m_heartbeatstatus = false;

            }

        }

        //void s7read()
        //{
        //    int Rack = System.Convert.ToInt32(TxtRack.Text);
        //    int Slot = System.Convert.ToInt32(TxtSlot.Text);

        //    int dbnum = int.Parse(tB_RecDB.Text);

        //    evenGetPLCData(TxtIP.Text, Rack, Slot, int.Parse(tB_RecDB.Text), int.Parse(tB_SendDB.Text));

        //}


        private void DisconnectBtn_Click(object sender, EventArgs e)
        {
           
            System.Threading.Thread.Sleep(200);
           
           
            evenDisconnectPLC();
            aTimer.Enabled = false;


            TextError.Text = "Disconnected";
            TxtIP.Enabled = true;
            TxtRack.Enabled = true;
            TxtSlot.Enabled = true;
            ConnectBtn.Enabled = true;
            DisconnectBtn.Enabled = false;
           
           
           
            tB_Status.BackColor = Color.Red;
           
          

        }

        

       

       

    

        private void readplc_Tick(object sender, EventArgs e)
        {
            
        }

      

     







        private void exportconfig(ListView listView, string configname)
        {

            string stFilePath = Application.StartupPath.Trim() + "\\config\\" + configname + ".txt";

            StreamWriter swStream;

            if (File.Exists(stFilePath))
            {


                //声明数据流文件写入方法  


                swStream = new StreamWriter(stFilePath);

            }
            else
            {

                swStream = File.CreateText(stFilePath);

            }

            for (int i = 0; i < listView.Items.Count; i++)
            {
                for (int j = 0; j < listView.Items[i].SubItems.Count; j++)
                {
                    string _strTemp = listView.Items[i].SubItems[j].Text;
                    swStream.Write(_strTemp);
                    swStream.Write("<-->");
                }
                swStream.WriteLine();


            }
            swStream.Flush();
            swStream.Close();

        }

        private void S7communication_Resize(object sender, EventArgs e)
        {
            float newx = (this.Width) / x;
            float newy = (this.Height) / y;
            setControls(newx, newy, this);
        }

        private void S7windowload(object sender, EventArgs e)
        {
           
        }

        public void Close()
        {

            this.Dispose();

        }





    }
}
