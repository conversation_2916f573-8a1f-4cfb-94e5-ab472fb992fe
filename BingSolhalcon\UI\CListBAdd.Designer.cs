﻿
namespace BingSolhalcon.UI
{
    partial class CListBAdd
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CListBAdd));
            this.tEdit_wheeltype = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.Btm_Add = new DevExpress.XtraEditors.SimpleButton();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            ((System.ComponentModel.ISupportInitialize)(this.tEdit_wheeltype.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            this.SuspendLayout();
            // 
            // tEdit_wheeltype
            // 
            resources.ApplyResources(this.tEdit_wheeltype, "tEdit_wheeltype");
            this.tEdit_wheeltype.Name = "tEdit_wheeltype";
            // 
            // 
            // 
            this.tEdit_wheeltype.Properties.AccessibleDescription = resources.GetString("tEdit_wheeltype.Properties.AccessibleDescription");
            this.tEdit_wheeltype.Properties.AccessibleName = resources.GetString("tEdit_wheeltype.Properties.AccessibleName");
            this.tEdit_wheeltype.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("tEdit_wheeltype.Properties.Appearance.Font")));
            this.tEdit_wheeltype.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("tEdit_wheeltype.Properties.Appearance.FontSizeDelta")));
            this.tEdit_wheeltype.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tEdit_wheeltype.Properties.Appearance.FontStyleDelta")));
            this.tEdit_wheeltype.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tEdit_wheeltype.Properties.Appearance.GradientMode")));
            this.tEdit_wheeltype.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("tEdit_wheeltype.Properties.Appearance.Image")));
            this.tEdit_wheeltype.Properties.Appearance.Options.UseFont = true;
            this.tEdit_wheeltype.Properties.AutoHeight = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.AutoHeight")));
            this.tEdit_wheeltype.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("tEdit_wheeltype.Properties.Mask.AutoComplete")));
            this.tEdit_wheeltype.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.Mask.BeepOnError")));
            this.tEdit_wheeltype.Properties.Mask.EditMask = resources.GetString("tEdit_wheeltype.Properties.Mask.EditMask");
            this.tEdit_wheeltype.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.Mask.IgnoreMaskBlank")));
            this.tEdit_wheeltype.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("tEdit_wheeltype.Properties.Mask.MaskType")));
            this.tEdit_wheeltype.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("tEdit_wheeltype.Properties.Mask.PlaceHolder")));
            this.tEdit_wheeltype.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.Mask.SaveLiteral")));
            this.tEdit_wheeltype.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.Mask.ShowPlaceHolders")));
            this.tEdit_wheeltype.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.Mask.UseMaskAsDisplayFormat")));
            this.tEdit_wheeltype.Properties.NullValuePrompt = resources.GetString("tEdit_wheeltype.Properties.NullValuePrompt");
            this.tEdit_wheeltype.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("tEdit_wheeltype.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl1.Appearance.Font")));
            this.labelControl1.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl1.Appearance.FontSizeDelta")));
            this.labelControl1.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl1.Appearance.FontStyleDelta")));
            this.labelControl1.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl1.Appearance.GradientMode")));
            this.labelControl1.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl1.Appearance.Image")));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Name = "labelControl1";
            // 
            // Btm_Add
            // 
            resources.ApplyResources(this.Btm_Add, "Btm_Add");
            this.Btm_Add.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("Btm_Add.Appearance.Font")));
            this.Btm_Add.Appearance.FontSizeDelta = ((int)(resources.GetObject("Btm_Add.Appearance.FontSizeDelta")));
            this.Btm_Add.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("Btm_Add.Appearance.FontStyleDelta")));
            this.Btm_Add.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("Btm_Add.Appearance.GradientMode")));
            this.Btm_Add.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("Btm_Add.Appearance.Image")));
            this.Btm_Add.Appearance.Options.UseFont = true;
            this.Btm_Add.Name = "Btm_Add";
            this.Btm_Add.Click += new System.EventHandler(this.Btm_Add_Click);
            // 
            // ribbonControl1
            // 
            resources.ApplyResources(this.ribbonControl1, "ribbonControl1");
            // 
            // 
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem});
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            // 
            // CListBAdd
            // 
            resources.ApplyResources(this, "$this");
            this.Appearance.FontSizeDelta = ((int)(resources.GetObject("CListBAdd.Appearance.FontSizeDelta")));
            this.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("CListBAdd.Appearance.FontStyleDelta")));
            this.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("CListBAdd.Appearance.GradientMode")));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CListBAdd.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.Btm_Add);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.tEdit_wheeltype);
            this.Controls.Add(this.ribbonControl1);
            this.Name = "CListBAdd";
            this.Ribbon = this.ribbonControl1;
            this.Load += new System.EventHandler(this.CListBAdd_Load);
            ((System.ComponentModel.ISupportInitialize)(this.tEdit_wheeltype.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.TextEdit tEdit_wheeltype;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton Btm_Add;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
    }
}