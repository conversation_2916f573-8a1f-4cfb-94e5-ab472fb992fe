﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="label1.Text" xml:space="preserve">
    <value>产品型号</value>
  </data>
  <data name="btn_Save.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1047, 717</value>
  </data>
  <data name="&gt;&gt;btn_CreaTepattern.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btn_BaseLine.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 43</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Name" xml:space="preserve">
    <value>hWindowControl1</value>
  </data>
  <data name="&gt;&gt;btn_Save.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btn_CreaTepattern.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 37</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>HKLaser</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Type" xml:space="preserve">
    <value>HalconDotNet.HWindowControl, halcondotnet, Version=19.11.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8</value>
  </data>
  <data name="&gt;&gt;wheelmodol.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="wheelmodol.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 129</value>
  </data>
  <data name="&gt;&gt;btn_BaseLine.Name" xml:space="preserve">
    <value>btn_BaseLine</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>9, 18</value>
  </data>
  <data name="btn_Test.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 42</value>
  </data>
  <data name="&gt;&gt;btn_CreaTepattern.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;ReadImage_button.Name" xml:space="preserve">
    <value>ReadImage_button</value>
  </data>
  <data name="btn_BaseLine.Text" xml:space="preserve">
    <value>检测基准</value>
  </data>
  <data name="btn_CreaTepattern.Text" xml:space="preserve">
    <value>创建模板</value>
  </data>
  <data name="btn_Save.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 50</value>
  </data>
  <data name="&gt;&gt;btn_Save.Name" xml:space="preserve">
    <value>btn_Save</value>
  </data>
  <data name="ReadImage_button.Text" xml:space="preserve">
    <value>加载数据</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;btn_Test.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btn_Save.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 18</value>
  </data>
  <data name="&gt;&gt;btn_BaseLine.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_CreaTepattern.Name" xml:space="preserve">
    <value>btn_CreaTepattern</value>
  </data>
  <data name="&gt;&gt;ReadImage_button.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="ReadImage_button.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 27</value>
  </data>
  <data name="&gt;&gt;btn_Save.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="hWindowControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 12</value>
  </data>
  <data name="&gt;&gt;wheelmodol.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_Test.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btn_Save.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 331</value>
  </data>
  <data name="&gt;&gt;btn_Test.Name" xml:space="preserve">
    <value>btn_Test</value>
  </data>
  <data name="&gt;&gt;hWindowControl1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btn_Test.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 422</value>
  </data>
  <data name="wheelmodol.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 28</value>
  </data>
  <data name="btn_CreaTepattern.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;btn_Save.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_CreaTepattern.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_BaseLine.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btn_Test.Text" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_BaseLine.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="ReadImage_button.Size" type="System.Drawing.Size, System.Drawing">
    <value>298, 44</value>
  </data>
  <data name="btn_Test.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;ReadImage_button.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="ReadImage_button.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>717, 93</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>HKLaser</value>
  </data>
  <data name="btn_CreaTepattern.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 260</value>
  </data>
  <data name="&gt;&gt;ReadImage_button.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btn_Test.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="hWindowControl1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btn_BaseLine.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="btn_BaseLine.Location" type="System.Drawing.Point, System.Drawing">
    <value>720, 193</value>
  </data>
  <data name="&gt;&gt;wheelmodol.Name" xml:space="preserve">
    <value>wheelmodol</value>
  </data>
  <data name="hWindowControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>686, 702</value>
  </data>
  <data name="wheelmodol.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;wheelmodol.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="hWindowControl1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <metadata name="hWindowControl1.LayoutBitmap" type="System.Resources.ResXNullRef, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value />
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>