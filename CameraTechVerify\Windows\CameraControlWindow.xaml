<Window x:Class="CameraTechVerify.Windows.CameraControlWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="MVS 工业相机控制器" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#FF2D2D30">

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#FF007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF1E90FF"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#FF666666"/>
                    <Setter Property="Foreground" Value="#FFAAAAAA"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 文本框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF007ACC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Margin" Value="2"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="ModernLabel" TargetType="Label">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF4A4A4A" Padding="15,10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📷" FontSize="20" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="MVS 工业相机控制器" FontSize="16" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock x:Name="ConnectionStatusText" Text="未连接" FontSize="12" Foreground="#FFFF6B6B" 
                          VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <Border Grid.Column="0" Background="#FF3C3C3C" CornerRadius="5" Margin="0,0,5,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 设备管理 -->
                        <GroupBox Header="设备管理" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <!-- 设备枚举 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <Button x:Name="RefreshDevicesBtn" Content="刷新设备" Style="{StaticResource ModernButton}"
                                           Click="RefreshDevicesBtn_Click" Width="70"/>
                                    <ComboBox x:Name="DeviceTypeComboBox" Width="80" Margin="5,5,0,5"
                                             Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC"
                                             SelectionChanged="DeviceTypeComboBox_SelectionChanged">
                                        <ComboBoxItem Content="全部" Tag="" IsSelected="True"/>
                                        <ComboBoxItem Content="GigE" Tag="GigE"/>
                                        <ComboBoxItem Content="USB3" Tag="USB3"/>
                                        <ComboBoxItem Content="USB2" Tag="USB2"/>
                                    </ComboBox>
                                    <Button x:Name="RefreshGigEBtn" Content="GigE" Style="{StaticResource ModernButton}"
                                           Click="RefreshGigEBtn_Click" Width="50"/>
                                </StackPanel>

                                <ComboBox x:Name="DeviceComboBox" Margin="5" Background="#FF3F3F46" Foreground="White"
                                         BorderBrush="#FF007ACC" SelectionChanged="DeviceComboBox_SelectionChanged"/>

                                <!-- 连接方式选择 -->
                                <TabControl x:Name="ConnectionTabControl" Margin="0,5,0,5" Background="#FF3F3F46">
                                    <TabItem Header="设备列表" Foreground="White">
                                        <StackPanel Margin="5">
                                            <StackPanel Orientation="Horizontal">
                                                <Button x:Name="ConnectBtn" Content="连接" Style="{StaticResource ModernButton}"
                                                       Click="ConnectBtn_Click"/>
                                                <Button x:Name="DisconnectBtn" Content="断开" Style="{StaticResource ModernButton}"
                                                       Click="DisconnectBtn_Click" IsEnabled="False"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </TabItem>

                                    <TabItem Header="IP 连接" Foreground="White">
                                        <StackPanel Margin="5">
                                            <Label Content="IP 地址:" Style="{StaticResource ModernLabel}"/>
                                            <TextBox x:Name="IpAddressTextBox" Text="***********00" Style="{StaticResource ModernTextBox}"/>
                                            <Label Content="端口:" Style="{StaticResource ModernLabel}"/>
                                            <TextBox x:Name="PortTextBox" Text="3956" Style="{StaticResource ModernTextBox}"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                <Button x:Name="PingBtn" Content="Ping" Style="{StaticResource ModernButton}"
                                                       Click="PingBtn_Click" Width="50"/>
                                                <Button x:Name="ConnectByIPBtn" Content="连接" Style="{StaticResource ModernButton}"
                                                       Click="ConnectByIPBtn_Click" Width="50"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </TabItem>

                                    <TabItem Header="序列号" Foreground="White">
                                        <StackPanel Margin="5">
                                            <Label Content="序列号:" Style="{StaticResource ModernLabel}"/>
                                            <TextBox x:Name="SerialNumberTextBox" Text="" Style="{StaticResource ModernTextBox}"/>
                                            <Button x:Name="ConnectBySerialBtn" Content="连接" Style="{StaticResource ModernButton}"
                                                   Click="ConnectBySerialBtn_Click" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </TabItem>

                                    <TabItem Header="MAC 地址" Foreground="White">
                                        <StackPanel Margin="5">
                                            <Label Content="MAC 地址:" Style="{StaticResource ModernLabel}"/>
                                            <TextBox x:Name="MacAddressTextBox" Text="00:11:22:33:44:55" Style="{StaticResource ModernTextBox}"/>
                                            <Button x:Name="ConnectByMacBtn" Content="连接" Style="{StaticResource ModernButton}"
                                                   Click="ConnectByMacBtn_Click" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </TabItem>
                                </TabControl>
                            </StackPanel>
                        </GroupBox>

                        <!-- 图像采集 -->
                        <GroupBox Header="图像采集" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <Button x:Name="StartGrabbingBtn" Content="开始采集" Style="{StaticResource ModernButton}" 
                                           Click="StartGrabbingBtn_Click" IsEnabled="False"/>
                                    <Button x:Name="StopGrabbingBtn" Content="停止采集" Style="{StaticResource ModernButton}" 
                                           Click="StopGrabbingBtn_Click" IsEnabled="False"/>
                                </StackPanel>
                                <Button x:Name="CaptureBtn" Content="单次拍照" Style="{StaticResource ModernButton}" 
                                       Click="CaptureBtn_Click" IsEnabled="False"/>
                                <Button x:Name="SaveImageBtn" Content="保存图像" Style="{StaticResource ModernButton}" 
                                       Click="SaveImageBtn_Click" IsEnabled="False"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 相机参数 -->
                        <GroupBox Header="相机参数" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <!-- 曝光时间 -->
                                <Label Content="曝光时间 (μs):" Style="{StaticResource ModernLabel}"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBox x:Name="ExposureTimeTextBox" Text="10000" Width="80" Style="{StaticResource ModernTextBox}"/>
                                    <Button Content="设置" Style="{StaticResource ModernButton}" Width="50" 
                                           Click="SetExposureBtn_Click" IsEnabled="False"/>
                                </StackPanel>

                                <!-- 增益 -->
                                <Label Content="增益 (dB):" Style="{StaticResource ModernLabel}"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBox x:Name="GainTextBox" Text="0" Width="80" Style="{StaticResource ModernTextBox}"/>
                                    <Button Content="设置" Style="{StaticResource ModernButton}" Width="50" 
                                           Click="SetGainBtn_Click" IsEnabled="False"/>
                                </StackPanel>

                                <!-- 帧率 -->
                                <Label Content="帧率 (fps):" Style="{StaticResource ModernLabel}"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBox x:Name="FrameRateTextBox" Text="30" Width="80" Style="{StaticResource ModernTextBox}"/>
                                    <Button Content="设置" Style="{StaticResource ModernButton}" Width="50" 
                                           Click="SetFrameRateBtn_Click" IsEnabled="False"/>
                                </StackPanel>

                                <!-- 触发模式 -->
                                <Label Content="触发模式:" Style="{StaticResource ModernLabel}"/>
                                <ComboBox x:Name="TriggerModeComboBox" Margin="5" Background="#FF3F3F46" Foreground="White"
                                         BorderBrush="#FF007ACC" SelectionChanged="TriggerModeComboBox_SelectionChanged"
                                         IsEnabled="False">
                                    <ComboBoxItem Content="连续模式" Tag="Continuous" IsSelected="True"/>
                                    <ComboBoxItem Content="软件触发" Tag="Software"/>
                                    <ComboBoxItem Content="硬件触发" Tag="Hardware"/>
                                </ComboBox>

                                <Button x:Name="SoftwareTriggerBtn" Content="软件触发" Style="{StaticResource ModernButton}" 
                                       Click="SoftwareTriggerBtn_Click" IsEnabled="False"/>

                                <!-- 参数操作 -->
                                <Separator Margin="0,10" Background="#FF666666"/>
                                <Button Content="读取所有参数" Style="{StaticResource ModernButton}" 
                                       Click="ReadParametersBtn_Click" IsEnabled="False"/>
                                <Button Content="保存参数配置" Style="{StaticResource ModernButton}" 
                                       Click="SaveParametersBtn_Click" IsEnabled="False"/>
                                <Button Content="加载参数配置" Style="{StaticResource ModernButton}" 
                                       Click="LoadParametersBtn_Click" IsEnabled="False"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 网络工具 -->
                        <GroupBox Header="网络工具" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <Label Content="强制 IP 配置:" Style="{StaticResource ModernLabel}"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0" Content="MAC:" Style="{StaticResource ModernLabel}" FontSize="10"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="ForceIPMacTextBox" Text="00:11:22:33:44:55"
                                            Style="{StaticResource ModernTextBox}" FontSize="10"/>

                                    <Label Grid.Row="1" Grid.Column="0" Content="IP:" Style="{StaticResource ModernLabel}" FontSize="10"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="ForceIPAddressTextBox" Text="***********00"
                                            Style="{StaticResource ModernTextBox}" FontSize="10"/>

                                    <Label Grid.Row="2" Grid.Column="0" Content="掩码:" Style="{StaticResource ModernLabel}" FontSize="10"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="ForceSubnetMaskTextBox" Text="*************"
                                            Style="{StaticResource ModernTextBox}" FontSize="10"/>

                                    <Label Grid.Row="3" Grid.Column="0" Content="网关:" Style="{StaticResource ModernLabel}" FontSize="10"/>
                                    <TextBox Grid.Row="3" Grid.Column="1" x:Name="ForceGatewayTextBox" Text="***********"
                                            Style="{StaticResource ModernTextBox}" FontSize="10"/>

                                    <Button Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" x:Name="ForceIPBtn" Content="强制配置 IP"
                                           Style="{StaticResource ModernButton}" Click="ForceIPBtn_Click" Margin="0,5,0,0"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- API 分析 -->
                        <GroupBox Header="SDK 分析" Foreground="White">
                            <StackPanel>
                                <Button Content="分析 MVS API" Style="{StaticResource ModernButton}"
                                       Click="AnalyzeAPIBtn_Click"/>
                                <Button Content="生成 API 报告" Style="{StaticResource ModernButton}"
                                       Click="GenerateReportBtn_Click"/>
                                <Button Content="测试基本功能" Style="{StaticResource ModernButton}"
                                       Click="TestBasicBtn_Click"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 中间图像显示区域 -->
            <Border Grid.Column="1" Background="#FF3C3C3C" CornerRadius="5" Margin="5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 图像信息栏 -->
                    <Border Grid.Row="0" Background="#FF4A4A4A" Padding="10,5">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="ImageInfoText" Text="未获取图像" Foreground="White" FontSize="12"/>
                            <TextBlock x:Name="ImageCountText" Text="图像计数: 0" Foreground="LightGray" FontSize="10" Margin="20,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 图像显示 -->
                    <Grid Grid.Row="1">
                        <Image x:Name="CameraImage" Stretch="Uniform" Margin="10"/>
                        <TextBlock x:Name="NoImageText" Text="📷 未获取图像" FontSize="24" Foreground="#FF666666" 
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>
                </Grid>
            </Border>

            <!-- 右侧信息面板 -->
            <Border Grid.Column="2" Background="#FF3C3C3C" CornerRadius="5" Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 设备信息 -->
                        <GroupBox Header="设备信息" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock x:Name="DeviceNameText" Text="设备名称: 未连接" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2" TextWrapping="Wrap"/>
                                <TextBlock x:Name="ModelNameText" Text="型号: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2" TextWrapping="Wrap"/>
                                <TextBlock x:Name="SerialNumberText" Text="序列号: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2" TextWrapping="Wrap"/>
                                <TextBlock x:Name="ConnectionTypeText" Text="连接类型: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2" TextWrapping="Wrap"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 当前参数 -->
                        <GroupBox Header="当前参数" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock x:Name="CurrentExposureText" Text="曝光: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="CurrentGainText" Text="增益: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="CurrentFrameRateText" Text="帧率: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="CurrentResolutionText" Text="分辨率: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="CurrentTriggerModeText" Text="触发模式: 未知" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 统计信息 -->
                        <GroupBox Header="统计信息" Foreground="White" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock x:Name="TotalImagesText" Text="总图像数: 0" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="FrameRateStatsText" Text="实际帧率: 0 fps" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                                <TextBlock x:Name="LastImageTimeText" Text="最后图像: 无" Foreground="LightGray" 
                                          FontSize="10" Margin="0,2"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 日志 -->
                        <GroupBox Header="操作日志" Foreground="White">
                            <ScrollViewer Height="200" VerticalScrollBarVisibility="Auto">
                                <TextBox x:Name="LogTextBox" Background="Transparent" Foreground="LightGray" 
                                        BorderThickness="0" IsReadOnly="True" TextWrapping="Wrap" FontSize="9"
                                        Text="系统启动...&#x0a;等待操作..."/>
                            </ScrollViewer>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FF4A4A4A" Padding="10,5">
            <Grid>
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="StatusText" Text="就绪" Foreground="White" FontSize="12"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <TextBlock Text="MVS SDK 状态:" Foreground="LightGray" FontSize="10" VerticalAlignment="Center"/>
                    <TextBlock x:Name="SDKStatusText" Text="未检测" Foreground="#FFFF6B6B" FontSize="10" 
                              VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
