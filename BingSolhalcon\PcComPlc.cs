﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using Sharp7;



namespace BingSolhalcon
{
    class PcComPlc
    {
        S7Client client;
        public const int BUFFER_LENTH = 1024;
        bool first = false, sencond = false;
        static Mutex mutex = new Mutex();

        //构造函数
        public PcComPlc()
        {
            client = new S7Client();
            
        }
        //连接PLC并读取plc缓存
        public int Connect(string Ip, int Rack, int Slot)
        {
            int connectresult = 3;
            connectresult = client.ConnectTo(Ip, Rack, Slot);
            return connectresult;
            
        }
        public void DisConnect()
        {
            client.Disconnect();

        }
        public bool Connected
        {
            get
            {
                return client.Connected;
            }
        }

        //获得bit
        public bool Getbit(int DBnum,int startbyte ,int indexbit)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[1];
            client.DBRead(DBnum, startbyte, 1, Buffer);
            bool bit = S7.GetBitAt(Buffer, 0, indexbit);
            mutex.ReleaseMutex();
            return bit;
        }
        //获得字符串
        public string GetString(int DBnum, int startbyte)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[512];
            client.DBRead(DBnum, startbyte, 256, Buffer);
            string str = S7.GetStringAt(Buffer, 0);
            mutex.ReleaseMutex();
            return str;
        }
        //获得浮点数
        public float GetFloat(int DBnum, int startbyte)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[4];
            client.DBRead(DBnum, startbyte, 4, Buffer);
            float flo = S7.GetRealAt(Buffer, 0);
            mutex.ReleaseMutex();
            return flo;
        }

        //获得整形数
        public short GetInt(int DBnum, int startbyte)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[2];
            client.DBRead(DBnum, startbyte, 2, Buffer);
            short sho = (short)S7.GetIntAt(Buffer, 0);
            mutex.ReleaseMutex();
            return sho;
        }

        //获得整形数
        public short GetIntLittleEndian(int DBnum, int startbyte)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[2];
            client.DBRead(DBnum, startbyte, 2, Buffer);

            var value = (short)(Buffer[0] | (Buffer[1] << 8));
            mutex.ReleaseMutex();
            return value;
        }
        

        //获得上升沿指令
        public bool P_TRIG(bool insignal)
        {
            sencond = insignal;
            first = sencond;
            if (sencond == true && first == false)
                return true;
            else
                return false;
        }

        //发送bool量
        public void SetBit(int DBnum, int startbyte, int indexbit,bool setvalue)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[2];
            for (int i = 0; i < 16; i++)
            {
                bool m_bit = Getbit(DBnum, startbyte, i);
                S7.SetBitAt(ref Buffer, 0, i, m_bit);
            }
      
            int result = 3;
            S7.SetBitAt(ref Buffer, 0, indexbit, setvalue);
            result = client.DBWrite(DBnum, startbyte, 2, Buffer);//第二个参数为PLCDATABSEE中的起始位置
            mutex.ReleaseMutex();
        }

        public void SetByte(int DBnum, int startbyte, byte setvalue)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[1];
            int result = 3;
            S7.SetByteAt(Buffer, 0, setvalue);
            result = client.DBWrite(DBnum, startbyte, 1, Buffer);//第二个参数为PLCDATABSEE中的起始位置
            mutex.ReleaseMutex();
        }

        //发送int
        public void SetInt(int DBnum, int startbyte, short setvalue)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[2];
            int result = 3;
            S7.SetIntAt(Buffer, 0, setvalue);
            result = client.DBWrite(DBnum, startbyte, 2, Buffer);//第二个参数为PLCDATABSEE中的起始位置
            mutex.ReleaseMutex();
        }

        //发送float
        public void SetFloat(int DBnum, int startbyte, float setvalue)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[4];
            int result = 3;
            S7.SetRealAt(Buffer, 0, setvalue);
            result = client.DBWrite(DBnum, startbyte, 4, Buffer);//第二个参数为PLCDATABSEE中的起始位置
            mutex.ReleaseMutex();
        }
        //发送字符串
        public void SetString(int DBnum, int startbyte, string setvalue)
        {
            mutex.WaitOne();
            byte[] Buffer = new byte[256];
            int result = 3;
            S7.SetStringAt(Buffer, 0, 254, setvalue);
            result = client.DBWrite(DBnum, startbyte, 256, Buffer);//第二个参数为PLCDATABSEE中的起始位置
            mutex.ReleaseMutex();
        }

    }
}
