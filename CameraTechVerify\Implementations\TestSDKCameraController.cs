using System.Drawing;
using MvCameraControl;
using CameraTechVerify.Interfaces;
using CameraTechVerify.Models;

namespace CameraTechVerify.Implementations
{
    /// <summary>
    /// 基于TestSDK界面功能的相机控制器实现
    /// 直接使用MvCameraControl SDK，基于TestSDK.xaml中已验证的代码
    /// </summary>
    public class TestSDKCameraController : ICameraController
    {
        #region 私有字段

        // SDK相关
        private readonly DeviceTLayerType enumTLayerType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice
            | DeviceTLayerType.MvGenTLGigEDevice | DeviceTLayerType.MvGenTLCXPDevice | DeviceTLayerType.MvGenTLCameraLinkDevice | DeviceTLayerType.MvGenTLXoFDevice;

        private List<IDeviceInfo> deviceInfoList = new List<IDeviceInfo>();
        private IDevice device = null;

        // 状态标志
        private bool isGrabbing = false;
        private bool isRecord = false;
        private Thread receiveThread = null;

        // 图像相关
        private IFrameOut frameForSave;
        private readonly object saveImageLock = new object();
        private long frameNumber = 0;

        // 接口实现相关
        private bool _isConnected = false;
        private CameraDeviceInfo _currentDeviceInfo;
        private CameraParameters _parameters;
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        #endregion

        #region 事件定义

        public event EventHandler<ImageReceivedEventArgs> ImageReceived;
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        public event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        #endregion

        #region 基本属性

        public bool IsConnected => _isConnected;
        public bool IsGrabbing => isGrabbing;
        public CameraDeviceInfo DeviceInfo => _currentDeviceInfo;
        public CameraParameters Parameters
        {
            get => _parameters;
            set => _parameters = value;
        }

        #endregion

        #region 构造函数和初始化

        public TestSDKCameraController()
        {
            try
            {
                // 初始化SDK
                SDKSystem.Initialize();

                // 初始化参数
                _parameters = new CameraParameters();

                OnConnectionStatusChanged(ConnectionStatus.Disconnected, "相机控制器已初始化");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"初始化相机控制器失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 设备管理

        public List<CameraDeviceInfo> EnumerateDevices()
        {
            return EnumerateDevices(null);
        }

        public List<CameraDeviceInfo> EnumerateDevices(string deviceType)
        {
            lock (_lockObject)
            {
                try
                {
                    var result = new List<CameraDeviceInfo>();

                    // 枚举设备
                    int nRet = DeviceEnumerator.EnumDevices(enumTLayerType, out deviceInfoList);
                    if (nRet != MvError.MV_OK)
                    {
                        OnErrorOccurred(nRet, "枚举设备失败");
                        return result;
                    }

                    // 转换为CameraDeviceInfo
                    for (int i = 0; i < deviceInfoList.Count; i++)
                    {
                        IDeviceInfo deviceInfo = deviceInfoList[i];
                        var cameraDeviceInfo = ConvertToDeviceInfo(deviceInfo, i);

                        // 根据设备类型过滤
                        if (!string.IsNullOrEmpty(deviceType))
                        {
                            if (!IsDeviceTypeMatch(cameraDeviceInfo, deviceType))
                                continue;
                        }

                        result.Add(cameraDeviceInfo);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"枚举设备异常: {ex.Message}", ex);
                    return new List<CameraDeviceInfo>();
                }
            }
        }

        public List<CameraDeviceInfo> EnumerateGigEDevices(string ipRange = null)
        {
            var allDevices = EnumerateDevices("GigE");

            if (string.IsNullOrEmpty(ipRange))
                return allDevices;

            // TODO: 实现IP范围过滤逻辑
            return allDevices;
        }

        public bool Connect(CameraDeviceInfo deviceInfo)
        {
            if (deviceInfo == null)
            {
                OnErrorOccurred(-1, "设备信息不能为空");
                return false;
            }

            return Connect(deviceInfo.DeviceIndex);
        }

        public bool Connect(int deviceIndex)
        {
            lock (_lockObject)
            {
                try
                {
                    if (_isConnected)
                    {
                        Disconnect();
                    }

                    if (deviceInfoList.Count == 0 || deviceIndex < 0 || deviceIndex >= deviceInfoList.Count)
                    {
                        OnErrorOccurred(-1, "无效的设备索引");
                        return false;
                    }

                    OnConnectionStatusChanged(ConnectionStatus.Connecting, $"正在连接设备 {deviceIndex}...");

                    // 获取设备信息
                    IDeviceInfo deviceInfo = deviceInfoList[deviceIndex];

                    try
                    {
                        // 创建设备
                        device = DeviceFactory.CreateDevice(deviceInfo);
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred(-1, $"创建设备失败: {ex.Message}", ex);
                        return false;
                    }

                    // 打开设备
                    int result = device.Open();
                    if (result != MvError.MV_OK)
                    {
                        OnErrorOccurred(result, "打开设备失败");
                        return false;
                    }

                    // GigE设备特殊处理
                    if (device is IGigEDevice)
                    {
                        IGigEDevice gigEDevice = device as IGigEDevice;

                        // 探测网络最佳包大小
                        int optionPacketSize;
                        result = gigEDevice.GetOptimalPacketSize(out optionPacketSize);
                        if (result == MvError.MV_OK)
                        {
                            result = device.Parameters.SetIntValue("GevSCPSPacketSize", (long)optionPacketSize);
                            if (result != MvError.MV_OK)
                            {
                                OnErrorOccurred(result, "设置包大小失败");
                            }
                        }
                    }

                    // 设置采集连续模式
                    device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                    device.Parameters.SetEnumValueByString("TriggerMode", "Off");

                    // 更新状态
                    _isConnected = true;
                    _currentDeviceInfo = ConvertToDeviceInfo(deviceInfo, deviceIndex);

                    OnConnectionStatusChanged(ConnectionStatus.Connected, "设备连接成功");

                    // 获取当前参数
                    UpdateParametersFromDevice();

                    return true;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"连接设备异常: {ex.Message}", ex);
                    OnConnectionStatusChanged(ConnectionStatus.Failed, "设备连接失败");
                    return false;
                }
            }
        }

        public bool ConnectByIP(string ipAddress, int port = 3956)
        {
            // 先枚举设备，然后查找匹配的IP地址
            var devices = EnumerateGigEDevices();
            var targetDevice = devices.Find(d => d.IpAddress == ipAddress && d.Port == port);

            if (targetDevice != null)
            {
                return Connect(targetDevice);
            }

            OnErrorOccurred(-1, $"未找到IP地址为 {ipAddress}:{port} 的设备");
            return false;
        }

        public bool ConnectBySerialNumber(string serialNumber)
        {
            var devices = EnumerateDevices();
            var targetDevice = devices.Find(d => d.SerialNumber == serialNumber);

            if (targetDevice != null)
            {
                return Connect(targetDevice);
            }

            OnErrorOccurred(-1, $"未找到序列号为 {serialNumber} 的设备");
            return false;
        }

        public bool ConnectByMacAddress(string macAddress)
        {
            var devices = EnumerateGigEDevices();
            var targetDevice = devices.Find(d => d.MacAddress == macAddress);

            if (targetDevice != null)
            {
                return Connect(targetDevice);
            }

            OnErrorOccurred(-1, $"未找到MAC地址为 {macAddress} 的设备");
            return false;
        }

        public void Disconnect()
        {
            lock (_lockObject)
            {
                try
                {
                    // 停止采集
                    if (isGrabbing)
                    {
                        StopGrabbing();
                    }

                    // 关闭设备
                    if (device != null)
                    {
                        device.Close();
                        device.Dispose();
                        device = null;
                    }

                    _isConnected = false;
                    _currentDeviceInfo = null;

                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, "设备已断开连接");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"断开设备连接异常: {ex.Message}", ex);
                }
            }
        }

        public bool PingCamera(string ipAddress, int timeout = 3000)
        {
            try
            {
                using (var ping = new System.Net.NetworkInformation.Ping())
                {
                    var reply = ping.Send(ipAddress, timeout);
                    return reply.Status == System.Net.NetworkInformation.IPStatus.Success;
                }
            }
            catch
            {
                return false;
            }
        }

        public bool ForceIP(string macAddress, string newIpAddress, string subnetMask, string gateway)
        {
            // TODO: 实现强制IP配置功能
            OnErrorOccurred(-1, "强制IP配置功能尚未实现");
            return false;
        }

        #endregion

        #region 图像采集

        public bool StartGrabbing()
        {
            lock (_lockObject)
            {
                try
                {
                    if (!_isConnected || device == null)
                    {
                        OnErrorOccurred(-1, "设备未连接");
                        return false;
                    }

                    if (isGrabbing)
                    {
                        return true; // 已经在采集中
                    }

                    // 标志位置位true
                    isGrabbing = true;

                    // 启动接收线程
                    receiveThread = new Thread(ReceiveThreadProcess);
                    receiveThread.Start();

                    // 开始采集
                    int result = device.StreamGrabber.StartGrabbing();
                    if (result != MvError.MV_OK)
                    {
                        isGrabbing = false;
                        if (receiveThread != null)
                        {
                            receiveThread.Join();
                            receiveThread = null;
                        }
                        OnErrorOccurred(result, "开始采集失败");
                        return false;
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    isGrabbing = false;
                    OnErrorOccurred(-1, $"开始采集异常: {ex.Message}", ex);
                    return false;
                }
            }
        }

        public bool StopGrabbing()
        {
            lock (_lockObject)
            {
                try
                {
                    if (!isGrabbing)
                    {
                        return true; // 已经停止
                    }

                    // 标志位清零
                    isGrabbing = false;

                    // 先停止设备采集，这样可以中断GetImageBuffer的阻塞
                    if (device != null)
                    {
                        int result = device.StreamGrabber.StopGrabbing();
                        if (result != MvError.MV_OK)
                        {
                            OnErrorOccurred(result, "停止采集失败");
                        }
                    }

                    // 等待接收线程结束，使用超时机制避免死锁
                    if (receiveThread != null)
                    {
                        // 使用超时等待，避免无限期阻塞UI线程
                        bool joined = receiveThread.Join(3000); // 等待最多3秒
                        if (!joined)
                        {
                            // 如果线程没有在超时时间内结束，强制中止（不推荐，但作为最后手段）
                            OnErrorOccurred(-1, "接收线程未能在超时时间内结束，可能存在阻塞");
                            // 注意：在.NET Core/.NET 5+中，Thread.Abort已被移除
                            // 这里我们只是记录警告，让线程自然结束
                        }
                        receiveThread = null;
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"停止采集异常: {ex.Message}", ex);
                    return false;
                }
            }
        }

        public Bitmap CaptureImage()
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return null;
            }

            try
            {
                IFrameOut frameOut;
                int result = device.StreamGrabber.GetImageBuffer(5000, out frameOut);
                if (result == MvError.MV_OK)
                {
                    var bitmap = frameOut.Image.ToBitmap();
                    device.StreamGrabber.FreeImageBuffer(frameOut);
                    return bitmap;
                }
                else
                {
                    OnErrorOccurred(result, "获取图像失败");
                    return null;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"捕获图像异常: {ex.Message}", ex);
                return null;
            }
        }

        public async Task<Bitmap> CaptureImageAsync()
        {
            return await Task.Run(() => CaptureImage());
        }

        private void ReceiveThreadProcess()
        {
            while (isGrabbing)
            {
                try
                {
                    IFrameOut frameOut;
                    // 移除不必要的长时间睡眠，这会导致StopGrabbing时的死锁
                    // Thread.Sleep(5000); // 删除这行
                    int nRet = device.StreamGrabber.GetImageBuffer(1000, out frameOut);
                    if (MvError.MV_OK == nRet)
                    {
                        // 录像处理
                        if (isRecord)
                        {
                            device.VideoRecorder.InputOneFrame(frameOut.Image);
                        }

                        // 保存帧信息用于图像保存
                        lock (saveImageLock)
                        {
                            try
                            {
                                frameForSave = frameOut.Clone() as IFrameOut;
                            }
                            catch (Exception e)
                            {
                                OnErrorOccurred(-1, $"克隆帧失败: {e.Message}", e);
                            }
                        }
                        // 触发图像接收事件
                        try
                        {
                            var bitmap = frameOut.Image.ToBitmap();
                            frameNumber++;
                            OnImageReceived(bitmap, frameNumber);
                        }
                        catch (Exception e)
                        {
                            OnErrorOccurred(-1, $"处理图像事件失败: {e.Message}", e);
                        }

                        device.StreamGrabber.FreeImageBuffer(frameOut);
                    }
                    else
                    {
                        // 在触发模式下稍作延时
                        if (GetTriggerMode() != TriggerMode.Continuous)
                        {
                            Thread.Sleep(5);
                        }
                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"接收线程异常: {ex.Message}", ex);
                    break;
                }
            }
        }

        #endregion

        #region 参数控制

        public bool SetExposureTime(float exposureTime)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                device.Parameters.SetEnumValue("ExposureAuto", 0);
                int result = device.Parameters.SetFloatValue("ExposureTime", exposureTime);
                if (result == MvError.MV_OK)
                {
                    _parameters.ExposureTime = exposureTime;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "设置曝光时间失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置曝光时间异常: {ex.Message}", ex);
                return false;
            }
        }

        public float GetExposureTime()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.ExposureTime;
            }

            try
            {
                IFloatValue floatValue;
                int result = device.Parameters.GetFloatValue("ExposureTime", out floatValue);
                if (result == MvError.MV_OK)
                {
                    _parameters.ExposureTime = floatValue.CurValue;
                    return floatValue.CurValue;
                }
                else
                {
                    OnErrorOccurred(result, "获取曝光时间失败");
                    return _parameters.ExposureTime;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取曝光时间异常: {ex.Message}", ex);
                return _parameters.ExposureTime;
            }
        }

        public bool SetGain(float gain)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                device.Parameters.SetEnumValue("GainAuto", 0);
                int result = device.Parameters.SetFloatValue("Gain", gain);
                if (result == MvError.MV_OK)
                {
                    _parameters.Gain = gain;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "设置增益失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置增益异常: {ex.Message}", ex);
                return false;
            }
        }

        public float GetGain()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.Gain;
            }

            try
            {
                IFloatValue floatValue;
                int result = device.Parameters.GetFloatValue("Gain", out floatValue);
                if (result == MvError.MV_OK)
                {
                    _parameters.Gain = floatValue.CurValue;
                    return floatValue.CurValue;
                }
                else
                {
                    OnErrorOccurred(result, "获取增益失败");
                    return _parameters.Gain;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取增益异常: {ex.Message}", ex);
                return _parameters.Gain;
            }
        }

        public bool SetResolution(int width, int height)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result1 = device.Parameters.SetIntValue("Width", width);
                int result2 = device.Parameters.SetIntValue("Height", height);

                if (result1 == MvError.MV_OK && result2 == MvError.MV_OK)
                {
                    _parameters.Width = width;
                    _parameters.Height = height;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result1 != MvError.MV_OK ? result1 : result2, "设置分辨率失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置分辨率异常: {ex.Message}", ex);
                return false;
            }
        }

        public System.Drawing.Size GetResolution()
        {
            if (!_isConnected || device == null)
            {
                return new System.Drawing.Size(_parameters.Width, _parameters.Height);
            }

            try
            {
                IIntValue intValue;
                int result1 = device.Parameters.GetIntValue("Width", out intValue);
                int width = result1 == MvError.MV_OK ? (int)intValue.CurValue : _parameters.Width;

                int result2 = device.Parameters.GetIntValue("Height", out intValue);
                int height = result2 == MvError.MV_OK ? (int)intValue.CurValue : _parameters.Height;

                _parameters.Width = width;
                _parameters.Height = height;
                return new System.Drawing.Size(width, height);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取分辨率异常: {ex.Message}", ex);
                return new System.Drawing.Size(_parameters.Width, _parameters.Height);
            }
        }

        public bool SetROI(int x, int y, int width, int height)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result1 = device.Parameters.SetIntValue("OffsetX", x);
                int result2 = device.Parameters.SetIntValue("OffsetY", y);
                int result3 = device.Parameters.SetIntValue("Width", width);
                int result4 = device.Parameters.SetIntValue("Height", height);

                if (result1 == MvError.MV_OK && result2 == MvError.MV_OK &&
                    result3 == MvError.MV_OK && result4 == MvError.MV_OK)
                {
                    _parameters.ROI = new Rectangle(x, y, width, height);
                    return true;
                }
                else
                {
                    OnErrorOccurred(-1, "设置ROI失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置ROI异常: {ex.Message}", ex);
                return false;
            }
        }

        public Rectangle GetROI()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.ROI;
            }

            try
            {
                IIntValue intValue;
                int x = 0, y = 0, width = 0, height = 0;

                if (device.Parameters.GetIntValue("OffsetX", out intValue) == MvError.MV_OK)
                    x = (int)intValue.CurValue;
                if (device.Parameters.GetIntValue("OffsetY", out intValue) == MvError.MV_OK)
                    y = (int)intValue.CurValue;
                if (device.Parameters.GetIntValue("Width", out intValue) == MvError.MV_OK)
                    width = (int)intValue.CurValue;
                if (device.Parameters.GetIntValue("Height", out intValue) == MvError.MV_OK)
                    height = (int)intValue.CurValue;

                _parameters.ROI = new Rectangle(x, y, width, height);
                return _parameters.ROI;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取ROI异常: {ex.Message}", ex);
                return _parameters.ROI;
            }
        }

        public bool SetFrameRate(float frameRate)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result = device.Parameters.SetFloatValue("AcquisitionFrameRate", frameRate);
                if (result == MvError.MV_OK)
                {
                    _parameters.FrameRate = frameRate;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "设置帧率失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置帧率异常: {ex.Message}", ex);
                return false;
            }
        }

        public float GetFrameRate()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.FrameRate;
            }

            try
            {
                IFloatValue floatValue;
                int result = device.Parameters.GetFloatValue("ResultingFrameRate", out floatValue);
                if (result == MvError.MV_OK)
                {
                    _parameters.FrameRate = floatValue.CurValue;
                    return floatValue.CurValue;
                }
                else
                {
                    OnErrorOccurred(result, "获取帧率失败");
                    return _parameters.FrameRate;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取帧率异常: {ex.Message}", ex);
                return _parameters.FrameRate;
            }
        }

        public bool SetTriggerMode(TriggerMode triggerMode)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result;
                switch (triggerMode)
                {
                    case TriggerMode.Continuous:
                        result = device.Parameters.SetEnumValueByString("TriggerMode", "Off");
                        break;
                    case TriggerMode.Software:
                        result = device.Parameters.SetEnumValueByString("TriggerMode", "On");
                        if (result == MvError.MV_OK)
                        {
                            result = device.Parameters.SetEnumValueByString("TriggerSource", "Software");
                        }
                        break;
                    case TriggerMode.Hardware:
                        result = device.Parameters.SetEnumValueByString("TriggerMode", "On");
                        if (result == MvError.MV_OK)
                        {
                            result = device.Parameters.SetEnumValueByString("TriggerSource", "Line0");
                        }
                        break;
                    default:
                        OnErrorOccurred(-1, "不支持的触发模式");
                        return false;
                }

                if (result == MvError.MV_OK)
                {
                    _parameters.TriggerMode = triggerMode;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "设置触发模式失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置触发模式异常: {ex.Message}", ex);
                return false;
            }
        }

        public TriggerMode GetTriggerMode()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.TriggerMode;
            }

            try
            {
                IEnumValue enumValue;
                int result = device.Parameters.GetEnumValue("TriggerMode", out enumValue);
                if (result == MvError.MV_OK)
                {
                    if (enumValue.CurEnumEntry.Symbolic == "Off")
                    {
                        _parameters.TriggerMode = TriggerMode.Continuous;
                    }
                    else // On
                    {
                        // 检查触发源
                        result = device.Parameters.GetEnumValue("TriggerSource", out enumValue);
                        if (result == MvError.MV_OK)
                        {
                            if (enumValue.CurEnumEntry.Symbolic == "TriggerSoftware")
                            {
                                _parameters.TriggerMode = TriggerMode.Software;
                            }
                            else
                            {
                                _parameters.TriggerMode = TriggerMode.Hardware;
                            }
                        }
                    }
                }
                return _parameters.TriggerMode;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取触发模式异常: {ex.Message}", ex);
                return _parameters.TriggerMode;
            }
        }

        public bool SoftwareTrigger()
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result = device.Parameters.SetCommandValue("TriggerSoftware");
                if (result == MvError.MV_OK)
                {
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "软件触发失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"软件触发异常: {ex.Message}", ex);
                return false;
            }
        }

        #endregion

        #region 高级功能

        public ParameterRange GetParameterRange(string parameterName)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return null;
            }

            try
            {
                // 根据参数名称获取不同类型的参数范围
                switch (parameterName.ToLower())
                {
                    case "exposuretime":
                        IFloatValue floatValue;
                        if (device.Parameters.GetFloatValue("ExposureTime", out floatValue) == MvError.MV_OK)
                        {
                            return new ParameterRange
                            {
                                MinValue = floatValue.Min,
                                MaxValue = floatValue.Max,
                                CurrentValue = floatValue.CurValue,
                                Unit = "μs",
                                Description = "曝光时间"
                            };
                        }
                        break;
                    case "gain":
                        if (device.Parameters.GetFloatValue("Gain", out floatValue) == MvError.MV_OK)
                        {
                            return new ParameterRange
                            {
                                MinValue = floatValue.Min,
                                MaxValue = floatValue.Max,
                                CurrentValue = floatValue.CurValue,
                                Unit = "dB",
                                Description = "增益"
                            };
                        }
                        break;
                    default:
                        OnErrorOccurred(-1, $"不支持的参数: {parameterName}");
                        break;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取参数范围异常: {ex.Message}", ex);
            }

            return null;
        }

        public bool SetParameter(string parameterName, object value)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                // 根据参数类型设置值
                if (value is float floatValue)
                {
                    int result = device.Parameters.SetFloatValue(parameterName, floatValue);
                    return result == MvError.MV_OK;
                }
                else if (value is int intValue)
                {
                    int result = device.Parameters.SetIntValue(parameterName, intValue);
                    return result == MvError.MV_OK;
                }
                else if (value is string stringValue)
                {
                    int result = device.Parameters.SetEnumValueByString(parameterName, stringValue);
                    return result == MvError.MV_OK;
                }
                else
                {
                    OnErrorOccurred(-1, "不支持的参数类型");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置参数异常: {ex.Message}", ex);
                return false;
            }
        }

        public object GetParameter(string parameterName)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return null;
            }

            try
            {
                // 尝试不同类型的参数获取
                IFloatValue floatValue;
                if (device.Parameters.GetFloatValue(parameterName, out floatValue) == MvError.MV_OK)
                {
                    return floatValue.CurValue;
                }

                IIntValue intValue;
                if (device.Parameters.GetIntValue(parameterName, out intValue) == MvError.MV_OK)
                {
                    return intValue.CurValue;
                }

                IEnumValue enumValue;
                if (device.Parameters.GetEnumValue(parameterName, out enumValue) == MvError.MV_OK)
                {
                    return enumValue.CurEnumEntry.Value;
                }

                OnErrorOccurred(-1, $"无法获取参数: {parameterName}");
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取参数异常: {ex.Message}", ex);
                return null;
            }
        }

        public bool SaveParametersToFile(string filePath)
        {
            // TODO: 实现参数保存到文件功能
            OnErrorOccurred(-1, "参数保存功能尚未实现");
            return false;
        }

        public bool LoadParametersFromFile(string filePath)
        {
            // TODO: 实现从文件加载参数功能
            OnErrorOccurred(-1, "参数加载功能尚未实现");
            return false;
        }

        public bool ResetParameters()
        {
            // TODO: 实现参数重置功能
            OnErrorOccurred(-1, "参数重置功能尚未实现");
            return false;
        }

        public bool RegisterImageCallback(Action<Bitmap, long> callback)
        {
            // 通过事件机制实现回调
            if (callback != null)
            {
                ImageReceived += (sender, args) => callback(args.Image, args.ImageNumber);
                return true;
            }
            return false;
        }

        public bool UnregisterImageCallback()
        {
            // 清除所有事件订阅
            ImageReceived = null;
            return true;
        }

        public float GetDeviceTemperature()
        {
            // TODO: 实现设备温度获取功能
            return 0.0f;
        }

        public Dictionary<string, object> GetDeviceStatistics()
        {
            var stats = new Dictionary<string, object>();

            if (_isConnected && device != null)
            {
                stats["IsConnected"] = true;
                stats["IsGrabbing"] = isGrabbing;
                stats["FrameNumber"] = frameNumber;
                stats["DeviceName"] = _currentDeviceInfo?.DeviceName ?? "Unknown";
                stats["SerialNumber"] = _currentDeviceInfo?.SerialNumber ?? "Unknown";
            }
            else
            {
                stats["IsConnected"] = false;
            }

            return stats;
        }

        public bool SetPixelFormat(string pixelFormat)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                int result = device.Parameters.SetEnumValueByString("PixelFormat", pixelFormat);
                if (result == MvError.MV_OK)
                {
                    _parameters.PixelFormat = pixelFormat;
                    return true;
                }
                else
                {
                    OnErrorOccurred(result, "设置像素格式失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置像素格式异常: {ex.Message}", ex);
                return false;
            }
        }

        public string GetPixelFormat()
        {
            if (!_isConnected || device == null)
            {
                return _parameters.PixelFormat;
            }

            try
            {
                IEnumValue enumValue;
                int result = device.Parameters.GetEnumValue("PixelFormat", out enumValue);
                if (result == MvError.MV_OK)
                {
                    // 返回当前枚举项的符号名称
                    return enumValue.CurEnumEntry.Symbolic;
                }
                else
                {
                    OnErrorOccurred(result, "获取像素格式失败");
                    return _parameters.PixelFormat;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取像素格式异常: {ex.Message}", ex);
                return _parameters.PixelFormat;
            }
        }

        public List<string> GetSupportedPixelFormats()
        {
            var formats = new List<string>();

            if (!_isConnected || device == null)
            {
                // 返回常见的像素格式
                formats.AddRange(new[] { "Mono8", "Mono10", "Mono12", "RGB8", "BGR8", "YUV422" });
                return formats;
            }

            try
            {
                IEnumValue enumValue;
                int result = device.Parameters.GetEnumValue("PixelFormat", out enumValue);
                if (result == MvError.MV_OK)
                {
                    // 获取所有支持的枚举项
                    foreach (var item in enumValue.SupportEnumEntries)
                    {
                        formats.Add(item.Symbolic);
                    }
                }
                else
                {
                    // 如果获取失败，返回常见格式
                    formats.AddRange(new[] { "Mono8", "Mono10", "Mono12", "RGB8", "BGR8", "YUV422" });
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取支持的像素格式异常: {ex.Message}", ex);
                // 异常时返回常见格式
                formats.AddRange(new[] { "Mono8", "Mono10", "Mono12", "RGB8", "BGR8", "YUV422" });
            }

            return formats;
        }

        public bool SetWhiteBalance(string mode)
        {
            // TODO: 实现白平衡设置功能
            OnErrorOccurred(-1, "白平衡设置功能尚未实现");
            return false;
        }

        public bool ExecuteWhiteBalance()
        {
            // TODO: 实现执行白平衡功能
            OnErrorOccurred(-1, "执行白平衡功能尚未实现");
            return false;
        }

        public bool SaveImage(string filePath, string format = "PNG")
        {
            lock (saveImageLock)
            {
                if (frameForSave == null)
                {
                    OnErrorOccurred(-1, "没有可保存的图像");
                    return false;
                }

                try
                {
                    ImageFormatInfo imageFormatInfo = new ImageFormatInfo();

                    switch (format.ToUpper())
                    {
                        case "BMP":
                            imageFormatInfo.FormatType = ImageFormatType.Bmp;
                            break;
                        case "JPG":
                        case "JPEG":
                            imageFormatInfo.FormatType = ImageFormatType.Jpeg;
                            imageFormatInfo.JpegQuality = 80;
                            break;
                        case "TIFF":
                            imageFormatInfo.FormatType = ImageFormatType.Tiff;
                            break;
                        case "PNG":
                        default:
                            imageFormatInfo.FormatType = ImageFormatType.Png;
                            break;
                    }

                    int result = device.ImageSaver.SaveImageToFile(filePath, frameForSave.Image, imageFormatInfo, CFAMethod.Equilibrated);
                    return result == MvError.MV_OK;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"保存图像异常: {ex.Message}", ex);
                    return false;
                }
            }
        }

        public bool StartRecording(string filePath, string codec = "H264", float frameRate = 30)
        {
            if (!_isConnected || device == null)
            {
                OnErrorOccurred(-1, "设备未连接");
                return false;
            }

            try
            {
                if (isRecord)
                {
                    return true; // 已经在录制中
                }
                IIntValue intValue;
                IEnumValue enumValue;

                uint width;
                uint height;
                MvGvspPixelType pixelType;

                int result;

                result = device.Parameters.GetIntValue("Width", out intValue);
                if (result != MvError.MV_OK)
                {
                    OnErrorOccurred(-1, "获取宽度失败");
                    return false;
                }
                width = (uint)intValue.CurValue;

                result = device.Parameters.GetIntValue("Height", out intValue);
                if (result != MvError.MV_OK)
                {
                    OnErrorOccurred(-1, "获取高度失败");
                    return false;
                }
                height = (uint)intValue.CurValue;

                result = device.Parameters.GetEnumValue("PixelFormat", out enumValue);
                if (result != MvError.MV_OK)
                {
                    OnErrorOccurred(-1, "获取PixelForma失败");
                    return false;
                }
                pixelType = (MvGvspPixelType)enumValue.CurEnumEntry.Value;

                // ch:开始录像 | en:Start record
                RecordParam recordParam;
                recordParam.Width = width;
                recordParam.Height = height;
                recordParam.PixelType = pixelType;
                recordParam.FrameRate = frameRate;
                recordParam.BitRate = 1000;
                recordParam.FormatType = VideoFormatType.AVI;

                result = device.VideoRecorder.StartRecord(filePath, recordParam);
                if (result != MvError.MV_OK)
                {
                    OnErrorOccurred(-1, "录像失败");
                    return false;
                }
                // TODO: 配置录像参数并开始录制
                isRecord = true;
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"开始录制异常: {ex.Message}", ex);
                return false;
            }
        }

        public bool StopRecording()
        {
            try
            {
                if (!isRecord)
                {
                    return true; // 已经停止
                }
                int result = device.VideoRecorder.StopRecord();
                if (result != MvError.MV_OK)
                {
                    OnErrorOccurred(-1, "Stop Record Fail!");
                }

                // TODO: 停止录制
                isRecord = false;
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"停止录制异常: {ex.Message}", ex);
                return false;
            }
        }

        public Dictionary<string, object> GetNetworkStatistics()
        {
            var stats = new Dictionary<string, object>();

            if (_isConnected && device != null && device is IGigEDevice)
            {
                // TODO: 实现网络统计信息获取
                stats["PacketSize"] = 1500;
                stats["PacketLoss"] = 0;
                stats["Bandwidth"] = 1000000000; // 1Gbps
            }

            return stats;
        }

        public bool RestartDevice()
        {
            // TODO: 实现设备重启功能
            OnErrorOccurred(-1, "设备重启功能尚未实现");
            return false;
        }

        public string GetDeviceLog()
        {
            // TODO: 实现设备日志获取功能
            return "设备日志功能尚未实现";
        }

        #endregion

        #region 辅助方法

        private CameraDeviceInfo ConvertToDeviceInfo(IDeviceInfo deviceInfo, int index)
        {
            var cameraDeviceInfo = new CameraDeviceInfo
            {
                DeviceIndex = index,
                SerialNumber = deviceInfo.SerialNumber,
                ManufacturerName = deviceInfo.ManufacturerName,
                ModelName = deviceInfo.ModelName,
                UserDefinedName = deviceInfo.UserDefinedName,
                DeviceVersion = deviceInfo.DeviceVersion,
                IsAccessible = true,
                IsOnline = true,
                LastDiscovered = DateTime.Now
            };

            // 根据TLayerType设置设备类型和连接信息
            switch (deviceInfo.TLayerType)
            {
                case DeviceTLayerType.MvGigEDevice:
                case DeviceTLayerType.MvGenTLGigEDevice:
                    cameraDeviceInfo.DeviceType = DeviceType.GigE;
                    cameraDeviceInfo.ConnectionType = "GigE";
                    // TODO: 从deviceInfo中提取IP地址等网络信息
                    break;
                case DeviceTLayerType.MvUsbDevice:
                    cameraDeviceInfo.DeviceType = DeviceType.USB3;
                    cameraDeviceInfo.ConnectionType = "USB3";
                    break;
                case DeviceTLayerType.MvGenTLCameraLinkDevice:
                    cameraDeviceInfo.DeviceType = DeviceType.CameraLink;
                    cameraDeviceInfo.ConnectionType = "CameraLink";
                    break;
                case DeviceTLayerType.MvGenTLCXPDevice:
                    cameraDeviceInfo.DeviceType = DeviceType.CoaXPress;
                    cameraDeviceInfo.ConnectionType = "CoaXPress";
                    break;
                default:
                    cameraDeviceInfo.DeviceType = DeviceType.Unknown;
                    cameraDeviceInfo.ConnectionType = "Unknown";
                    break;
            }

            // 设置显示名称
            if (!string.IsNullOrEmpty(deviceInfo.UserDefinedName))
            {
                cameraDeviceInfo.DeviceName = deviceInfo.UserDefinedName;
            }
            else
            {
                cameraDeviceInfo.DeviceName = $"{deviceInfo.ManufacturerName} {deviceInfo.ModelName}";
            }

            return cameraDeviceInfo;
        }

        private bool IsDeviceTypeMatch(CameraDeviceInfo deviceInfo, string deviceType)
        {
            switch (deviceType.ToUpper())
            {
                case "GIGE":
                    return deviceInfo.DeviceType == DeviceType.GigE;
                case "USB3":
                    return deviceInfo.DeviceType == DeviceType.USB3;
                case "USB2":
                    return deviceInfo.DeviceType == DeviceType.USB2;
                case "CAMERALINK":
                    return deviceInfo.DeviceType == DeviceType.CameraLink;
                case "COAXPRESS":
                    return deviceInfo.DeviceType == DeviceType.CoaXPress;
                default:
                    return true; // 如果不匹配任何已知类型，则包含所有设备
            }
        }

        private void UpdateParametersFromDevice()
        {
            if (!_isConnected || device == null)
                return;

            try
            {
                // 更新曝光时间
                _parameters.ExposureTime = GetExposureTime();

                // 更新增益
                _parameters.Gain = GetGain();

                // 更新帧率
                _parameters.FrameRate = GetFrameRate();

                // 更新分辨率
                var resolution = GetResolution();
                _parameters.Width = resolution.Width;
                _parameters.Height = resolution.Height;

                // 更新触发模式
                _parameters.TriggerMode = GetTriggerMode();

                // 更新像素格式
                _parameters.PixelFormat = GetPixelFormat();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"更新参数异常: {ex.Message}", ex);
            }
        }

        #endregion

        #region 事件触发方法

        private void OnImageReceived(Bitmap image, long imageNumber)
        {
            try
            {
                var args = new ImageReceivedEventArgs
                {
                    Image = image,
                    ImageNumber = imageNumber,
                    Timestamp = DateTime.Now,
                    Width = image?.Width ?? 0,
                    Height = image?.Height ?? 0,
                    PixelFormat = Models.PixelFormat.RGB8 // 默认格式，实际应该从设备获取
                };
                ImageReceived?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"触发图像接收事件异常: {ex.Message}", ex);
            }
        }

        private void OnConnectionStatusChanged(ConnectionStatus status, string message)
        {
            try
            {
                var args = new ConnectionStatusChangedEventArgs
                {
                    Status = status,
                    Message = message,
                    DeviceInfo = _currentDeviceInfo
                };
                ConnectionStatusChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                // 避免在错误处理中再次触发错误
                System.Diagnostics.Debug.WriteLine($"触发连接状态事件异常: {ex.Message}");
            }
        }

        private void OnErrorOccurred(int errorCode, string errorMessage, Exception exception = null)
        {
            try
            {
                var args = new ErrorOccurredEventArgs
                {
                    ErrorCode = errorCode,
                    ErrorMessage = errorMessage,
                    Exception = exception,
                    Timestamp = DateTime.Now
                };
                ErrorOccurred?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                // 避免在错误处理中再次触发错误
                System.Diagnostics.Debug.WriteLine($"触发错误事件异常: {ex.Message}");
            }
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 停止采集
                        if (isGrabbing)
                        {
                            StopGrabbing();
                        }

                        // 断开连接
                        if (_isConnected)
                        {
                            Disconnect();
                        }

                        // 清理资源
                        frameForSave?.Dispose();
                        frameForSave = null;

                        // 清理事件订阅
                        ImageReceived = null;
                        ConnectionStatusChanged = null;
                        ErrorOccurred = null;

                        // 终结化SDK
                        SDKSystem.Finalize();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放资源异常: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        ~TestSDKCameraController()
        {
            Dispose(false);
        }

        #endregion
    }
}
