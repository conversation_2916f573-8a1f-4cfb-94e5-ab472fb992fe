using CameraTechVerify.Interfaces;
using CameraTechVerify.Implementations;

namespace CameraTechVerify.Factories
{
    /// <summary>
    /// 视觉算法引擎工厂类
    /// </summary>
    public static class VisionEngineFactory
    {
        /// <summary>
        /// 支持的引擎类型
        /// </summary>
        public enum EngineType
        {
            Halcon,
            OpenCV,
            Custom
        }

        /// <summary>
        /// 创建视觉算法引擎
        /// </summary>
        /// <param name="engineType">引擎类型</param>
        /// <param name="config">配置参数</param>
        /// <returns>视觉算法引擎实例</returns>
        public static IVisionAlgorithmEngine CreateEngine(EngineType engineType, Dictionary<string, object> config = null)
        {
            switch (engineType)
            {
                case EngineType.Halcon:
                    var halconEngine = new HalconVisionEngine();
                    if (halconEngine.Initialize(config))
                    {
                        return halconEngine;
                    }
                    halconEngine.Dispose();
                    throw new InvalidOperationException("Failed to initialize Halcon engine");

                case EngineType.OpenCV:
                    // 这里可以添加 OpenCV 引擎的实现
                    // var openCvEngine = new OpenCVVisionEngine();
                    // if (openCvEngine.Initialize(config))
                    // {
                    //     return openCvEngine;
                    // }
                    // openCvEngine.Dispose();
                    throw new NotImplementedException("OpenCV engine not implemented yet");

                case EngineType.Custom:
                    // 这里可以添加自定义引擎的实现
                    throw new NotImplementedException("Custom engine not implemented yet");

                default:
                    throw new ArgumentException($"Unsupported engine type: {engineType}");
            }
        }

        /// <summary>
        /// 获取可用的引擎类型列表
        /// </summary>
        /// <returns>引擎类型列表</returns>
        public static List<EngineType> GetAvailableEngines()
        {
            var engines = new List<EngineType>();

            // 检查 Halcon 是否可用
            try
            {
                using (var testEngine = new HalconVisionEngine())
                {
                    if (testEngine.Initialize())
                    {
                        engines.Add(EngineType.Halcon);
                    }
                }
            }
            catch
            {
                // Halcon 不可用
            }

            // 检查其他引擎...

            return engines;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <param name="engineType">引擎类型</param>
        /// <returns>默认配置</returns>
        public static Dictionary<string, object> CreateDefaultConfig(EngineType engineType)
        {
            var config = new Dictionary<string, object>();

            switch (engineType)
            {
                case EngineType.Halcon:
                    config["width"] = 512;
                    config["height"] = 512;
                    config["use_window_thread"] = true;
                    break;

                case EngineType.OpenCV:
                    // OpenCV 默认配置
                    break;

                case EngineType.Custom:
                    // 自定义引擎默认配置
                    break;
            }

            return config;
        }
    }

    /// <summary>
    /// 视觉算法引擎管理器
    /// </summary>
    public class VisionEngineManager : IDisposable
    {
        private readonly Dictionary<string, IVisionAlgorithmEngine> _engines = new Dictionary<string, IVisionAlgorithmEngine>();
        private bool _disposed = false;

        /// <summary>
        /// 注册引擎
        /// </summary>
        /// <param name="name">引擎名称</param>
        /// <param name="engine">引擎实例</param>
        public void RegisterEngine(string name, IVisionAlgorithmEngine engine)
        {
            if (_engines.ContainsKey(name))
            {
                _engines[name].Dispose();
            }
            _engines[name] = engine;
        }

        /// <summary>
        /// 获取引擎
        /// </summary>
        /// <param name="name">引擎名称</param>
        /// <returns>引擎实例</returns>
        public IVisionAlgorithmEngine GetEngine(string name)
        {
            return _engines.TryGetValue(name, out IVisionAlgorithmEngine engine) ? engine : null;
        }

        /// <summary>
        /// 移除引擎
        /// </summary>
        /// <param name="name">引擎名称</param>
        public void RemoveEngine(string name)
        {
            if (_engines.TryGetValue(name, out IVisionAlgorithmEngine engine))
            {
                engine.Dispose();
                _engines.Remove(name);
            }
        }

        /// <summary>
        /// 获取所有引擎名称
        /// </summary>
        /// <returns>引擎名称列表</returns>
        public List<string> GetEngineNames()
        {
            return new List<string>(_engines.Keys);
        }

        /// <summary>
        /// 创建并注册引擎
        /// </summary>
        /// <param name="name">引擎名称</param>
        /// <param name="engineType">引擎类型</param>
        /// <param name="config">配置参数</param>
        /// <returns>是否成功</returns>
        public bool CreateAndRegisterEngine(string name, VisionEngineFactory.EngineType engineType, Dictionary<string, object> config = null)
        {
            try
            {
                var engine = VisionEngineFactory.CreateEngine(engineType, config);
                RegisterEngine(name, engine);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    foreach (var engine in _engines.Values)
                    {
                        engine?.Dispose();
                    }
                    _engines.Clear();
                }
                _disposed = true;
            }
        }

        ~VisionEngineManager()
        {
            Dispose(false);
        }
    }
}
