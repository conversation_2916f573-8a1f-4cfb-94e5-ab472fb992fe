<Window x:Class="HalconAlgorithmManager.Dialogs.NewVersionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="新建版本" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        Background="#FF2D2D30"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF1E1E1E" BorderBrush="#FF007ACC" BorderThickness="0,0,0,2" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="24" Foreground="#FF007ACC" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="新建版本" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="Create New Version" FontSize="12" Foreground="LightGray" Margin="0,2,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主内容区 -->
        <StackPanel Grid.Row="1" Margin="30,20">
            
            <!-- 版本信息 -->
            <GroupBox Header="📋 版本信息" Foreground="White" Margin="0,10">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="版本号:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="VersionNumberTextBox" Height="30" Margin="10,10,0,10"
                            Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                            FontSize="12" Text="v2.2.0" ToolTip="版本号格式：v主版本.次版本.修订版本"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="版本类型:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                    <ComboBox Grid.Row="1" Grid.Column="1" x:Name="VersionTypeComboBox" Height="30" Margin="10,10,0,10"
                             Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="12">
                        <ComboBoxItem Content="开发版本 (dev)" IsSelected="True"/>
                        <ComboBoxItem Content="测试版本 (beta)"/>
                        <ComboBoxItem Content="候选版本 (rc)"/>
                        <ComboBoxItem Content="正式版本 (release)"/>
                    </ComboBox>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="基于版本:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                    <ComboBox Grid.Row="2" Grid.Column="1" x:Name="BaseVersionComboBox" Height="30" Margin="10,10,0,10"
                             Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="12">
                        <ComboBoxItem Content="v2.1.0 (当前版本)" IsSelected="True"/>
                        <ComboBoxItem Content="v2.0.3"/>
                        <ComboBoxItem Content="v2.0.2-beta"/>
                        <ComboBoxItem Content="v2.0.1"/>
                    </ComboBox>
                </Grid>
            </GroupBox>

            <!-- 版本描述 -->
            <GroupBox Header="📝 版本描述" Foreground="White" Margin="0,10">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="变更说明:" Foreground="LightGray" FontSize="14" Margin="0,0,0,10"/>
                    <TextBox Grid.Row="1" x:Name="DescriptionTextBox" Height="120" 
                            Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                            FontSize="12" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                            Text="请输入本版本的主要变更内容：&#x0a;&#x0a;新功能：&#x0a;- &#x0a;&#x0a;优化改进：&#x0a;- &#x0a;&#x0a;问题修复：&#x0a;- "/>
                </Grid>
            </GroupBox>

        </StackPanel>

        <!-- 按钮区域 -->
        <Border Grid.Row="2" Background="#FF3F3F46" BorderBrush="#FF5A5A5A" BorderThickness="0,1,0,0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="✅ 创建版本" Width="100" Height="35" Margin="10,0"
                       Background="#FF28A745" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Create_Click"/>
                <Button Content="❌ 取消" Width="80" Height="35" Margin="10,0"
                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
