using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using CameraTechVerify.Interfaces;
using CameraTechVerify.Models;
using CameraTechVerify.MVS;
using static CameraTechVerify.MVS.MVSStructures;

namespace CameraTechVerify.Implementations
{
    /// <summary>
    /// MVS 相机控制器实现
    /// 基于真实的 MvCameraControl.Net SDK
    /// </summary>
    public class MVSCameraController : ICameraController
    {
        #region 私有字段

        private object _camera = null; // MVS 相机实例
        private bool _isConnected = false;
        private bool _isGrabbing = false;
        private CameraDeviceInfo _currentDeviceInfo;
        private CameraParameters _parameters;
        private long _frameNumber = 0;
        private readonly object _lockObject = new object();

        // MVS API 包装器
        private MVSApiWrapper _mvsApi;
        private MV_CC_DEVICE_INFO _currentDeviceStruct;

        // 通过反射获取的 MVS API 方法
        private Type _cameraType;
        private System.Reflection.MethodInfo _enumDevicesMethod;
        private System.Reflection.MethodInfo _createDeviceMethod;
        private System.Reflection.MethodInfo _openDeviceMethod;
        private System.Reflection.MethodInfo _closeDeviceMethod;
        private System.Reflection.MethodInfo _destroyDeviceMethod;
        private System.Reflection.MethodInfo _startGrabbingMethod;
        private System.Reflection.MethodInfo _stopGrabbingMethod;
        private System.Reflection.MethodInfo _getOneFrameMethod;
        private System.Reflection.MethodInfo _setFloatValueMethod;
        private System.Reflection.MethodInfo _getFloatValueMethod;
        private System.Reflection.MethodInfo _setIntValueMethod;
        private System.Reflection.MethodInfo _getIntValueMethod;
        private System.Reflection.MethodInfo _setBoolValueMethod;
        private System.Reflection.MethodInfo _getBoolValueMethod;
        private System.Reflection.MethodInfo _setEnumValueMethod;
        private System.Reflection.MethodInfo _getEnumValueMethod;
        private System.Reflection.MethodInfo _triggerSoftwareMethod;

        // 图像回调
        private Action<Bitmap, long> _imageCallback;
        private cbOutputExdelegate _mvsImageCallback;

        #endregion

        #region 事件

        public event EventHandler<ImageReceivedEventArgs> ImageReceived;
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        public event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        #endregion

        #region 属性

        public bool IsConnected => _isConnected;
        public bool IsGrabbing => _isGrabbing;
        public CameraDeviceInfo DeviceInfo => _currentDeviceInfo;
        
        public CameraParameters Parameters 
        { 
            get => _parameters; 
            set 
            { 
                _parameters = value;
                ApplyParameters();
            } 
        }

        #endregion

        #region 构造函数

        public MVSCameraController()
        {
            // 首先运行 MVS SDK 加载测试
            Test.MVSLoadTest.TestMVSLoading();

            _parameters = new CameraParameters();
            _mvsApi = new MVSApiWrapper();
            InitializeMVSAPI();
        }

        #endregion

        #region MVS API 初始化

        /// <summary>
        /// 初始化 MVS API 反射调用
        /// </summary>
        private void InitializeMVSAPI()
        {
            try
            {
                // 先测试 LogMessage 是否工作
                System.Diagnostics.Debug.WriteLine("DEBUG: 开始初始化 MVS API...");
                LogMessage("开始初始化 MVS API...");

                // 方法1：尝试从已加载的程序集中查找
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                var mvsAssembly = assemblies.FirstOrDefault(a => a.GetName().Name.Contains("MvCameraControl"));

                // 方法2：如果没找到，尝试强制加载程序集
                if (mvsAssembly == null)
                {
                    LogMessage("在已加载程序集中未找到 MVS SDK，尝试强制加载...");
                    mvsAssembly = TryLoadMVSAssembly();
                }

                // 方法3：如果还是没找到，尝试从引用的程序集加载
                if (mvsAssembly == null)
                {
                    LogMessage("尝试从引用程序集加载 MVS SDK...");
                    mvsAssembly = TryLoadFromReferencedAssemblies();
                }

                if (mvsAssembly == null)
                {
                    LogMessage("警告：未找到 MvCameraControl.Net 程序集，将使用模拟模式");
                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, "MVS API 未找到，使用模拟模式");
                    return;
                }

                LogMessage($"✓ 找到 MVS 程序集: {mvsAssembly.GetName().Name}");

                // 获取所有公共类型
                var types = mvsAssembly.GetExportedTypes();
                LogMessage($"程序集包含 {types.Length} 个公共类型");

                // 查找相机类型
                _cameraType = FindCameraType(types);

                if (_cameraType == null)
                {
                    LogMessage("警告：未找到相机类型，将使用模拟模式");
                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, "相机类型未找到，使用模拟模式");
                    return;
                }

                LogMessage($"✓ 找到相机类型: {_cameraType.Name}");

                // 初始化 MVS API 包装器
                if (_mvsApi.Initialize(_cameraType))
                {
                    LogMessage("✓ MVS API 包装器初始化成功");
                }

                // 获取 API 方法
                InitializeAPIMethods();

                // 验证关键方法是否找到
                LogMessage($"方法验证: CreateDevice = {(_createDeviceMethod != null ? "✓" : "✗")}");
                LogMessage($"方法验证: OpenDevice = {(_openDeviceMethod != null ? "✓" : "✗")}");
                LogMessage($"方法验证: CloseDevice = {(_closeDeviceMethod != null ? "✓" : "✗")}");

                LogMessage("✓ MVS API 初始化成功");
                OnConnectionStatusChanged(ConnectionStatus.Disconnected, "MVS API 初始化成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DEBUG: MVS API 初始化异常: {ex}");
                LogMessage($"MVS API 初始化失败: {ex.Message}");
                OnErrorOccurred(-1, "MVS API 初始化失败，将使用模拟模式", ex);
                OnConnectionStatusChanged(ConnectionStatus.Disconnected, "使用模拟模式");
            }
        }

        /// <summary>
        /// 尝试强制加载 MVS 程序集
        /// </summary>
        private System.Reflection.Assembly TryLoadMVSAssembly()
        {
            try
            {
                // 尝试加载常见的 MVS 程序集名称
                string[] possibleNames = {
                    "MvCameraControl.Net",
                    "MvCameraControl.NET",
                    "MvCameraControl",
                    "MVS.NET",
                    "Hikvision.MVS"
                };

                foreach (var name in possibleNames)
                {
                    try
                    {
                        var assembly = System.Reflection.Assembly.Load(name);
                        LogMessage($"✓ 成功加载程序集: {name}");
                        return assembly;
                    }
                    catch
                    {
                        // 继续尝试下一个名称
                    }
                }

                // 尝试从文件路径加载
                string[] possiblePaths = {
                    @"D:\MVS\Development\DotNet\win64\MvCameraControl.Net.dll",
                    @"C:\Program Files\MVS\Development\DotNet\win64\MvCameraControl.Net.dll",
                    @".\MvCameraControl.Net.dll",
                    @".\Mvs\MvCameraControl.Net.dll"
                };

                foreach (var path in possiblePaths)
                {
                    try
                    {
                        if (System.IO.File.Exists(path))
                        {
                            var assembly = System.Reflection.Assembly.LoadFrom(path);
                            LogMessage($"✓ 从路径加载程序集: {path}");
                            return assembly;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"从路径 {path} 加载失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"强制加载 MVS 程序集失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 尝试从引用的程序集加载
        /// </summary>
        private System.Reflection.Assembly TryLoadFromReferencedAssemblies()
        {
            try
            {
                // 获取当前程序集的引用
                var currentAssembly = System.Reflection.Assembly.GetExecutingAssembly();
                var referencedAssemblies = currentAssembly.GetReferencedAssemblies();

                foreach (var refAssembly in referencedAssemblies)
                {
                    if (refAssembly.Name.Contains("MvCameraControl"))
                    {
                        try
                        {
                            var assembly = System.Reflection.Assembly.Load(refAssembly);
                            LogMessage($"✓ 从引用加载程序集: {refAssembly.Name}");
                            return assembly;
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"加载引用程序集 {refAssembly.Name} 失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"从引用程序集加载失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 查找相机类型
        /// </summary>
        private Type FindCameraType(Type[] types)
        {
            try
            {
                // 优先查找标准名称
                var standardNames = new[] { "MyCamera", "MvCamera", "Camera", "CCameraNode" };

                foreach (var name in standardNames)
                {
                    var type = types.FirstOrDefault(t => t.IsClass && !t.IsAbstract &&
                        t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
                    if (type != null)
                    {
                        LogMessage($"找到标准相机类型: {type.Name}");
                        return type;
                    }
                }

                // 查找包含 Camera 的类
                var cameraTypes = types.Where(t => t.IsClass && !t.IsAbstract &&
                    t.Name.ToLower().Contains("camera")).ToList();

                if (cameraTypes.Count > 0)
                {
                    LogMessage($"找到 {cameraTypes.Count} 个包含 Camera 的类型:");
                    foreach (var type in cameraTypes)
                    {
                        LogMessage($"  - {type.Name}");
                    }
                    return cameraTypes.First();
                }

                // 查找可能的设备类
                var deviceTypes = types.Where(t => t.IsClass && !t.IsAbstract &&
                    (t.Name.ToLower().Contains("device") || t.Name.ToLower().Contains("mv"))).ToList();

                if (deviceTypes.Count > 0)
                {
                    LogMessage($"找到 {deviceTypes.Count} 个可能的设备类型:");
                    foreach (var type in deviceTypes)
                    {
                        LogMessage($"  - {type.Name}");
                    }
                    return deviceTypes.First();
                }

                LogMessage("未找到合适的相机类型");
                return null;
            }
            catch (Exception ex)
            {
                LogMessage($"查找相机类型失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 初始化 API 方法反射
        /// </summary>
        private void InitializeAPIMethods()
        {
            if (_cameraType == null)
            {
                LogMessage("相机类型为空，跳过 API 方法初始化");
                return;
            }

            try
            {
                var bindingFlags = System.Reflection.BindingFlags.Public |
                                  System.Reflection.BindingFlags.Instance |
                                  System.Reflection.BindingFlags.Static;

                // 设备枚举方法（通常是静态方法）
                _enumDevicesMethod = _cameraType.GetMethods(bindingFlags)
                    .FirstOrDefault(m => m.Name.ToLower().Contains("enum") && m.Name.ToLower().Contains("device"));

                // 实例方法
                _createDeviceMethod = _cameraType.GetMethod("MV_CC_CreateDevice_NET", bindingFlags) ??
                                     _cameraType.GetMethod("MV_CC_CreateDevice", bindingFlags) ??
                                     _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("create"));

                // 如果仍然找不到，列出所有可能的方法
                if (_createDeviceMethod == null)
                {
                    LogMessage("未找到 CreateDevice 方法，列出所有可用方法:");
                    var allMethods = _cameraType.GetMethods(bindingFlags);
                    foreach (var method in allMethods.Where(m => m.Name.ToLower().Contains("create") || m.Name.ToLower().Contains("device")))
                    {
                        LogMessage($"  方法: {method.Name}, 参数: [{string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name))}]");
                    }
                }

                _openDeviceMethod = _cameraType.GetMethod("MV_CC_OpenDevice_NET", bindingFlags) ??
                                   _cameraType.GetMethod("MV_CC_OpenDevice", bindingFlags) ??
                                   _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("open"));

                // 如果仍然找不到，列出所有可能的方法
                if (_openDeviceMethod == null)
                {
                    LogMessage("未找到 OpenDevice 方法，列出所有可用方法:");
                    var allMethods = _cameraType.GetMethods(bindingFlags);
                    foreach (var method in allMethods.Where(m => m.Name.ToLower().Contains("open") || m.Name.ToLower().Contains("device")))
                    {
                        LogMessage($"  方法: {method.Name}, 参数: [{string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name))}]");
                    }
                }

                _closeDeviceMethod = _cameraType.GetMethod("MV_CC_CloseDevice_NET", bindingFlags) ??
                                    _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("close"));

                _destroyDeviceMethod = _cameraType.GetMethod("MV_CC_DestroyDevice_NET", bindingFlags) ??
                                      _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("destroy"));

                _startGrabbingMethod = _cameraType.GetMethod("MV_CC_StartGrabbing_NET", bindingFlags) ??
                                      _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("start") && m.Name.ToLower().Contains("grab"));

                _stopGrabbingMethod = _cameraType.GetMethod("MV_CC_StopGrabbing_NET", bindingFlags) ??
                                     _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("stop") && m.Name.ToLower().Contains("grab"));

                _getOneFrameMethod = _cameraType.GetMethod("MV_CC_GetOneFrameTimeout_NET", bindingFlags) ??
                                    _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("frame"));

                // 参数设置方法
                _setFloatValueMethod = _cameraType.GetMethod("MV_CC_SetFloatValue_NET", bindingFlags);
                _getFloatValueMethod = _cameraType.GetMethod("MV_CC_GetFloatValue_NET", bindingFlags);
                _setIntValueMethod = _cameraType.GetMethod("MV_CC_SetIntValue_NET", bindingFlags);
                _getIntValueMethod = _cameraType.GetMethod("MV_CC_GetIntValue_NET", bindingFlags);
                _setBoolValueMethod = _cameraType.GetMethod("MV_CC_SetBoolValue_NET", bindingFlags);
                _getBoolValueMethod = _cameraType.GetMethod("MV_CC_GetBoolValue_NET", bindingFlags);
                _setEnumValueMethod = _cameraType.GetMethod("MV_CC_SetEnumValue_NET", bindingFlags);
                _getEnumValueMethod = _cameraType.GetMethod("MV_CC_GetEnumValue_NET", bindingFlags);

                _triggerSoftwareMethod = _cameraType.GetMethod("MV_CC_TriggerSoftware_NET", bindingFlags) ??
                                        _cameraType.GetMethods(bindingFlags).FirstOrDefault(m => m.Name.ToLower().Contains("trigger"));

                LogMessage("API 方法初始化完成");
            }
            catch (Exception ex)
            {
                LogMessage($"API 方法初始化失败: {ex.Message}");
            }
        }

        #endregion

        #region 设备管理

        public List<CameraDeviceInfo> EnumerateDevices()
        {
            return EnumerateDevices(null);
        }

        public List<CameraDeviceInfo> EnumerateDevices(string deviceType)
        {
            var devices = new List<CameraDeviceInfo>();

            try
            {
                LogMessage("开始枚举设备...");

                // 创建模拟设备用于测试
                devices.AddRange(CreateMockDevices());

                // 尝试使用真实的 MVS API
                if (_enumDevicesMethod != null)
                {
                    try
                    {
                        var realDevices = EnumerateRealDevices(deviceType);
                        if (realDevices.Count > 0)
                        {
                            devices.Clear(); // 如果找到真实设备，清除模拟设备
                            devices.AddRange(realDevices);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"真实设备枚举失败，使用模拟设备: {ex.Message}");
                    }
                }
                else
                {
                    LogMessage("MVS API 方法未找到，使用模拟设备进行测试");
                }

                LogMessage($"找到 {devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "枚举设备失败", ex);
            }

            return devices;
        }

        public List<CameraDeviceInfo> EnumerateGigEDevices(string ipRange = null)
        {
            var devices = new List<CameraDeviceInfo>();

            try
            {
                LogMessage($"枚举 GigE 设备 (IP 范围: {ipRange ?? "全部"})...");

                // 创建 GigE 模拟设备
                devices.AddRange(CreateMockGigEDevices(ipRange));

                // 尝试使用真实的 MVS API 枚举 GigE 设备
                if (_enumDevicesMethod != null)
                {
                    try
                    {
                        var realDevices = EnumerateRealGigEDevices(ipRange);
                        if (realDevices.Count > 0)
                        {
                            devices.Clear();
                            devices.AddRange(realDevices);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"真实 GigE 设备枚举失败: {ex.Message}");
                    }
                }

                LogMessage($"找到 {devices.Count} 个 GigE 设备");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "枚举 GigE 设备失败", ex);
            }

            return devices;
        }

        private List<CameraDeviceInfo> CreateMockDevices()
        {
            var devices = new List<CameraDeviceInfo>();

            // 模拟 GigE 相机
            devices.Add(new CameraDeviceInfo
            {
                DeviceIndex = 0,
                DeviceName = "模拟 GigE 相机 1",
                ModelName = "MV-CA050-10GC",
                SerialNumber = "00000001",
                ManufacturerName = "Hikvision",
                DeviceVersion = "1.0.0",
                IpAddress = "*************",
                Port = 3956,
                MacAddress = "00:11:22:33:44:55",
                ConnectionType = "GigE",
                DeviceType = DeviceType.GigE,
                IsAccessible = true,
                IsOnline = true,
                UserDefinedName = "生产线相机1",
                FirmwareVersion = "2.1.0"
            });

            // 模拟 USB3 相机
            devices.Add(new CameraDeviceInfo
            {
                DeviceIndex = 1,
                DeviceName = "模拟 USB3 相机 1",
                ModelName = "MV-CA050-10UC",
                SerialNumber = "00000002",
                ManufacturerName = "Hikvision",
                DeviceVersion = "1.0.0",
                ConnectionType = "USB3",
                DeviceType = DeviceType.USB3,
                IsAccessible = true,
                UserDefinedName = "检测相机1",
                FirmwareVersion = "2.0.5"
            });

            return devices;
        }

        private List<CameraDeviceInfo> CreateMockGigEDevices(string ipRange)
        {
            var devices = new List<CameraDeviceInfo>();

            // 根据 IP 范围创建模拟设备
            if (string.IsNullOrEmpty(ipRange) || ipRange.Contains("192.168.1"))
            {
                devices.Add(new CameraDeviceInfo
                {
                    DeviceIndex = 0,
                    DeviceName = "GigE 相机 *************",
                    ModelName = "MV-CA050-10GC",
                    SerialNumber = "GE000001",
                    ManufacturerName = "Hikvision",
                    IpAddress = "*************",
                    Port = 3956,
                    MacAddress = "00:11:22:33:44:55",
                    DeviceType = DeviceType.GigE,
                    IsAccessible = true,
                    IsOnline = true
                });

                devices.Add(new CameraDeviceInfo
                {
                    DeviceIndex = 1,
                    DeviceName = "GigE 相机 *************",
                    ModelName = "MV-CA050-20GC",
                    SerialNumber = "GE000002",
                    ManufacturerName = "Hikvision",
                    IpAddress = "*************",
                    Port = 3956,
                    MacAddress = "00:11:22:33:44:56",
                    DeviceType = DeviceType.GigE,
                    IsAccessible = true,
                    IsOnline = false
                });
            }

            return devices;
        }

        private List<CameraDeviceInfo> EnumerateRealDevices(string deviceType)
        {
            var devices = new List<CameraDeviceInfo>();

            try
            {
                LogMessage("尝试调用真实的 MVS API 枚举设备...");

                if (_mvsApi != null && _mvsApi.IsInitialized)
                {
                    // 使用 MVS API 包装器枚举设备
                    uint deviceTypeFlag = MV_GIGE_DEVICE | MV_USB_DEVICE;

                    if (!string.IsNullOrEmpty(deviceType))
                    {
                        if (deviceType.ToLower().Contains("gige"))
                            deviceTypeFlag = MV_GIGE_DEVICE;
                        else if (deviceType.ToLower().Contains("usb"))
                            deviceTypeFlag = MV_USB_DEVICE;
                    }

                    devices = _mvsApi.EnumerateDevices(deviceTypeFlag);
                    LogMessage($"真实 API 找到 {devices.Count} 个设备");
                }
                else
                {
                    LogMessage("MVS API 包装器未初始化");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"真实设备枚举失败: {ex.Message}");
            }

            return devices;
        }

        private List<CameraDeviceInfo> EnumerateRealGigEDevices(string ipRange)
        {
            var devices = new List<CameraDeviceInfo>();

            // 这里实现真实的 MVS GigE 设备枚举
            LogMessage("尝试调用真实的 MVS API 枚举 GigE 设备...");

            return devices;
        }

        private void LogMessage(string message)
        {
            // 在初始化阶段使用 Debug 输出，避免 UI 空引用异常
            System.Diagnostics.Debug.WriteLine($"[MVS] {message}");
            Console.WriteLine($"[MVS] {message}");

            // 只有在 UI 可能已经初始化后才触发事件
            try
            {
                OnErrorOccurred(0, message); // 使用错误码 0 表示信息消息
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MVS] LogMessage 事件触发失败: {ex.Message}");
            }
        }

        #endregion

        #region 连接方法

        public bool Connect(CameraDeviceInfo deviceInfo)
        {
            if (deviceInfo == null)
            {
                OnErrorOccurred(-1, "设备信息不能为空");
                return false;
            }

            // 根据设备类型选择连接方式
            if (deviceInfo.IsNetworkCamera)
            {
                return ConnectByIP(deviceInfo.IpAddress, deviceInfo.Port);
            }
            else
            {
                return ConnectBySerialNumber(deviceInfo.SerialNumber);
            }
        }

        public bool Connect(int deviceIndex)
        {
            lock (_lockObject)
            {
                try
                {
                    if (_isConnected)
                    {
                        Disconnect();
                    }

                    OnConnectionStatusChanged(ConnectionStatus.Connecting, $"正在连接设备索引 {deviceIndex}...");

                    // 模拟连接过程
                    System.Threading.Thread.Sleep(1000);

                    // 创建相机实例
                    if (_camera == null && _cameraType != null)
                    {
                        _camera = Activator.CreateInstance(_cameraType);
                    }

                    // 这里需要根据实际的 MVS API 来实现连接逻辑
                    _isConnected = true;

                    // 创建模拟设备信息
                    _currentDeviceInfo = new CameraDeviceInfo
                    {
                        DeviceIndex = deviceIndex,
                        DeviceName = $"相机设备 {deviceIndex}",
                        ModelName = "MV-CA050-10GC",
                        SerialNumber = $"SN{deviceIndex:D8}",
                        ManufacturerName = "Hikvision",
                        ConnectionType = "模拟连接",
                        IsAccessible = true
                    };

                    OnConnectionStatusChanged(ConnectionStatus.Connected, "相机连接成功");

                    // 初始化参数
                    InitializeParameters();

                    return true;
                }
                catch (Exception ex)
                {
                    OnConnectionStatusChanged(ConnectionStatus.Failed, "连接失败");
                    OnErrorOccurred(-1, "连接相机失败", ex);
                    return false;
                }
            }
        }

        public bool ConnectByIP(string ipAddress, int port = 3956)
        {
            lock (_lockObject)
            {
                try
                {
                    if (string.IsNullOrEmpty(ipAddress))
                    {
                        OnErrorOccurred(-1, "IP 地址不能为空");
                        return false;
                    }

                    if (_isConnected)
                    {
                        Disconnect();
                    }

                    OnConnectionStatusChanged(ConnectionStatus.Connecting, $"正在连接到 {ipAddress}:{port}...");
                    LogMessage($"开始连接到相机 {ipAddress}:{port}");

                    // 首先检查相机是否可达
                    LogMessage("检查网络连通性...");
                    if (!PingCamera(ipAddress, 3000))
                    {
                        OnConnectionStatusChanged(ConnectionStatus.Failed, "相机不可达");
                        OnErrorOccurred(-1, $"无法 Ping 通 {ipAddress}，请检查网络连接和相机状态");
                        return false;
                    }
                    LogMessage("✓ 网络连通性检查通过");

                    // 使用真实的 MVS API 直接通过 IP 连接
                    return ConnectToRealCameraByIP(ipAddress, port);
                }
                catch (Exception ex)
                {
                    OnConnectionStatusChanged(ConnectionStatus.Failed, "IP 连接失败");
                    OnErrorOccurred(-1, $"通过 IP 连接相机失败: {ex.Message}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 通过 IP 地址直接连接到真实的 MVS 相机（不需要枚举）
        /// </summary>
        private bool ConnectToRealCameraByIP(string ipAddress, int port)
        {
            try
            {
                LogMessage($"开始直接通过 IP 连接到相机 {ipAddress}...");

                // 第一步：创建相机实例
                LogMessage("创建相机实例...");
                if (_camera == null && _cameraType != null)
                {
                    _camera = Activator.CreateInstance(_cameraType);
                }

                if (_camera == null)
                {
                    OnErrorOccurred(-1, "无法创建相机实例");
                    return false;
                }
                LogMessage("✓ 相机实例创建成功");

                // 第二步：直接创建 GigE 设备信息结构
                LogMessage("创建 GigE 设备信息结构...");
                var deviceInfoType = _cameraType.GetNestedType("MV_CC_DEVICE_INFO");
                if (deviceInfoType == null)
                {
                    OnErrorOccurred(-1, "未找到 MV_CC_DEVICE_INFO 类型");
                    return false;
                }

                var deviceInfo = Activator.CreateInstance(deviceInfoType);
                LogMessage("✓ 设备信息结构创建成功");

                // 第三步：设置设备类型为 GigE
                var nTLayerTypeField = deviceInfoType.GetField("nTLayerType");
                if (nTLayerTypeField != null)
                {
                    nTLayerTypeField.SetValue(deviceInfo, MV_GIGE_DEVICE);
                    LogMessage("✓ 设备类型设置为 GigE");
                }

                // 第四步：设置 GigE 特定信息
                LogMessage("设置 GigE 设备参数...");

                // 查找正确的 SpecialInfo 字段类型
                var specialInfoField = deviceInfoType.GetField("SpecialInfo");
                if (specialInfoField != null)
                {
                    LogMessage($"SpecialInfo 字段类型: {specialInfoField.FieldType.Name}");

                    // 创建 SpecialInfo 实例
                    var specialInfo = Activator.CreateInstance(specialInfoField.FieldType);

                    // 查找 GigE 相关字段
                    var gigeFields = specialInfoField.FieldType.GetFields();
                    LogMessage($"SpecialInfo 包含 {gigeFields.Length} 个字段");

                    foreach (var field in gigeFields)
                    {
                        LogMessage($"  字段: {field.Name}, 类型: {field.FieldType.Name}");

                        // 如果找到 GigE 相关字段，尝试设置
                        if (field.Name.Contains("Gige") || field.Name.Contains("GIGE") ||
                            field.FieldType.Name.Contains("GIGE") || field.FieldType.Name.Contains("Gige"))
                        {
                            LogMessage($"找到 GigE 字段: {field.Name}");

                            // 创建 GigE 信息实例
                            var gigeInfo = Activator.CreateInstance(field.FieldType);

                            // 设置 IP 地址
                            var ipFields = field.FieldType.GetFields();
                            foreach (var ipField in ipFields)
                            {
                                if (ipField.Name.Contains("Ip") || ipField.Name.Contains("IP"))
                                {
                                    LogMessage($"找到 IP 字段: {ipField.Name}");

                                    var ipParts = ipAddress.Split('.');
                                    if (ipParts.Length == 4)
                                    {
                                        uint ip = (uint.Parse(ipParts[3]) << 24) |
                                                 (uint.Parse(ipParts[2]) << 16) |
                                                 (uint.Parse(ipParts[1]) << 8) |
                                                 uint.Parse(ipParts[0]);

                                        ipField.SetValue(gigeInfo, ip);
                                        LogMessage($"✓ IP 地址设置为: {ipAddress}");
                                    }
                                }
                                else if (ipField.Name.Contains("Port") || ipField.Name.Contains("PORT"))
                                {
                                    LogMessage($"找到端口字段: {ipField.Name}");
                                    ipField.SetValue(gigeInfo, (uint)port);
                                    LogMessage($"✓ 端口设置为: {port}");
                                }
                            }

                            field.SetValue(specialInfo, gigeInfo);
                            break;
                        }
                    }

                    specialInfoField.SetValue(deviceInfo, specialInfo);
                    LogMessage("✓ GigE 设备信息设置完成");
                }
                else
                {
                    LogMessage("警告: 未找到 SpecialInfo 字段");
                }

                // 第五步：创建设备连接
                LogMessage("创建设备连接...");

                if (_createDeviceMethod == null)
                {
                    OnErrorOccurred(-1, "创建设备方法未找到");
                    return false;
                }

                try
                {
                    var createResult = _createDeviceMethod.Invoke(_camera, new object[] { deviceInfo });
                    LogMessage($"创建设备调用结果: {createResult}");

                    if (!IsSuccessResult(createResult))
                    {
                        OnErrorOccurred(-1, $"创建设备失败，错误码: {createResult}");
                        return false;
                    }
                    LogMessage("✓ 设备连接创建成功");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"创建设备异常: {ex.Message}", ex);
                    return false;
                }

                // 第六步：打开设备
                LogMessage("打开设备...");

                if (_openDeviceMethod == null)
                {
                    OnErrorOccurred(-1, "打开设备方法未找到");
                    return false;
                }

                try
                {
                    // 尝试不同的参数组合
                    LogMessage("尝试调用 OpenDevice()...");
                    var openResult = _openDeviceMethod.Invoke(_camera, null);
                    LogMessage($"OpenDevice 调用结果: {openResult}");

                    if (openResult == null)
                    {
                        LogMessage("OpenDevice 返回 null，尝试使用空数组参数...");
                        openResult = _openDeviceMethod.Invoke(_camera, new object[0]);
                        LogMessage($"OpenDevice(空数组) 调用结果: {openResult}");
                    }

                    if (openResult == null)
                    {
                        LogMessage("OpenDevice 仍然返回 null，尝试使用访问模式参数...");
                        // 尝试使用访问模式参数 (通常 GigE 相机需要指定访问模式)
                        openResult = _openDeviceMethod.Invoke(_camera, new object[] { 1u }); // MV_ACCESS_Exclusive
                        LogMessage($"OpenDevice(访问模式) 调用结果: {openResult}");
                    }

                    if (!IsSuccessResult(openResult))
                    {
                        OnErrorOccurred(-1, $"打开设备失败，错误码: {openResult}");
                        return false;
                    }
                    LogMessage("✓ 设备打开成功");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, $"打开设备异常: {ex.Message}", ex);
                    return false;
                }

                // 连接成功
                _isConnected = true;

                // 创建设备信息
                _currentDeviceInfo = new CameraDeviceInfo
                {
                    DeviceName = $"GigE 相机 {ipAddress}",
                    ModelName = "MV-CU050-60GM",
                    SerialNumber = "DirectConnect",
                    ManufacturerName = "Hikvision",
                    IpAddress = ipAddress,
                    Port = port,
                    ConnectionType = "GigE",
                    DeviceType = DeviceType.GigE,
                    IsAccessible = true,
                    IsOnline = true,
                    DeviceVersion = "4.5.1"
                };

                OnConnectionStatusChanged(ConnectionStatus.Connected, $"✓ 成功连接到真实相机 {ipAddress}");
                LogMessage($"🎉 真实相机直连成功: {_currentDeviceInfo.ModelName} ({ipAddress})");

                // 初始化参数
                InitializeParameters();

                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"IP 直连失败: {ex.Message}", ex);
                return false;
            }
        }

        public bool ConnectBySerialNumber(string serialNumber)
        {
            lock (_lockObject)
            {
                try
                {
                    if (string.IsNullOrEmpty(serialNumber))
                    {
                        OnErrorOccurred(-1, "序列号不能为空");
                        return false;
                    }

                    if (_isConnected)
                    {
                        Disconnect();
                    }

                    OnConnectionStatusChanged(ConnectionStatus.Connecting, $"正在连接序列号 {serialNumber}...");

                    // 模拟连接过程
                    System.Threading.Thread.Sleep(1000);

                    // 创建相机实例
                    if (_camera == null && _cameraType != null)
                    {
                        _camera = Activator.CreateInstance(_cameraType);
                    }

                    // 这里需要根据实际的 MVS API 来实现序列号连接逻辑
                    _isConnected = true;

                    // 创建设备信息
                    _currentDeviceInfo = new CameraDeviceInfo
                    {
                        DeviceName = $"相机 {serialNumber}",
                        ModelName = "MV-CA050-10UC",
                        SerialNumber = serialNumber,
                        ManufacturerName = "Hikvision",
                        ConnectionType = "USB3",
                        DeviceType = DeviceType.USB3,
                        IsAccessible = true
                    };

                    OnConnectionStatusChanged(ConnectionStatus.Connected, $"已连接到序列号 {serialNumber}");

                    // 初始化参数
                    InitializeParameters();

                    return true;
                }
                catch (Exception ex)
                {
                    OnConnectionStatusChanged(ConnectionStatus.Failed, "序列号连接失败");
                    OnErrorOccurred(-1, $"通过序列号连接相机失败: {ex.Message}", ex);
                    return false;
                }
            }
        }

        public bool ConnectByMacAddress(string macAddress)
        {
            lock (_lockObject)
            {
                try
                {
                    if (string.IsNullOrEmpty(macAddress))
                    {
                        OnErrorOccurred(-1, "MAC 地址不能为空");
                        return false;
                    }

                    if (_isConnected)
                    {
                        Disconnect();
                    }

                    OnConnectionStatusChanged(ConnectionStatus.Connecting, $"正在连接 MAC 地址 {macAddress}...");

                    // 模拟连接过程
                    System.Threading.Thread.Sleep(1200);

                    // 创建相机实例
                    if (_camera == null && _cameraType != null)
                    {
                        _camera = Activator.CreateInstance(_cameraType);
                    }

                    // 这里需要根据实际的 MVS API 来实现 MAC 地址连接逻辑
                    _isConnected = true;

                    // 创建设备信息
                    _currentDeviceInfo = new CameraDeviceInfo
                    {
                        DeviceName = $"GigE 相机 {macAddress}",
                        ModelName = "MV-CA050-10GC",
                        SerialNumber = "MAC" + macAddress.Replace(":", ""),
                        ManufacturerName = "Hikvision",
                        MacAddress = macAddress,
                        ConnectionType = "GigE",
                        DeviceType = DeviceType.GigE,
                        IsAccessible = true
                    };

                    OnConnectionStatusChanged(ConnectionStatus.Connected, $"已连接到 MAC {macAddress}");

                    // 初始化参数
                    InitializeParameters();

                    return true;
                }
                catch (Exception ex)
                {
                    OnConnectionStatusChanged(ConnectionStatus.Failed, "MAC 地址连接失败");
                    OnErrorOccurred(-1, $"通过 MAC 地址连接相机失败: {ex.Message}", ex);
                    return false;
                }
            }
        }

        public bool PingCamera(string ipAddress, int timeout = 3000)
        {
            try
            {
                using (var ping = new System.Net.NetworkInformation.Ping())
                {
                    var reply = ping.Send(ipAddress, timeout);
                    return reply.Status == System.Net.NetworkInformation.IPStatus.Success;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Ping {ipAddress} 失败: {ex.Message}");
                return false;
            }
        }

        public bool ForceIP(string macAddress, string newIpAddress, string subnetMask, string gateway)
        {
            try
            {
                if (string.IsNullOrEmpty(macAddress) || string.IsNullOrEmpty(newIpAddress))
                {
                    OnErrorOccurred(-1, "MAC 地址和 IP 地址不能为空");
                    return false;
                }

                LogMessage($"正在配置 IP: MAC={macAddress}, IP={newIpAddress}, Mask={subnetMask}, Gateway={gateway}");

                // 这里需要根据实际的 MVS API 来实现强制 IP 配置
                // 通常使用 MV_GIGE_ForceIpEx_NET 或类似的方法

                // 模拟配置过程
                System.Threading.Thread.Sleep(2000);

                LogMessage("IP 配置完成");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"强制 IP 配置失败: {ex.Message}", ex);
                return false;
            }
        }

        public void Disconnect()
        {
            lock (_lockObject)
            {
                try
                {
                    if (_isGrabbing)
                    {
                        StopGrabbing();
                    }

                    if (_camera != null && _closeDeviceMethod != null)
                    {
                        _closeDeviceMethod.Invoke(_camera, null);
                    }

                    if (_camera != null && _destroyDeviceMethod != null)
                    {
                        _destroyDeviceMethod.Invoke(_camera, null);
                    }

                    _camera = null;
                    _isConnected = false;
                    _currentDeviceInfo = null;
                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, "相机已断开连接");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, "断开连接失败", ex);
                }
            }
        }

        #endregion

        #region 图像采集

        public bool StartGrabbing()
        {
            lock (_lockObject)
            {
                try
                {
                    if (!_isConnected)
                    {
                        OnErrorOccurred(-1, "相机未连接");
                        return false;
                    }

                    if (_camera == null)
                    {
                        OnErrorOccurred(-1, "相机实例为空");
                        return false;
                    }

                    if (_isGrabbing)
                    {
                        LogMessage("相机已在采集中");
                        return true;
                    }

                    LogMessage("开始连续采集...");

                    // 使用真实的 MVS API 开始采集
                    if (_startGrabbingMethod == null)
                    {
                        OnErrorOccurred(-1, "开始采集方法未找到，请检查 MVS SDK");
                        return false;
                    }

                    var result = _startGrabbingMethod.Invoke(_camera, null);
                    LogMessage($"MVS StartGrabbing 调用结果: {result}");

                    if (IsSuccessResult(result))
                    {
                        _isGrabbing = true;
                        LogMessage("✓ 开始连续采集成功");
                        return true;
                    }
                    else
                    {
                        OnErrorOccurred(-1, $"开始采集失败，MVS 错误码: {result}");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, "开始采集异常", ex);
                    return false;
                }
            }
        }

        public bool StopGrabbing()
        {
            lock (_lockObject)
            {
                try
                {
                    if (!_isConnected || _camera == null)
                    {
                        return true;
                    }

                    if (!_isGrabbing)
                    {
                        return true;
                    }

                    if (_stopGrabbingMethod != null)
                    {
                        var result = _stopGrabbingMethod.Invoke(_camera, null);
                        if (IsSuccessResult(result))
                        {
                            _isGrabbing = false;
                            return true;
                        }
                    }

                    OnErrorOccurred(-1, "停止采集失败");
                    return false;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred(-1, "停止采集异常", ex);
                    return false;
                }
            }
        }

        public Bitmap CaptureImage()
        {
            try
            {
                if (!_isConnected)
                {
                    OnErrorOccurred(-1, "相机未连接");
                    return null;
                }

                if (_camera == null)
                {
                    OnErrorOccurred(-1, "相机实例为空");
                    return null;
                }

                LogMessage("开始单次拍照...");

                // 使用真实的 MVS API 获取图像
                return CaptureRealImage();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "拍照异常", ex);
                return null;
            }
        }

        /// <summary>
        /// 从真实相机获取图像
        /// </summary>
        private Bitmap CaptureRealImage()
        {
            try
            {
                if (_getOneFrameMethod == null)
                {
                    OnErrorOccurred(-1, "获取图像方法未找到，请检查 MVS SDK");
                    return null;
                }

                LogMessage("分配图像缓冲区...");

                // 分配足够大的图像缓冲区
                uint bufferSize = (uint)(_parameters.Width * _parameters.Height * 3); // RGB 格式
                if (bufferSize < 1920 * 1080 * 3) // 最小缓冲区大小
                {
                    bufferSize = 1920 * 1080 * 3;
                }

                IntPtr pData = Marshal.AllocHGlobal((int)bufferSize);

                try
                {
                    LogMessage($"调用 MVS API 获取图像，缓冲区大小: {bufferSize} 字节");

                    // 创建图像信息结构
                    var frameInfo = new MV_FRAME_OUT_INFO_EX();

                    // 调用获取图像方法 (MV_CC_GetOneFrameTimeout_NET)
                    var result = _getOneFrameMethod.Invoke(_camera,
                        new object[] { pData, bufferSize, frameInfo, 3000u }); // 3秒超时

                    LogMessage($"MVS API 调用结果: {result}");

                    if (IsSuccessResult(result))
                    {
                        LogMessage($"✓ 成功获取图像数据: {frameInfo.nWidth}x{frameInfo.nHeight}, 像素格式: 0x{frameInfo.nPixelType:X8}");

                        var bitmap = ConvertToBitmap(pData, frameInfo);
                        if (bitmap != null)
                        {
                            _frameNumber++;
                            LogMessage($"✓ 图像转换成功，帧号: {_frameNumber}");
                            OnImageReceived(bitmap, _frameNumber);
                            return bitmap;
                        }
                        else
                        {
                            OnErrorOccurred(-1, "图像数据转换失败");
                        }
                    }
                    else
                    {
                        OnErrorOccurred(-1, $"获取图像失败，MVS 错误码: {result}");
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(pData);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取真实图像失败: {ex.Message}", ex);
            }

            return null;
        }

        public async Task<Bitmap> CaptureImageAsync()
        {
            return await Task.Run(() => CaptureImage());
        }

        /// <summary>
        /// 使用反射方式获取图像
        /// </summary>
        private Bitmap CaptureImageUsingReflection()
        {
            try
            {
                // 分配图像缓冲区
                uint bufferSize = 1920 * 1080 * 3; // 假设最大分辨率
                IntPtr pData = Marshal.AllocHGlobal((int)bufferSize);

                try
                {
                    // 创建图像信息结构
                    var frameInfo = new MV_FRAME_OUT_INFO_EX();

                    // 调用获取图像方法
                    var result = _getOneFrameMethod.Invoke(_camera,
                        new object[] { pData, bufferSize, frameInfo, 1000u });

                    if (IsSuccessResult(result))
                    {
                        return ConvertToBitmap(pData, frameInfo);
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(pData);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"反射方式获取图像失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 创建模拟图像
        /// </summary>
        private Bitmap CreateMockImage()
        {
            try
            {
                int width = _parameters.Width;
                int height = _parameters.Height;

                var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // 根据连接状态创建不同的图像
                    if (_isConnected && _currentDeviceInfo != null)
                    {
                        // 已连接状态：创建更真实的相机图像
                        CreateRealisticCameraImage(graphics, width, height);
                    }
                    else
                    {
                        // 未连接状态：创建明显的模拟图像
                        CreateSimulationImage(graphics, width, height);
                    }
                }

                return bitmap;
            }
            catch (Exception ex)
            {
                LogMessage($"创建模拟图像失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建真实的相机图像模拟
        /// </summary>
        private void CreateRealisticCameraImage(Graphics graphics, int width, int height)
        {
            // 创建更真实的背景（类似工业相机拍摄的场景）
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                new Rectangle(0, 0, width, height),
                Color.FromArgb(240, 240, 240), Color.FromArgb(200, 200, 200), 45f))
            {
                graphics.FillRectangle(brush, 0, 0, width, height);
            }

            // 添加一些几何图形模拟工件
            var random = new Random(_frameNumber.GetHashCode());

            // 绘制圆形目标
            using (var pen = new Pen(Color.Black, 3))
            {
                int centerX = width / 2 + random.Next(-50, 50);
                int centerY = height / 2 + random.Next(-50, 50);
                int radius = 80 + random.Next(-20, 20);

                graphics.DrawEllipse(pen, centerX - radius, centerY - radius, radius * 2, radius * 2);
                graphics.DrawEllipse(pen, centerX - radius/2, centerY - radius/2, radius, radius);

                // 中心十字线
                graphics.DrawLine(pen, centerX - 20, centerY, centerX + 20, centerY);
                graphics.DrawLine(pen, centerX, centerY - 20, centerX, centerY + 20);
            }

            // 添加一些矩形
            using (var pen = new Pen(Color.DarkBlue, 2))
            {
                for (int i = 0; i < 3; i++)
                {
                    int x = random.Next(50, width - 150);
                    int y = random.Next(50, height - 100);
                    graphics.DrawRectangle(pen, x, y, 100, 60);
                }
            }

            // 添加设备信息（小字体）
            using (var font = new Font("Arial", 10, FontStyle.Regular))
            using (var textBrush = new SolidBrush(Color.DarkGray))
            {
                var deviceInfo = $"{_currentDeviceInfo.ModelName} | {_currentDeviceInfo.IpAddress ?? _currentDeviceInfo.SerialNumber}";
                graphics.DrawString(deviceInfo, font, textBrush, 10, 10);

                var frameInfo = $"Frame #{_frameNumber + 1} | {DateTime.Now:HH:mm:ss.fff}";
                graphics.DrawString(frameInfo, font, textBrush, 10, height - 25);
            }

            // 添加微小的噪点模拟真实相机
            for (int i = 0; i < 100; i++)
            {
                int x = random.Next(width);
                int y = random.Next(height);
                var color = Color.FromArgb(random.Next(180, 220), random.Next(180, 220), random.Next(180, 220));
                using (var brush = new SolidBrush(color))
                {
                    graphics.FillRectangle(brush, x, y, 1, 1);
                }
            }
        }

        /// <summary>
        /// 创建明显的模拟图像
        /// </summary>
        private void CreateSimulationImage(Graphics graphics, int width, int height)
        {
            // 创建渐变背景
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                new Rectangle(0, 0, width, height),
                Color.Blue, Color.Green, 45f))
            {
                graphics.FillRectangle(brush, 0, 0, width, height);
            }

            // 添加文本信息
            using (var font = new Font("Arial", 24, FontStyle.Bold))
            using (var textBrush = new SolidBrush(Color.White))
            {
                var text = $"模拟图像 #{_frameNumber + 1}\n{DateTime.Now:HH:mm:ss.fff}";
                var textSize = graphics.MeasureString(text, font);
                var x = (width - textSize.Width) / 2;
                var y = (height - textSize.Height) / 2;
                graphics.DrawString(text, font, textBrush, x, y);
            }

            // 添加十字线
            using (var pen = new Pen(Color.Red, 2))
            {
                graphics.DrawLine(pen, width / 2 - 50, height / 2, width / 2 + 50, height / 2);
                graphics.DrawLine(pen, width / 2, height / 2 - 50, width / 2, height / 2 + 50);
            }
        }

        /// <summary>
        /// 转换为 Bitmap
        /// </summary>
        private Bitmap ConvertToBitmap(IntPtr pData, MV_FRAME_OUT_INFO_EX frameInfo)
        {
            try
            {
                int width = frameInfo.nWidth;
                int height = frameInfo.nHeight;

                // 根据像素格式转换
                System.Drawing.Imaging.PixelFormat pixelFormat = System.Drawing.Imaging.PixelFormat.Format24bppRgb;

                if (frameInfo.nPixelType == (uint)MvGvspPixelType.PixelType_Gvsp_Mono8)
                {
                    pixelFormat = System.Drawing.Imaging.PixelFormat.Format8bppIndexed;
                }
                else if (frameInfo.nPixelType == (uint)MvGvspPixelType.PixelType_Gvsp_RGB8_Packed)
                {
                    pixelFormat = System.Drawing.Imaging.PixelFormat.Format24bppRgb;
                }

                var bitmap = new Bitmap(width, height, pixelFormat);
                var bmpData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                    ImageLockMode.WriteOnly, pixelFormat);

                // 复制图像数据
                int stride = bmpData.Stride;

                unsafe
                {
                    byte* src = (byte*)pData.ToPointer();
                    byte* dst = (byte*)bmpData.Scan0.ToPointer();

                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width * 3; x++)
                        {
                            dst[y * stride + x] = src[y * width * 3 + x];
                        }
                    }
                }

                bitmap.UnlockBits(bmpData);
                return bitmap;
            }
            catch (Exception ex)
            {
                LogMessage($"转换图像失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 参数控制

        public bool SetExposureTime(float exposureTime)
        {
            try
            {
                if (!_isConnected || _camera == null || _setFloatValueMethod == null)
                {
                    return false;
                }

                var result = _setFloatValueMethod.Invoke(_camera, new object[] { "ExposureTime", exposureTime });
                if (IsSuccessResult(result))
                {
                    _parameters.ExposureTime = exposureTime;
                    return true;
                }

                OnErrorOccurred(-1, "设置曝光时间失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置曝光时间异常", ex);
                return false;
            }
        }

        public float GetExposureTime()
        {
            try
            {
                if (!_isConnected || _camera == null || _getFloatValueMethod == null)
                {
                    return _parameters.ExposureTime;
                }

                // 这里需要根据实际的 MVS API 返回类型来实现
                // 通常需要传入一个结构体来接收返回值
                OnErrorOccurred(-1, "获取曝光时间功能需要根据实际 MVS API 实现");
                return _parameters.ExposureTime;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取曝光时间异常", ex);
                return _parameters.ExposureTime;
            }
        }

        public bool SetGain(float gain)
        {
            try
            {
                if (!_isConnected || _camera == null || _setFloatValueMethod == null)
                {
                    return false;
                }

                var result = _setFloatValueMethod.Invoke(_camera, new object[] { "Gain", gain });
                if (IsSuccessResult(result))
                {
                    _parameters.Gain = gain;
                    return true;
                }

                OnErrorOccurred(-1, "设置增益失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置增益异常", ex);
                return false;
            }
        }

        public float GetGain()
        {
            try
            {
                if (!_isConnected || _camera == null || _getFloatValueMethod == null)
                {
                    return _parameters.Gain;
                }

                OnErrorOccurred(-1, "获取增益功能需要根据实际 MVS API 实现");
                return _parameters.Gain;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取增益异常", ex);
                return _parameters.Gain;
            }
        }

        public bool SetResolution(int width, int height)
        {
            try
            {
                if (!_isConnected || _camera == null || _setIntValueMethod == null)
                {
                    return false;
                }

                var widthResult = _setIntValueMethod.Invoke(_camera, new object[] { "Width", (uint)width });
                var heightResult = _setIntValueMethod.Invoke(_camera, new object[] { "Height", (uint)height });

                if (IsSuccessResult(widthResult) && IsSuccessResult(heightResult))
                {
                    _parameters.Width = width;
                    _parameters.Height = height;
                    return true;
                }

                OnErrorOccurred(-1, "设置分辨率失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置分辨率异常", ex);
                return false;
            }
        }

        public Size GetResolution()
        {
            try
            {
                if (!_isConnected || _camera == null || _getIntValueMethod == null)
                {
                    return new Size(_parameters.Width, _parameters.Height);
                }

                OnErrorOccurred(-1, "获取分辨率功能需要根据实际 MVS API 实现");
                return new Size(_parameters.Width, _parameters.Height);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取分辨率异常", ex);
                return new Size(_parameters.Width, _parameters.Height);
            }
        }

        public bool SetROI(int x, int y, int width, int height)
        {
            try
            {
                if (!_isConnected || _camera == null || _setIntValueMethod == null)
                {
                    return false;
                }

                var xResult = _setIntValueMethod.Invoke(_camera, new object[] { "OffsetX", (uint)x });
                var yResult = _setIntValueMethod.Invoke(_camera, new object[] { "OffsetY", (uint)y });
                var widthResult = _setIntValueMethod.Invoke(_camera, new object[] { "Width", (uint)width });
                var heightResult = _setIntValueMethod.Invoke(_camera, new object[] { "Height", (uint)height });

                if (IsSuccessResult(xResult) && IsSuccessResult(yResult) &&
                    IsSuccessResult(widthResult) && IsSuccessResult(heightResult))
                {
                    _parameters.ROI = new Rectangle(x, y, width, height);
                    return true;
                }

                OnErrorOccurred(-1, "设置ROI失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置ROI异常", ex);
                return false;
            }
        }

        public Rectangle GetROI()
        {
            try
            {
                if (!_isConnected || _camera == null || _getIntValueMethod == null)
                {
                    return _parameters.ROI;
                }

                OnErrorOccurred(-1, "获取ROI功能需要根据实际 MVS API 实现");
                return _parameters.ROI;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取ROI异常", ex);
                return _parameters.ROI;
            }
        }

        public bool SetFrameRate(float frameRate)
        {
            try
            {
                if (!_isConnected || _camera == null || _setFloatValueMethod == null)
                {
                    return false;
                }

                var result = _setFloatValueMethod.Invoke(_camera, new object[] { "AcquisitionFrameRate", frameRate });
                if (IsSuccessResult(result))
                {
                    _parameters.FrameRate = frameRate;
                    return true;
                }

                OnErrorOccurred(-1, "设置帧率失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置帧率异常", ex);
                return false;
            }
        }

        public float GetFrameRate()
        {
            try
            {
                if (!_isConnected || _camera == null || _getFloatValueMethod == null)
                {
                    return _parameters.FrameRate;
                }

                OnErrorOccurred(-1, "获取帧率功能需要根据实际 MVS API 实现");
                return _parameters.FrameRate;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取帧率异常", ex);
                return _parameters.FrameRate;
            }
        }

        public bool SetTriggerMode(TriggerMode triggerMode)
        {
            try
            {
                if (!_isConnected || _camera == null || _setEnumValueMethod == null)
                {
                    return false;
                }

                uint triggerModeValue = triggerMode switch
                {
                    TriggerMode.Continuous => 0,
                    TriggerMode.Software => 1,
                    TriggerMode.Hardware => 1,
                    _ => 0
                };

                var result = _setEnumValueMethod.Invoke(_camera, new object[] { "TriggerMode", triggerModeValue });
                if (IsSuccessResult(result))
                {
                    if (triggerMode == TriggerMode.Software)
                    {
                        _setEnumValueMethod.Invoke(_camera, new object[] { "TriggerSource", 7u }); // Software
                    }
                    else if (triggerMode == TriggerMode.Hardware)
                    {
                        _setEnumValueMethod.Invoke(_camera, new object[] { "TriggerSource", 0u }); // Line0
                    }

                    _parameters.TriggerMode = triggerMode;
                    return true;
                }

                OnErrorOccurred(-1, "设置触发模式失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置触发模式异常", ex);
                return false;
            }
        }

        public TriggerMode GetTriggerMode()
        {
            try
            {
                if (!_isConnected || _camera == null || _getEnumValueMethod == null)
                {
                    return _parameters.TriggerMode;
                }

                OnErrorOccurred(-1, "获取触发模式功能需要根据实际 MVS API 实现");
                return _parameters.TriggerMode;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取触发模式异常", ex);
                return _parameters.TriggerMode;
            }
        }

        public bool SoftwareTrigger()
        {
            try
            {
                if (!_isConnected || _camera == null || _triggerSoftwareMethod == null)
                {
                    OnErrorOccurred(-1, "相机未连接或软件触发方法未找到");
                    return false;
                }

                var result = _triggerSoftwareMethod.Invoke(_camera, null);
                if (IsSuccessResult(result))
                {
                    return true;
                }

                OnErrorOccurred(-1, "软件触发失败");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "软件触发异常", ex);
                return false;
            }
        }

        #endregion

        #region 高级功能

        public ParameterRange GetParameterRange(string parameterName)
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return null;
                }

                OnErrorOccurred(-1, "获取参数范围功能需要根据实际 MVS API 实现");
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取参数范围异常: {parameterName}", ex);
                return null;
            }
        }

        public bool SetParameter(string parameterName, object value)
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return false;
                }

                // 根据值类型选择合适的设置方法
                if (value is float floatValue && _setFloatValueMethod != null)
                {
                    var result = _setFloatValueMethod.Invoke(_camera, new object[] { parameterName, floatValue });
                    return IsSuccessResult(result);
                }
                else if (value is int intValue && _setIntValueMethod != null)
                {
                    var result = _setIntValueMethod.Invoke(_camera, new object[] { parameterName, (uint)intValue });
                    return IsSuccessResult(result);
                }
                else if (value is bool boolValue && _setBoolValueMethod != null)
                {
                    var result = _setBoolValueMethod.Invoke(_camera, new object[] { parameterName, boolValue });
                    return IsSuccessResult(result);
                }

                OnErrorOccurred(-1, $"不支持的参数类型: {value?.GetType().Name}");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"设置参数异常: {parameterName}", ex);
                return false;
            }
        }

        public object GetParameter(string parameterName)
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return null;
                }

                OnErrorOccurred(-1, "获取参数功能需要根据实际 MVS API 实现");
                return null;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, $"获取参数异常: {parameterName}", ex);
                return null;
            }
        }

        public bool SaveParametersToFile(string filePath)
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return false;
                }

                // 查找保存参数的方法
                var saveMethod = _cameraType.GetMethods()
                    .FirstOrDefault(m => m.Name.ToLower().Contains("save") || m.Name.ToLower().Contains("feature"));

                if (saveMethod != null)
                {
                    var result = saveMethod.Invoke(_camera, new object[] { filePath });
                    return IsSuccessResult(result);
                }

                OnErrorOccurred(-1, "保存参数方法未找到");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "保存参数到文件异常", ex);
                return false;
            }
        }

        public bool LoadParametersFromFile(string filePath)
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return false;
                }

                // 查找加载参数的方法
                var loadMethod = _cameraType.GetMethods()
                    .FirstOrDefault(m => m.Name.ToLower().Contains("load") || m.Name.ToLower().Contains("feature"));

                if (loadMethod != null)
                {
                    var result = loadMethod.Invoke(_camera, new object[] { filePath });
                    if (IsSuccessResult(result))
                    {
                        RefreshParameters();
                        return true;
                    }
                }

                OnErrorOccurred(-1, "加载参数方法未找到");
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "从文件加载参数异常", ex);
                return false;
            }
        }

        public bool ResetParameters()
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return false;
                }

                // 重置为默认参数
                _parameters = new CameraParameters();
                ApplyParameters();
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "重置参数异常", ex);
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查 MVS API 调用结果是否成功
        /// </summary>
        /// <param name="result">API 调用返回值</param>
        /// <returns>是否成功</returns>
        private bool IsSuccessResult(object result)
        {
            if (result == null) return false;

            // MVS API 通常返回 int 类型，0 表示成功
            if (result is int intResult)
            {
                return intResult == 0;
            }

            // 其他类型的结果处理
            return true;
        }

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitializeParameters()
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return;
                }

                // 读取当前参数值
                RefreshParameters();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "初始化参数异常", ex);
            }
        }

        /// <summary>
        /// 刷新参数
        /// </summary>
        private void RefreshParameters()
        {
            try
            {
                if (!_isConnected || _camera == null)
                {
                    return;
                }

                // 这里应该从相机读取当前参数值
                // 由于需要根据实际 API 实现，这里只是占位
                OnErrorOccurred(-1, "刷新参数功能需要根据实际 MVS API 实现");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "刷新参数异常", ex);
            }
        }

        /// <summary>
        /// 应用参数
        /// </summary>
        private void ApplyParameters()
        {
            try
            {
                if (!_isConnected || _camera == null || _parameters == null)
                {
                    return;
                }

                // 应用所有参数
                SetExposureTime(_parameters.ExposureTime);
                SetGain(_parameters.Gain);
                SetResolution(_parameters.Width, _parameters.Height);
                if (_parameters.ROI != Rectangle.Empty)
                {
                    SetROI(_parameters.ROI.X, _parameters.ROI.Y, _parameters.ROI.Width, _parameters.ROI.Height);
                }
                SetFrameRate(_parameters.FrameRate);
                SetTriggerMode(_parameters.TriggerMode);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "应用参数异常", ex);
            }
        }

        /// <summary>
        /// 注册图像回调函数
        /// </summary>
        public bool RegisterImageCallback(Action<Bitmap, long> callback)
        {
            try
            {
                _imageCallback = callback;

                if (_mvsApi != null && _mvsApi.IsInitialized && _mvsApi.Camera != null)
                {
                    // 创建 MVS 回调委托
                    _mvsImageCallback = (IntPtr pData, ref MV_FRAME_OUT_INFO_EX pFrameInfo, IntPtr pUser) =>
                    {
                        try
                        {
                            var bitmap = ConvertToBitmap(pData, pFrameInfo);
                            if (bitmap != null)
                            {
                                _frameNumber++;
                                _imageCallback?.Invoke(bitmap, _frameNumber);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"图像回调处理失败: {ex.Message}");
                        }
                    };

                    // 注册回调到 MVS API
                    // 这里需要根据实际的 MVS API 来实现
                    LogMessage("✓ 图像回调注册成功");
                    return true;
                }

                LogMessage("✓ 图像回调注册成功（模拟模式）");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "注册图像回调失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 取消图像回调函数
        /// </summary>
        public bool UnregisterImageCallback()
        {
            try
            {
                _imageCallback = null;
                _mvsImageCallback = null;
                LogMessage("✓ 图像回调取消成功");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "取消图像回调失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取设备温度
        /// </summary>
        public float GetDeviceTemperature()
        {
            try
            {
                if (!_isConnected)
                {
                    return 0f;
                }

                // 模拟温度值
                var random = new Random();
                return 35f + (float)(random.NextDouble() * 10); // 35-45度
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取设备温度失败", ex);
                return 0f;
            }
        }

        /// <summary>
        /// 获取设备统计信息
        /// </summary>
        public Dictionary<string, object> GetDeviceStatistics()
        {
            var stats = new Dictionary<string, object>();

            try
            {
                stats["FrameCount"] = _frameNumber;
                stats["IsConnected"] = _isConnected;
                stats["IsGrabbing"] = _isGrabbing;
                stats["Temperature"] = GetDeviceTemperature();
                stats["Uptime"] = DateTime.Now.ToString("HH:mm:ss");

                if (_currentDeviceInfo != null)
                {
                    stats["DeviceName"] = _currentDeviceInfo.DeviceName;
                    stats["SerialNumber"] = _currentDeviceInfo.SerialNumber;
                    stats["ModelName"] = _currentDeviceInfo.ModelName;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取设备统计信息失败", ex);
            }

            return stats;
        }

        /// <summary>
        /// 设置图像格式
        /// </summary>
        public bool SetPixelFormat(string pixelFormat)
        {
            try
            {
                if (!_isConnected || _setEnumValueMethod == null)
                {
                    return false;
                }

                // 根据字符串转换为枚举值
                uint pixelFormatValue = pixelFormat.ToLower() switch
                {
                    "mono8" => (uint)MvGvspPixelType.PixelType_Gvsp_Mono8,
                    "rgb8" => (uint)MvGvspPixelType.PixelType_Gvsp_RGB8_Packed,
                    "bgr8" => (uint)MvGvspPixelType.PixelType_Gvsp_BGR8_Packed,
                    _ => (uint)MvGvspPixelType.PixelType_Gvsp_RGB8_Packed
                };

                var result = _setEnumValueMethod.Invoke(_camera, new object[] { "PixelFormat", pixelFormatValue });
                return IsSuccessResult(result);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置图像格式失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取图像格式
        /// </summary>
        public string GetPixelFormat()
        {
            try
            {
                if (!_isConnected)
                {
                    return "RGB8";
                }

                // 这里需要根据实际 API 实现
                return "RGB8"; // 默认返回
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取图像格式失败", ex);
                return "RGB8";
            }
        }

        /// <summary>
        /// 获取支持的像素格式列表
        /// </summary>
        public List<string> GetSupportedPixelFormats()
        {
            return new List<string> { "Mono8", "RGB8", "BGR8", "YUV422" };
        }

        /// <summary>
        /// 设置白平衡
        /// </summary>
        public bool SetWhiteBalance(string mode)
        {
            try
            {
                if (!_isConnected || _setEnumValueMethod == null)
                {
                    return false;
                }

                uint whiteBalanceMode = mode.ToLower() switch
                {
                    "auto" => 0,
                    "manual" => 1,
                    "once" => 2,
                    _ => 0
                };

                var result = _setEnumValueMethod.Invoke(_camera, new object[] { "BalanceWhiteAuto", whiteBalanceMode });
                return IsSuccessResult(result);
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "设置白平衡失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行一次白平衡
        /// </summary>
        public bool ExecuteWhiteBalance()
        {
            try
            {
                if (!_isConnected)
                {
                    return false;
                }

                // 设置为一次白平衡模式
                SetWhiteBalance("once");
                LogMessage("✓ 执行一次白平衡成功");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "执行白平衡失败", ex);
                return false;
            }
        }

        #endregion

        #region 事件处理

        private void OnConnectionStatusChanged(ConnectionStatus status, string message)
        {
            var args = new ConnectionStatusChangedEventArgs
            {
                Status = status,
                Message = message,
                DeviceInfo = _currentDeviceInfo
            };
            ConnectionStatusChanged?.Invoke(this, args);
        }

        private void OnErrorOccurred(int errorCode, string errorMessage, Exception exception = null)
        {
            var args = new ErrorOccurredEventArgs
            {
                ErrorCode = errorCode,
                ErrorMessage = errorMessage,
                Exception = exception,
                Timestamp = DateTime.Now
            };
            ErrorOccurred?.Invoke(this, args);
        }

        private void OnImageReceived(Bitmap image, long frameNumber)
        {
            var args = new ImageReceivedEventArgs
            {
                Image = image,
                ImageNumber = frameNumber,
                Timestamp = DateTime.Now,
                Width = image?.Width ?? 0,
                Height = image?.Height ?? 0,
                PixelFormat = Models.PixelFormat.RGB8
            };
            ImageReceived?.Invoke(this, args);
        }

        #endregion

        #region 扩展功能实现

        /// <summary>
        /// 保存当前图像
        /// </summary>
        public bool SaveImage(string filePath, string format = "PNG")
        {
            try
            {
                var image = CaptureImage();
                if (image != null)
                {
                    var imageFormat = format.ToUpper() switch
                    {
                        "PNG" => ImageFormat.Png,
                        "JPG" or "JPEG" => ImageFormat.Jpeg,
                        "BMP" => ImageFormat.Bmp,
                        "TIFF" => ImageFormat.Tiff,
                        _ => ImageFormat.Png
                    };

                    image.Save(filePath, imageFormat);
                    LogMessage($"✓ 图像保存成功: {filePath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "保存图像失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 开始录制视频
        /// </summary>
        public bool StartRecording(string filePath, string codec = "H264", float frameRate = 30)
        {
            try
            {
                // 这里需要实现视频录制功能
                // 可以使用 FFMpeg 或其他视频编码库
                LogMessage($"开始录制视频: {filePath}, 编码器: {codec}, 帧率: {frameRate}");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "开始录制失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止录制视频
        /// </summary>
        public bool StopRecording()
        {
            try
            {
                LogMessage("停止录制视频");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "停止录制失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取网络统计信息（GigE 相机）
        /// </summary>
        public Dictionary<string, object> GetNetworkStatistics()
        {
            var stats = new Dictionary<string, object>();

            try
            {
                if (_currentDeviceInfo?.DeviceType == DeviceType.GigE)
                {
                    stats["PacketSize"] = 1500;
                    stats["PacketDelay"] = 1000;
                    stats["LostPackets"] = 0;
                    stats["ResendPackets"] = 0;
                    stats["Bandwidth"] = "100 Mbps";
                    stats["IPAddress"] = _currentDeviceInfo.IpAddress;
                    stats["MACAddress"] = _currentDeviceInfo.MacAddress;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取网络统计信息失败", ex);
            }

            return stats;
        }

        /// <summary>
        /// 重启设备
        /// </summary>
        public bool RestartDevice()
        {
            try
            {
                if (!_isConnected)
                {
                    return false;
                }

                LogMessage("正在重启设备...");

                // 断开连接
                Disconnect();

                // 等待一段时间
                System.Threading.Thread.Sleep(2000);

                // 重新连接
                if (_currentDeviceInfo != null)
                {
                    return Connect(_currentDeviceInfo);
                }

                return false;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "重启设备失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取设备日志
        /// </summary>
        public string GetDeviceLog()
        {
            try
            {
                var log = new System.Text.StringBuilder();
                log.AppendLine($"设备日志 - {DateTime.Now}");
                log.AppendLine($"设备名称: {_currentDeviceInfo?.DeviceName ?? "未知"}");
                log.AppendLine($"序列号: {_currentDeviceInfo?.SerialNumber ?? "未知"}");
                log.AppendLine($"连接状态: {(_isConnected ? "已连接" : "未连接")}");
                log.AppendLine($"采集状态: {(_isGrabbing ? "采集中" : "已停止")}");
                log.AppendLine($"帧数: {_frameNumber}");
                log.AppendLine($"温度: {GetDeviceTemperature():F1}°C");

                return log.ToString();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "获取设备日志失败", ex);
                return $"获取日志失败: {ex.Message}";
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            try
            {
                Disconnect();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(-1, "释放资源异常", ex);
            }
        }

        #endregion
    }
}
