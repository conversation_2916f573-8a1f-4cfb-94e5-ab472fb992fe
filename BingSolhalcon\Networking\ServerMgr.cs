﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Networking
{
    public class ServerMgr
    {
        #region 服务器字段
        private static AsyncTcpServer _tcpServer = null;
        private string _localIp = "127.0.0.1";
        private int _port = 888;

        /// <summary>
        /// 写数据是否成功标志量,1表示成功
        /// </summary>
        public static int WriteDataFlag = 0;
        public static int RecvDataFlag = 0;
        public static double RecvSnapFlag = 0;

        private Thread recvDataThrd = null;
        CancellationTokenSource recvDataCts = null;
        object recvObj = new object();
        #endregion

        #region 事件
        public delegate void ClientConnected(string ip);
        public ClientConnected OnClientConnected;
        public delegate void ClientDisconnected(string ip);
        public ClientDisconnected OnClientDisconnected;
        public delegate void ReceiveComData(ComData comdate);
        public ReceiveComData OnReceiveComData;
        #endregion

        #region 构造器
        public ServerMgr()
            : this("127.0.0.1", 888)
        {
        }

        public ServerMgr(string ip, int port)
        {
            this._localIp = ip;
            this._port = port;
        }
        #endregion

        #region 资源管理
        public bool open()
        {
            //开启服务器
            _tcpServer = new AsyncTcpServer(this._localIp, this._port, (new ComData()).GetType());
            _tcpServer.Encoding = Encoding.UTF8;

            _tcpServer.ClientConnected +=
                          new EventHandler<TcpClientConnectedEventArgs>(server_ClientConnected);
            _tcpServer.ClientDisconnected +=
                          new EventHandler<TcpClientDisconnectedEventArgs>(server_ClientDisconnected);
            _tcpServer.PlaintextReceived +=
                          new EventHandler<TcpDatagramReceivedEventArgs<string>>(server_PlaintextReceived);

            _tcpServer.Start();
            //this._tcpServer.TcpServerStart();

            //启动侦听线程
            this.recvDataCts = new CancellationTokenSource();
            this.recvDataThrd = new Thread(new ThreadStart(this.RecvDataFunc));
            this.recvDataThrd.IsBackground = true;
            this.recvDataThrd.Start();

            return true;
        }

        private void server_ClientConnected(object sender, TcpClientConnectedEventArgs e)
        {
            //Logger.Debug(string.Format(CultureInfo.InvariantCulture,
            //    "TCP client {0} has connected.",
            //    e.TcpClient.Client.RemoteEndPoint.ToString()));
            if (OnClientConnected != null)
            {
                OnClientConnected(e.TcpClient.Client.RemoteEndPoint.ToString());
            }
        }

        private void server_ClientDisconnected(object sender, TcpClientDisconnectedEventArgs e)
        {
            //Logger.Debug(string.Format(CultureInfo.InvariantCulture,
            //    "TCP client {0} has disconnected.",
            //    e.TcpClient.Client.RemoteEndPoint.ToString()));
            if (OnClientDisconnected != null)
            {
                OnClientDisconnected(e.TcpClient.Client.RemoteEndPoint.ToString());
            }
        }

        private void server_PlaintextReceived(object sender, TcpDatagramReceivedEventArgs<string> e)
        {
            //if (e.Datagram != "Received")
            //{
            //    Console.Write(string.Format("Client : {0} --> ",
            //      e.TcpClient.Client.RemoteEndPoint.ToString()));
            //    Console.WriteLine(string.Format("{0}", e.Datagram));
            //    server.Send(e.TcpClient, "Server has received you text : " + e.Datagram);
            //}            
        }

        public void close()
        {
            //关闭侦听线程
            //this.recvDataCts.Cancel();

            //关闭服务器
            //this._tcpServer.CloseAll();
            _tcpServer.Dispose();
        }
        #endregion

        #region 指令解析线程
        //指令集解析线程
        private void RecvDataFunc()
        {
            while (true)
            {
                if (recvDataCts.Token.IsCancellationRequested)
                    return;

                // 线程锁
                lock (recvObj)
                {
                    if (_tcpServer.comDataQueue.Count < 1)
                        continue;
                }
                // 查询指令队列对首
                ComData _comData = _tcpServer.comDataQueue.Peek();
                switch (_comData.cmdID)
                {
                    case 0:
                        Function_0000(_comData);
                        break;
                    default:
                        break;
                }
                // 将数据发送去
                if (OnReceiveComData != null)
                {
                    OnReceiveComData(_comData);
                }
                // 将队首的指令移除
                if (_tcpServer.comDataQueue.Count > 0)
                    _tcpServer.comDataQueue.Dequeue();
            }
        }
        #endregion

        #region 成品检测1相机
        private void Function_0000(ComData _comData)
        {
            ComData _resultData = new ComData(_comData.cmdID, _comData.pdtID);
            string _resultMsg = "";
            _resultData.iData[0] = _comData.iData[0] + 1;
            _resultData.dData[0] = _comData.dData[0] + 0.1;
            _resultData.cData = _resultMsg.PadRight(256, '\0').ToCharArray();
            _tcpServer.SendMessage(_resultData);
        }
        #endregion

        #region 字符串处理
        public static string CharToString(char[] chars)
        {
            string str = "";
            int len = 0;
            for (int i = 0; i < chars.Length; i++)
            {
                if (chars[i] == '\0')
                {
                    len = i;
                    break;
                }
            }
            string msg = new string(chars);
            str = msg.Substring(0, len);
            return str;
        }

        /// <summary>
        /// 将列表中的数据通过-号连接，并返回连接后的字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="lst"></param>
        /// <returns></returns>
        public static string GetString<T>(List<T> lst)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < lst.Count; i++)
            {
                sb.Append(lst[i]);
                if (i < lst.Count - 1)
                    sb.Append("-");
            }
            return sb.ToString();
        }
        #endregion
    }
}
