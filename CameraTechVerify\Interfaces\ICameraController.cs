using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using CameraTechVerify.Models;

namespace CameraTechVerify.Interfaces
{
    /// <summary>
    /// 相机控制接口
    /// </summary>
    public interface ICameraController : IDisposable
    {
        #region 事件定义
        
        /// <summary>
        /// 图像接收事件
        /// </summary>
        event EventHandler<ImageReceivedEventArgs> ImageReceived;
        
        /// <summary>
        /// 连接状态改变事件
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        
        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        #endregion

        #region 基本属性

        /// <summary>
        /// 相机是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 相机是否正在采集
        /// </summary>
        bool IsGrabbing { get; }

        /// <summary>
        /// 相机设备信息
        /// </summary>
        CameraDeviceInfo DeviceInfo { get; }

        /// <summary>
        /// 相机参数配置
        /// </summary>
        CameraParameters Parameters { get; set; }

        #endregion

        #region 设备管理

        /// <summary>
        /// 枚举可用的相机设备
        /// </summary>
        /// <returns>相机设备列表</returns>
        List<CameraDeviceInfo> EnumerateDevices();

        /// <summary>
        /// 枚举指定类型的相机设备
        /// </summary>
        /// <param name="deviceType">设备类型 (如: GigE, USB3, CameraLink等)</param>
        /// <returns>相机设备列表</returns>
        List<CameraDeviceInfo> EnumerateDevices(string deviceType);

        /// <summary>
        /// 枚举指定网段的 GigE 相机设备
        /// </summary>
        /// <param name="ipRange">IP 地址范围 (如: "***********/24")</param>
        /// <returns>相机设备列表</returns>
        List<CameraDeviceInfo> EnumerateGigEDevices(string ipRange = null);

        /// <summary>
        /// 连接到指定的相机设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>连接是否成功</returns>
        bool Connect(CameraDeviceInfo deviceInfo);

        /// <summary>
        /// 通过索引连接相机
        /// </summary>
        /// <param name="deviceIndex">设备索引</param>
        /// <returns>连接是否成功</returns>
        bool Connect(int deviceIndex);

        /// <summary>
        /// 通过 IP 地址连接 GigE 相机
        /// </summary>
        /// <param name="ipAddress">相机 IP 地址</param>
        /// <param name="port">端口号 (默认 3956)</param>
        /// <returns>连接是否成功</returns>
        bool ConnectByIP(string ipAddress, int port = 3956);

        /// <summary>
        /// 通过序列号连接相机
        /// </summary>
        /// <param name="serialNumber">相机序列号</param>
        /// <returns>连接是否成功</returns>
        bool ConnectBySerialNumber(string serialNumber);

        /// <summary>
        /// 通过 MAC 地址连接 GigE 相机
        /// </summary>
        /// <param name="macAddress">MAC 地址</param>
        /// <returns>连接是否成功</returns>
        bool ConnectByMacAddress(string macAddress);

        /// <summary>
        /// 断开相机连接
        /// </summary>
        void Disconnect();

        /// <summary>
        /// 检查指定 IP 地址的相机是否可达
        /// </summary>
        /// <param name="ipAddress">相机 IP 地址</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>是否可达</returns>
        bool PingCamera(string ipAddress, int timeout = 3000);

        /// <summary>
        /// 强制 IP 配置 (用于配置相机 IP 地址)
        /// </summary>
        /// <param name="macAddress">相机 MAC 地址</param>
        /// <param name="newIpAddress">新的 IP 地址</param>
        /// <param name="subnetMask">子网掩码</param>
        /// <param name="gateway">网关地址</param>
        /// <returns>配置是否成功</returns>
        bool ForceIP(string macAddress, string newIpAddress, string subnetMask, string gateway);

        #endregion

        #region 图像采集

        /// <summary>
        /// 开始连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool StartGrabbing();

        /// <summary>
        /// 停止连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool StopGrabbing();

        /// <summary>
        /// 单次拍照
        /// </summary>
        /// <returns>拍摄的图像</returns>
        Bitmap CaptureImage();

        /// <summary>
        /// 异步单次拍照
        /// </summary>
        /// <returns>拍摄的图像任务</returns>
        System.Threading.Tasks.Task<Bitmap> CaptureImageAsync();

        #endregion

        #region 参数控制

        /// <summary>
        /// 设置曝光时间（微秒）
        /// </summary>
        /// <param name="exposureTime">曝光时间</param>
        /// <returns>设置是否成功</returns>
        bool SetExposureTime(float exposureTime);

        /// <summary>
        /// 获取曝光时间（微秒）
        /// </summary>
        /// <returns>当前曝光时间</returns>
        float GetExposureTime();

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="gain">增益值</param>
        /// <returns>设置是否成功</returns>
        bool SetGain(float gain);

        /// <summary>
        /// 获取增益值
        /// </summary>
        /// <returns>当前增益值</returns>
        float GetGain();

        /// <summary>
        /// 设置图像分辨率
        /// </summary>
        /// <param name="width">图像宽度</param>
        /// <param name="height">图像高度</param>
        /// <returns>设置是否成功</returns>
        bool SetResolution(int width, int height);

        /// <summary>
        /// 获取图像分辨率
        /// </summary>
        /// <returns>当前分辨率</returns>
        Size GetResolution();

        /// <summary>
        /// 设置ROI区域
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>设置是否成功</returns>
        bool SetROI(int x, int y, int width, int height);

        /// <summary>
        /// 获取ROI区域
        /// </summary>
        /// <returns>当前ROI区域</returns>
        Rectangle GetROI();

        /// <summary>
        /// 设置帧率
        /// </summary>
        /// <param name="frameRate">帧率</param>
        /// <returns>设置是否成功</returns>
        bool SetFrameRate(float frameRate);

        /// <summary>
        /// 获取帧率
        /// </summary>
        /// <returns>当前帧率</returns>
        float GetFrameRate();

        /// <summary>
        /// 设置触发模式
        /// </summary>
        /// <param name="triggerMode">触发模式</param>
        /// <returns>设置是否成功</returns>
        bool SetTriggerMode(TriggerMode triggerMode);

        /// <summary>
        /// 获取触发模式
        /// </summary>
        /// <returns>当前触发模式</returns>
        TriggerMode GetTriggerMode();

        /// <summary>
        /// 软件触发
        /// </summary>
        /// <returns>触发是否成功</returns>
        bool SoftwareTrigger();

        #endregion

        #region 高级功能

        /// <summary>
        /// 获取参数范围信息
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数范围信息</returns>
        ParameterRange GetParameterRange(string parameterName);

        /// <summary>
        /// 设置自定义参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <returns>设置是否成功</returns>
        bool SetParameter(string parameterName, object value);

        /// <summary>
        /// 获取自定义参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数值</returns>
        object GetParameter(string parameterName);

        /// <summary>
        /// 保存参数配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存是否成功</returns>
        bool SaveParametersToFile(string filePath);

        /// <summary>
        /// 从文件加载参数配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载是否成功</returns>
        bool LoadParametersFromFile(string filePath);

        /// <summary>
        /// 重置参数到默认值
        /// </summary>
        /// <returns>重置是否成功</returns>
        bool ResetParameters();

        /// <summary>
        /// 注册图像回调函数
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>注册是否成功</returns>
        bool RegisterImageCallback(Action<Bitmap, long> callback);

        /// <summary>
        /// 取消图像回调函数
        /// </summary>
        /// <returns>取消是否成功</returns>
        bool UnregisterImageCallback();

        /// <summary>
        /// 获取设备温度
        /// </summary>
        /// <returns>设备温度（摄氏度）</returns>
        float GetDeviceTemperature();

        /// <summary>
        /// 获取设备统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Dictionary<string, object> GetDeviceStatistics();

        /// <summary>
        /// 设置图像格式
        /// </summary>
        /// <param name="pixelFormat">像素格式</param>
        /// <returns>设置是否成功</returns>
        bool SetPixelFormat(string pixelFormat);

        /// <summary>
        /// 获取图像格式
        /// </summary>
        /// <returns>当前像素格式</returns>
        string GetPixelFormat();

        /// <summary>
        /// 获取支持的像素格式列表
        /// </summary>
        /// <returns>支持的像素格式</returns>
        List<string> GetSupportedPixelFormats();

        /// <summary>
        /// 设置白平衡
        /// </summary>
        /// <param name="mode">白平衡模式</param>
        /// <returns>设置是否成功</returns>
        bool SetWhiteBalance(string mode);

        /// <summary>
        /// 执行一次白平衡
        /// </summary>
        /// <returns>执行是否成功</returns>
        bool ExecuteWhiteBalance();

        /// <summary>
        /// 保存当前图像
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <param name="format">图像格式</param>
        /// <returns>保存是否成功</returns>
        bool SaveImage(string filePath, string format = "PNG");

        /// <summary>
        /// 开始录制视频
        /// </summary>
        /// <param name="filePath">视频文件路径</param>
        /// <param name="codec">编码器</param>
        /// <param name="frameRate">帧率</param>
        /// <returns>开始是否成功</returns>
        bool StartRecording(string filePath, string codec = "H264", float frameRate = 30);

        /// <summary>
        /// 停止录制视频
        /// </summary>
        /// <returns>停止是否成功</returns>
        bool StopRecording();

        /// <summary>
        /// 获取网络统计信息（GigE 相机）
        /// </summary>
        /// <returns>网络统计信息</returns>
        Dictionary<string, object> GetNetworkStatistics();

        /// <summary>
        /// 重启设备
        /// </summary>
        /// <returns>重启是否成功</returns>
        bool RestartDevice();

        /// <summary>
        /// 获取设备日志
        /// </summary>
        /// <returns>设备日志</returns>
        string GetDeviceLog();

        #endregion
    }
}
