﻿using BingSolhalcon.resources;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BingSolhalcon
{
    public partial class ModifyPassword : Form
    {
        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx));


        static Dictionary<string, string> nameMap = new Dictionary<string, string>{
                    { "Operator", "操作员"},
                    { "Technician", "技术员"},
                    { "Expert", "专家"},
                    { "operador", "操作员"},
                    { "técnico", "技术员"},
                    { "experto", "专家"}
            };

        public ModifyPassword()
        {
            InitializeComponent();
        }
        
        private void ModifyPassword_Load(object sender, EventArgs e)
        {   
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void btnModify_Click(object sender, EventArgs e)
        {
            try
            {
                modifyPassword();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
        private void modifyPassword()
        {

            string name = txtLoginName.Text;
            string passwordOld = txtPasswordOld.Text;
            string passwordNew = txtPasswordNew.Text;
            string passwordNew2 = txtPasswordNew2.Text;

            if (passwordNew.Equals(""))
            {
                MessageBoxEX.Show(res.GetString("passwordEmpty"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return;
            }

            if (!passwordNew.Equals(passwordNew2))
            {
                MessageBoxEX.Show(res.GetString("passwordInconsistency"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return;
            }


            if (nameMap.ContainsKey(name))
            {
                name = nameMap[name];
            }

            string connection = @"Server=.;Database=MyDB;Trusted_Connection=SSPI";

            SQlFun m_sqlfun = new SQlFun();
            m_sqlfun.connection(connection);
            m_sqlfun.Sql_open();
            if (m_sqlfun.conn.State != ConnectionState.Open)
            {
                MessageBoxEX.Show(res.GetString("databaseFault"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return;
            }

            bool passwordCorrect = false;
            m_sqlfun.fill_datatable("syuser");
            foreach(DataRow row in m_sqlfun.dTable.Rows)
            {
                if (name.Equals(row[1].ToString().Trim()) && passwordOld.Equals(row[2].ToString().Trim()))
                {
                    passwordCorrect = true;
                    break;
                }
            }
            if (!passwordCorrect)
            {
                MessageBoxEX.Show(res.GetString("faultPaw"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return;
            }

            var sqlcmd = m_sqlfun.conn.CreateCommand();
            sqlcmd.CommandText = string.Format("update syuser set [密码]='{0}' where [用户名]='{1}'", passwordNew, name);
            sqlcmd.ExecuteNonQuery();

            MessageBoxEX.Show(res.GetString("modifySucceeded"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            Close();
        }

        private void txtPasswordNew_KeyPress(object sender, KeyPressEventArgs e)
        {
            if ((e.KeyChar < '0' || e.KeyChar > '9') && e.KeyChar != '\b')
            {
                e.Handled = true;
            }
            if (txtPasswordNew.SelectionStart == 0 && e.KeyChar == '0')
            {
                e.Handled = true;
            }
        }
    }
}
