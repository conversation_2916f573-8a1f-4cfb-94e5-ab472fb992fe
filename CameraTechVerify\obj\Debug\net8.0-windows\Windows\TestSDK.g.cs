﻿#pragma checksum "..\..\..\..\Windows\TestSDK.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "84A13E6B3C55D1022E2BE74694657507279EBA18"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using CameraTechVerify.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CameraTechVerify.Windows {
    
    
    /// <summary>
    /// TestSDK
    /// </summary>
    public partial class TestSDK : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbDeviceList;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Image1;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnEnum;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnOpen;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnClose;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton bnContinuesMode;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton bnTriggerMode;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnStartGrab;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnStopGrab;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox cbSoftTrigger;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnTriggerExec;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnStartRecord;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnStopRecord;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnSaveBmp;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnSaveJpg;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnSaveTiff;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnSavePng;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbExposure;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbGain;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbFrameRate;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbPixelFormat;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnGetParam;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Windows\TestSDK.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bnSetParam;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CameraTechVerify;component/windows/testsdk.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\TestSDK.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 7 "..\..\..\..\Windows\TestSDK.xaml"
            ((CameraTechVerify.Windows.TestSDK)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cbDeviceList = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.Image1 = ((System.Windows.Controls.Image)(target));
            return;
            case 4:
            this.bnEnum = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnEnum.Click += new System.Windows.RoutedEventHandler(this.bnEnum_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.bnOpen = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnOpen.Click += new System.Windows.RoutedEventHandler(this.bnOpen_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.bnClose = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnClose.Click += new System.Windows.RoutedEventHandler(this.bnClose_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.bnContinuesMode = ((System.Windows.Controls.RadioButton)(target));
            
            #line 49 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnContinuesMode.Checked += new System.Windows.RoutedEventHandler(this.bnContinuesMode_Checked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.bnTriggerMode = ((System.Windows.Controls.RadioButton)(target));
            
            #line 50 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnTriggerMode.Checked += new System.Windows.RoutedEventHandler(this.bnTriggerMode_Checked);
            
            #line default
            #line hidden
            return;
            case 9:
            this.bnStartGrab = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnStartGrab.Click += new System.Windows.RoutedEventHandler(this.bnStartGrab_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.bnStopGrab = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnStopGrab.Click += new System.Windows.RoutedEventHandler(this.bnStopGrab_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.cbSoftTrigger = ((System.Windows.Controls.CheckBox)(target));
            
            #line 53 "..\..\..\..\Windows\TestSDK.xaml"
            this.cbSoftTrigger.Checked += new System.Windows.RoutedEventHandler(this.cbSoftTrigger_Checked);
            
            #line default
            #line hidden
            
            #line 53 "..\..\..\..\Windows\TestSDK.xaml"
            this.cbSoftTrigger.Unchecked += new System.Windows.RoutedEventHandler(this.cbSoftTrigger_Unchecked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.bnTriggerExec = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnTriggerExec.Click += new System.Windows.RoutedEventHandler(this.bnTriggerExec_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.bnStartRecord = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnStartRecord.Click += new System.Windows.RoutedEventHandler(this.bnStartRecord_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.bnStopRecord = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnStopRecord.Click += new System.Windows.RoutedEventHandler(this.bnStopRecord_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.bnSaveBmp = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnSaveBmp.Click += new System.Windows.RoutedEventHandler(this.bnSaveBmp_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.bnSaveJpg = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnSaveJpg.Click += new System.Windows.RoutedEventHandler(this.bnSaveJpg_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.bnSaveTiff = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnSaveTiff.Click += new System.Windows.RoutedEventHandler(this.bnSaveTiff_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.bnSavePng = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnSavePng.Click += new System.Windows.RoutedEventHandler(this.bnSavePng_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.tbExposure = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.tbGain = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.tbFrameRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.cbPixelFormat = ((System.Windows.Controls.ComboBox)(target));
            
            #line 95 "..\..\..\..\Windows\TestSDK.xaml"
            this.cbPixelFormat.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cbPixelFormat_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.bnGetParam = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnGetParam.Click += new System.Windows.RoutedEventHandler(this.bnGetParam_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.bnSetParam = ((System.Windows.Controls.Button)(target));
            
            #line 97 "..\..\..\..\Windows\TestSDK.xaml"
            this.bnSetParam.Click += new System.Windows.RoutedEventHandler(this.bnSetParam_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

