﻿using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using HalconDotNet;
using _3DFunc;


namespace BingSolhalcon.UI
{
    public partial class HKLaser : Form
    {
        public delegate void delegateGetDatabasename(ref string databasename,ref string  numberofdecimal);
        public event delegateGetDatabasename evenGetDatabasename;
        string m_databasename, numberofdecimal;
        string connection;

        
        HKVision_3D m_HKVision_3D;
        HObject ho_ContourAngle2;
        HObject ho_Contour3, ho_Contour4;
        static HTuple hv_ObjectModel3D;
        HTuple hv_DrawIDPattern, hv_DrawID, hv_DrawID1;
        HTuple hv_Row = new HTuple(), hv_Col = new HTuple(), hv_Distance = new HTuple();
        HTuple RowDown = new HTuple(), ColDown = new HTuple();
        HTuple Rec_Old_Row1 = new HTuple(), Rec_Old_Col1 = new HTuple(), Rec_Old_Row2 = new HTuple(), Rec_Old_Col2 = new HTuple();
        HTuple Lin_Old_Row1 = new HTuple(), Lin_Old_Col1 = new HTuple(), Lin_Old_Row2 = new HTuple(), Lin_Old_Col2 = new HTuple();
        _3DFunc._3DFunc m_3dfun;
        int c1_ngcount;
        Task t_task;

        CancellationToken ct;
        bool m_lock01 = false, m_saveimage = false;
        double x = 0, y = 0;
        const int UM_PIX = 150;

    
        private void action()
        {


            // Local iconic variables 

            HObject ho_Image1, ho_ShapeModel, ho_ImageResult = null;
            HObject ho_IntersectionX = null, ho_ContourAngle2 = null, ho_Image = null;
            HObject ho_ImageCleared = null, ho_Rectangle1 = null, ho_ModeCroppedContours = null;
            HObject ho_region = null, ho_ContoursAffinTrans = null, ho_Rectangle = null;
            HObject ho_CroppedContours = null, ho_Contour1 = null, ho_Contour2 = null;
            HObject ho_ParallelContours = null, ho_Contour3 = null, ho_Contour4 = null;

            // Local control variables 

            HTuple hv_ModelID1 = new HTuple(), hv_SearchRow1 = new HTuple();
            HTuple hv_SearchColumn1 = new HTuple(), hv_SearchAngle1 = new HTuple();
            HTuple hv_Score1 = new HTuple(), hv_Files = new HTuple();
            HTuple hv_i = new HTuple(), hv_ObjectModel3D = new HTuple();
            HTuple hv_Status = new HTuple(), hv_WindowHandle1 = new HTuple();
            HTuple hv_PoseOut = new HTuple(), hv_ObjectModel3D1 = new HTuple();
            HTuple hv_rang_zmax = new HTuple(), hv_rang_zmin = new HTuple();
            HTuple hv_rang_xmax = new HTuple(), hv_rang_xmin = new HTuple();
            HTuple hv_GenParamValuez = new HTuple(), hv_GenParamValuex = new HTuple();
            HTuple hv_Max_z = new HTuple(), hv_Max_x = new HTuple();
            HTuple hv_Min_z = new HTuple(), hv_Min_x = new HTuple();
            HTuple hv_Thresholdedz = new HTuple(), hv_Thresholdedx = new HTuple();
            HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
            HTuple hv_WindowHandle = new HTuple(), hv_BorderFact = new HTuple();
            HTuple hv_IsTelecentric = new HTuple(), hv_translate1 = new HTuple();
            HTuple hv_revolve1 = new HTuple(), hv_GenParamValuey = new HTuple();
            HTuple hv_Min_y = new HTuple(), hv_translate2 = new HTuple();
            HTuple hv_Pose = new HTuple(), hv_CamParam = new HTuple();
            HTuple hv_Labels = new HTuple(), hv_VisParamName = new HTuple();
            HTuple hv_VisParamValue = new HTuple(), hv_Title = new HTuple();
            HTuple hv_cropRow1 = new HTuple(), hv_cropColumn1 = new HTuple();
            HTuple hv_cropRow2 = new HTuple(), hv_cropColumn2 = new HTuple();
            HTuple hv_ModelID = new HTuple(), hv_ModeRow = new HTuple();
            HTuple hv_ModeColumn = new HTuple(), hv_ModeAngle = new HTuple();
            HTuple hv_ModeScore = new HTuple(), hv_DrawID = new HTuple();
            HTuple hv_recrow1 = new HTuple(), hv_recrow2 = new HTuple();
            HTuple hv_reccol1 = new HTuple(), hv_reccol2 = new HTuple();
            HTuple hv_DrawID1 = new HTuple(), hv_linerow1 = new HTuple();
            HTuple hv_linerow2 = new HTuple(), hv_linecol1 = new HTuple();
            HTuple hv_linecol2 = new HTuple(), hv_offsetrecrow1 = new HTuple();
            HTuple hv_offsetrecrow2 = new HTuple(), hv_offsetreccol1 = new HTuple();
            HTuple hv_offsetreccol2 = new HTuple(), hv_offsetlinerow1 = new HTuple();
            HTuple hv_offsetlinerow2 = new HTuple(), hv_offsetlinecol1 = new HTuple();
            HTuple hv_offsetlinecol2 = new HTuple(), hv_SearchRow = new HTuple();
            HTuple hv_SearchColumn = new HTuple(), hv_SearchAngle = new HTuple();
            HTuple hv_Score = new HTuple(), hv_HomMat2DIdentity = new HTuple();
            HTuple hv_HomMat2DRotate = new HTuple(), hv_HomMat2DTranslate = new HTuple();
            HTuple hv_CroppedRow = new HTuple(), hv_CroppedCol = new HTuple();
            HTuple hv_InterRow = new HTuple(), hv_InterColumn = new HTuple();
            HTuple hv_IsOverlapping = new HTuple(), hv_Distance = new HTuple();
            HTuple hv_RowProj = new HTuple(), hv_ColProj = new HTuple();
            HTuple hv_OffsetDistance = new HTuple(), hv_Row = new HTuple();
            HTuple hv_Col = new HTuple(), hv_Length = new HTuple();
            HTuple hv_RowProj1 = new HTuple(), hv_ColProj1 = new HTuple();
            HTuple hv_RowProj2 = new HTuple(), hv_ColProj2 = new HTuple();
            // Initialize local and output iconic variables 
            HOperatorSet.GenEmptyObj(out ho_Image1);
            HOperatorSet.GenEmptyObj(out ho_ShapeModel);
            HOperatorSet.GenEmptyObj(out ho_ImageResult);
            HOperatorSet.GenEmptyObj(out ho_IntersectionX);
            HOperatorSet.GenEmptyObj(out ho_ContourAngle2);
            HOperatorSet.GenEmptyObj(out ho_Image);
            HOperatorSet.GenEmptyObj(out ho_ImageCleared);
            HOperatorSet.GenEmptyObj(out ho_Rectangle1);
            HOperatorSet.GenEmptyObj(out ho_ModeCroppedContours);
            HOperatorSet.GenEmptyObj(out ho_region);
            HOperatorSet.GenEmptyObj(out ho_ContoursAffinTrans);
            HOperatorSet.GenEmptyObj(out ho_Rectangle);
            HOperatorSet.GenEmptyObj(out ho_CroppedContours);
            HOperatorSet.GenEmptyObj(out ho_Contour1);
            HOperatorSet.GenEmptyObj(out ho_Contour2);
            HOperatorSet.GenEmptyObj(out ho_ParallelContours);
            HOperatorSet.GenEmptyObj(out ho_Contour3);
            HOperatorSet.GenEmptyObj(out ho_Contour4);
            try
            {
               

                    hv_ObjectModel3D.Dispose(); hv_Status.Dispose();
                    HOperatorSet.ReadObjectModel3d("C:/Users/<USER>/Desktop/07119C10/重复测量/07119C10/下午采图/2022-09-29_32.ply",
                        "m", new HTuple(), new HTuple(), out hv_ObjectModel3D, out hv_Status);
                   
                    //去掉无效数据,海康威视的-32768
                    hv_ObjectModel3D1.Dispose();
                m_3dfun.filterdata(hv_ObjectModel3D, -32768, out hv_ObjectModel3D1);

                hv_rang_zmax.Dispose();
                    hv_rang_zmax = 40000;
                    hv_rang_zmin.Dispose();
                    hv_rang_zmin = 20000;
                    hv_rang_xmax.Dispose();
                    hv_rang_xmax = 70000;
                    hv_rang_xmin.Dispose();
                    hv_rang_xmin = 0;
                    hv_GenParamValuez.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_ObjectModel3D1, "point_coord_z", out hv_GenParamValuez);
                    hv_GenParamValuex.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_ObjectModel3D1, "point_coord_x", out hv_GenParamValuex);


                    hv_Max_z.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                    hv_Max_x.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);
                    hv_Min_z.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                    hv_Min_x.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);

                    hv_Thresholdedz.Dispose();
                    HOperatorSet.SelectPointsObjectModel3d(hv_ObjectModel3D1, "point_coord_z",
                        hv_rang_zmin, hv_rang_zmax, out hv_Thresholdedz);
                 
                    hv_Thresholdedx.Dispose();
                    HOperatorSet.SelectPointsObjectModel3d(hv_Thresholdedz, "point_coord_x",
                        hv_rang_xmin, hv_rang_xmax, out hv_Thresholdedx);

                   
                    hv_GenParamValuez.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_z", out hv_GenParamValuez);
                    hv_GenParamValuex.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_x", out hv_GenParamValuex);

                    hv_Min_z.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                    hv_Max_z.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                    hv_Min_x.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);
                    hv_Max_x.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);


                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        {
                            HTuple
                              ExpTmpLocalVar_GenParamValuez = hv_GenParamValuez - hv_Min_z;
                            hv_GenParamValuez.Dispose();
                            hv_GenParamValuez = ExpTmpLocalVar_GenParamValuez;
                        }
                    }
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        {
                            HTuple
                              ExpTmpLocalVar_GenParamValuex = hv_GenParamValuex - hv_Min_x;
                            hv_GenParamValuex.Dispose();
                            hv_GenParamValuex = ExpTmpLocalVar_GenParamValuex;
                        }
                    }

                    
                    //定义虚拟相机分辨率.
                  
                    //定义一个系数用于在3D模型中计算图像的边缘
                    hv_BorderFact.Dispose();
                    hv_BorderFact = 1.5;
                    //是否选择镜头.
                    hv_IsTelecentric.Dispose();
                    hv_IsTelecentric = 1;

                    //定义相机参数以及相机初始位姿.
                    //CutPlanePose := [0,1,0,90,0,0,0]
                    //triangulate_object_model_3d (ObjectModel3D1, 'greedy', [], [], TransObjectModel, Information)
                    //intersect_plane_object_model_3d (TransObjectModel, CutPlanePose, ObjectModel3DIntersection)
                    //gen_plane_object_model_3d (CutPlanePose, [-1,-1,1,1] * 30000, [-1,1,1,-1] * 30000, IntersectionPlane)
                    //get_cam_par_and_pose (IsTelecentric, ObjectModel3D1, BorderFact, Width, Height, PoseInit, CameraParam)
                    //便于显示，X与Z最小值都变0
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_translate1.Dispose();
                        HOperatorSet.RigidTransObjectModel3d(hv_Thresholdedx, ((((((-hv_Min_x)).TupleConcat(
                            0))).TupleConcat(-hv_Min_z))).TupleConcat((((new HTuple(0)).TupleConcat(
                            0)).TupleConcat(0)).TupleConcat(0)), out hv_translate1);
                    }
                    hv_revolve1.Dispose();
                    HOperatorSet.RigidTransObjectModel3d(hv_translate1, ((((((new HTuple(0)).TupleConcat(
                        0)).TupleConcat(0)).TupleConcat(90)).TupleConcat(0)).TupleConcat(0)).TupleConcat(
                        0), out hv_revolve1);
                    hv_GenParamValuey.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_revolve1, "point_coord_y", out hv_GenParamValuey);
                    hv_Min_y.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuey, out hv_Min_y);
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_translate2.Dispose();
                        HOperatorSet.RigidTransObjectModel3d(hv_revolve1, (((new HTuple(0)).TupleConcat(
                            -hv_Min_y))).TupleConcat(((((new HTuple(0)).TupleConcat(0)).TupleConcat(
                            0)).TupleConcat(0)).TupleConcat(0)), out hv_translate2);
                    }
                    hv_Pose.Dispose();
                    HOperatorSet.CreatePose(0, 0, 0, 0, 0, 0, "Rp+T", "gba", "point", out hv_Pose);
                    hv_CamParam.Dispose();
                    hv_CamParam = new HTuple();
                    hv_CamParam[0] = 0;
                    hv_CamParam[1] = 0;
                    hv_CamParam[2] = 150;
                    hv_CamParam[3] = 150;
                    hv_CamParam[4] = 0;
                    hv_CamParam[5] = 0;
                    hv_CamParam[6] = 500;
                    hv_CamParam[7] = 500;
                    ho_IntersectionX.Dispose();
                    HOperatorSet.ProjectObjectModel3d(out ho_IntersectionX, hv_translate2, hv_CamParam,
                        hv_Pose, (((new HTuple("data")).TupleConcat("point_shape")).TupleConcat(
                        "point_size")).TupleConcat("union_adjacent_contours"), (((new HTuple("auto")).TupleConcat(
                        "circle")).TupleConcat(1)).TupleConcat("true"));

                   
                    //visualize_object_model_3d (WindowHandle, [translate2], [], [], VisParamName, VisParamValue, Title, Labels, [], PoseOut)
                    //平面像素的处理
                   
                        HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                   
                        HOperatorSet.DispObj(ho_IntersectionX, hWindowControl1.HalconWindow);
                   
                    ho_ContourAngle2.Dispose();
                m_3dfun.PointTransXld(ho_IntersectionX, out ho_ContourAngle2);
                    //gen_region_contour_xld (ContourAngle2, Region, 'filled')
                    ho_Image.Dispose();
                    HOperatorSet.GenImageConst(out ho_Image, "byte", 500, 500);
                    ho_ImageCleared.Dispose();
                    HOperatorSet.GenImageProto(ho_Image, out ho_ImageCleared, 0);
                    //paint_region (Region, ImageCleared, ImageResult, 255, 'fill')
                    ho_ImageResult.Dispose();
                    HOperatorSet.PaintXld(ho_ContourAngle2, ho_ImageCleared, out ho_ImageResult,
                        255);

                    
                    hv_offsetrecrow1.Dispose();
                    hv_offsetrecrow1 = -84.37;
                    hv_offsetrecrow2.Dispose();
                    hv_offsetrecrow2 = 15.6334;
                    hv_offsetreccol1.Dispose();
                    hv_offsetreccol1 = -22.2413;
                    hv_offsetreccol2.Dispose();
                    hv_offsetreccol2 = -8.26521;

                    hv_offsetlinerow1.Dispose();
                    hv_offsetlinerow1 = -26.7079;
                    hv_offsetlinerow2.Dispose();
                    hv_offsetlinerow2 = 52.0251;
                    hv_offsetlinecol1.Dispose();
                    hv_offsetlinecol1 = 10.2231;
                    hv_offsetlinecol2.Dispose();
                    hv_offsetlinecol2 = 10.8966;




                    if (HDevWindowStack.IsOpen())
                    {
                        HOperatorSet.DispObj(ho_ImageResult, hWindowControl1.HalconWindow);
                    }
                    hv_ModelID1.Dispose();
                    HOperatorSet.ReadShapeModel("D://工作//2022项目//海康3D相机//3Dhalcon//00616C11.shm", out hv_ModelID1);
                    ho_ShapeModel.Dispose();
                    HOperatorSet.GetShapeModelContours(out ho_ShapeModel, hv_ModelID1, 1);
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_SearchRow.Dispose(); hv_SearchColumn.Dispose(); hv_SearchAngle.Dispose(); hv_Score.Dispose();
                        HOperatorSet.FindShapeModel(ho_ImageResult, hv_ModelID1, 0, (new HTuple(180)).TupleRad()
                            , 0.5, 1, 0.5, "least_squares", 8, 0.9, out hv_SearchRow, out hv_SearchColumn,
                            out hv_SearchAngle, out hv_Score);
                    }
                    hv_HomMat2DIdentity.Dispose();
                    HOperatorSet.HomMat2dIdentity(out hv_HomMat2DIdentity);
                    hv_HomMat2DRotate.Dispose();
                    HOperatorSet.HomMat2dRotate(hv_HomMat2DIdentity, hv_SearchAngle, 0, 0, out hv_HomMat2DRotate);
                    hv_HomMat2DTranslate.Dispose();
                    HOperatorSet.HomMat2dTranslate(hv_HomMat2DRotate, hv_SearchRow, hv_SearchColumn,
                        out hv_HomMat2DTranslate);
                    ho_ContoursAffinTrans.Dispose();
                    HOperatorSet.AffineTransContourXld(ho_ShapeModel, out ho_ContoursAffinTrans,
                        hv_HomMat2DTranslate);

                    if (HDevWindowStack.IsOpen())
                    {
                        HOperatorSet.DispObj(ho_ContoursAffinTrans, hWindowControl1.HalconWindow);

                }
                    //原来测量矩形转换
                    hv_recrow1.Dispose(); hv_reccol1.Dispose();
                    HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_offsetrecrow1, hv_offsetreccol1,
                        out hv_recrow1, out hv_reccol1);
                    hv_recrow2.Dispose(); hv_reccol2.Dispose();
                    HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_offsetrecrow2, hv_offsetreccol2,
                        out hv_recrow2, out hv_reccol2);

                    hv_linerow1.Dispose(); hv_linecol1.Dispose();
                    HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_offsetlinerow1,
                        hv_offsetlinecol1, out hv_linerow1, out hv_linecol1);
                    hv_linerow2.Dispose(); hv_linecol2.Dispose();
                    HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_offsetlinerow2,
                        hv_offsetlinecol2, out hv_linerow2, out hv_linecol2);
                    //gen_rectangle1 (Rectangle, 66, 110+ (offset_column)/150, 129, 114+ (offset_column)/150)


                    ho_Rectangle.Dispose();
                    HOperatorSet.GenRectangle1(out ho_Rectangle, hv_recrow1, hv_reccol1, hv_recrow2,
                        hv_reccol2);
                    //crop_contours_xld (ContourAngle2, CroppedContours, 66, 110+ (offset_column)/150, 129, 114+ (offset_column)/150, 'true')
                    ho_CroppedContours.Dispose();
                    HOperatorSet.CropContoursXld(ho_ContourAngle2, out ho_CroppedContours, hv_recrow1,
                        hv_reccol1, hv_recrow2, hv_reccol2, "true");
                    hv_CroppedRow.Dispose(); hv_CroppedCol.Dispose();
                    HOperatorSet.GetContourXld(ho_CroppedContours, out hv_CroppedRow, out hv_CroppedCol);


                    //fit_line_contour_xld (CroppedContours, 'tukey', -1, 0, 5, 2, FitRowBegin, FitColBegin, FitRowEnd, FitColEnd, Nr, Nc, Dist)
                    if (HDevWindowStack.IsOpen())
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "blue");
                    }
                    //gen_contour_polygon_xld (Contour, [FitRowBegin,FitRowEnd], [FitColBegin,FitColEnd])




                    //gen_contour_polygon_xld (Contour1, [138,179], [137+ (offset_column)/150,137+ (offset_column)/150])
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        ho_Contour1.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_linerow1.TupleConcat(
                            hv_linerow2), hv_linecol1.TupleConcat(hv_linecol2));
                    }
                    //求竖直轴与XLD的交线
                    hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                    HOperatorSet.IntersectionContoursXld(ho_ContourAngle2, ho_Contour1, "all",
                        out hv_InterRow, out hv_InterColumn, out hv_IsOverlapping);
                    //求点到直线的距离
                    //distance_pl (InterRow, InterColumn, FitRowBegin, FitColBegin, FitRowEnd, FitColEnd, Distance)

                    hv_Distance.Dispose();
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_Distance = hv_InterRow - (hv_CroppedRow.TupleSelect(
                            (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
                            ));
                    }
                    {
                        HTuple ExpTmpOutVar_0;
                        HOperatorSet.TupleAbs(hv_Distance, out ExpTmpOutVar_0);
                        hv_Distance.Dispose();
                        hv_Distance = ExpTmpOutVar_0;
                    }
                    //显示标注
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_RowProj.Dispose(); hv_ColProj.Dispose();
                        HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_CroppedRow.TupleSelect(
                            (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
                            ), hv_CroppedCol.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
                            )) / 2) - 1)).TupleInt()), hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
                            )) / 2) - 1)).TupleInt()), 0, out hv_RowProj, out hv_ColProj);
                    }
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        ho_Contour2.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour2, hv_InterRow.TupleConcat(
                            hv_RowProj), hv_InterColumn.TupleConcat(hv_ColProj));
                    }
                    hv_OffsetDistance.Dispose();
                    hv_OffsetDistance = 5;
                    ho_ParallelContours.Dispose();
                    HOperatorSet.GenParallelContourXld(ho_Contour2, out ho_ParallelContours,
                        "regression_normal", hv_OffsetDistance);
                    hv_Row.Dispose(); hv_Col.Dispose();
                    HOperatorSet.GetContourXld(ho_ParallelContours, out hv_Row, out hv_Col);
                    hv_Length.Dispose();
                    HOperatorSet.TupleLength(hv_Row, out hv_Length);

                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_RowProj1.Dispose(); hv_ColProj1.Dispose();
                        HOperatorSet.ProjectionPl(hv_RowProj, hv_ColProj, hv_Row.TupleSelect(0),
                            hv_Col.TupleSelect(0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(1),
                            out hv_RowProj1, out hv_ColProj1);
                    }
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_RowProj2.Dispose(); hv_ColProj2.Dispose();
                        HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_Row.TupleSelect(
                            0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(
                            1), out hv_RowProj2, out hv_ColProj2);
                    }

                    
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");
                    
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        ho_Contour3.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
                            hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
                            )) / 2) - 1)).TupleInt())), hv_ColProj1.TupleConcat(hv_CroppedCol.TupleSelect(
                            (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
                            )));
                    }
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        ho_Contour4.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour4, hv_RowProj2.TupleConcat(
                            hv_InterRow), hv_ColProj2.TupleConcat(hv_InterColumn));
                    }
                    if (HDevWindowStack.IsOpen())
                    {
                        HOperatorSet.DispObj(ho_Contour3, hWindowControl1.HalconWindow);
                    }
                    if (HDevWindowStack.IsOpen())
                    {
                        HOperatorSet.DispObj(ho_Contour4, hWindowControl1.HalconWindow);
                    }
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                    m_3dfun.dev_Callout_Cad(hv_Row.TupleSelect(0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(
                            hv_Length - 1), hv_Col.TupleSelect(hv_Length - 1), (hv_Distance * 150) * 0.001,
                            hWindowControl1.HalconWindow);
                    }

                    // stop(...); only in hdevelop
                


            }
            catch (HalconException HDevExpDefaultException)
            {
                ho_Image1.Dispose();
                ho_ShapeModel.Dispose();
                ho_ImageResult.Dispose();
                ho_IntersectionX.Dispose();
                ho_ContourAngle2.Dispose();
                ho_Image.Dispose();
                ho_ImageCleared.Dispose();
                ho_Rectangle1.Dispose();
                ho_ModeCroppedContours.Dispose();
                ho_region.Dispose();
                ho_ContoursAffinTrans.Dispose();
                ho_Rectangle.Dispose();
                ho_CroppedContours.Dispose();
                ho_Contour1.Dispose();
                ho_Contour2.Dispose();
                ho_ParallelContours.Dispose();
                ho_Contour3.Dispose();
                ho_Contour4.Dispose();

                hv_ModelID1.Dispose();
                hv_SearchRow1.Dispose();
                hv_SearchColumn1.Dispose();
                hv_SearchAngle1.Dispose();
                hv_Score1.Dispose();
                hv_Files.Dispose();
                hv_i.Dispose();
                hv_ObjectModel3D.Dispose();
                hv_Status.Dispose();
                hv_WindowHandle1.Dispose();
                hv_PoseOut.Dispose();
                hv_ObjectModel3D1.Dispose();
                hv_rang_zmax.Dispose();
                hv_rang_zmin.Dispose();
                hv_rang_xmax.Dispose();
                hv_rang_xmin.Dispose();
                hv_GenParamValuez.Dispose();
                hv_GenParamValuex.Dispose();
                hv_Max_z.Dispose();
                hv_Max_x.Dispose();
                hv_Min_z.Dispose();
                hv_Min_x.Dispose();
                hv_Thresholdedz.Dispose();
                hv_Thresholdedx.Dispose();
                hv_Width.Dispose();
                hv_Height.Dispose();
                hv_WindowHandle.Dispose();
                hv_BorderFact.Dispose();
                hv_IsTelecentric.Dispose();
                hv_translate1.Dispose();
                hv_revolve1.Dispose();
                hv_GenParamValuey.Dispose();
                hv_Min_y.Dispose();
                hv_translate2.Dispose();
                hv_Pose.Dispose();
                hv_CamParam.Dispose();
                hv_Labels.Dispose();
                hv_VisParamName.Dispose();
                hv_VisParamValue.Dispose();
                hv_Title.Dispose();
                hv_cropRow1.Dispose();
                hv_cropColumn1.Dispose();
                hv_cropRow2.Dispose();
                hv_cropColumn2.Dispose();
                hv_ModelID.Dispose();
                hv_ModeRow.Dispose();
                hv_ModeColumn.Dispose();
                hv_ModeAngle.Dispose();
                hv_ModeScore.Dispose();
                hv_DrawID.Dispose();
                hv_recrow1.Dispose();
                hv_recrow2.Dispose();
                hv_reccol1.Dispose();
                hv_reccol2.Dispose();
                hv_DrawID1.Dispose();
                hv_linerow1.Dispose();
                hv_linerow2.Dispose();
                hv_linecol1.Dispose();
                hv_linecol2.Dispose();
                hv_offsetrecrow1.Dispose();
                hv_offsetrecrow2.Dispose();
                hv_offsetreccol1.Dispose();
                hv_offsetreccol2.Dispose();
                hv_offsetlinerow1.Dispose();
                hv_offsetlinerow2.Dispose();
                hv_offsetlinecol1.Dispose();
                hv_offsetlinecol2.Dispose();
                hv_SearchRow.Dispose();
                hv_SearchColumn.Dispose();
                hv_SearchAngle.Dispose();
                hv_Score.Dispose();
                hv_HomMat2DIdentity.Dispose();
                hv_HomMat2DRotate.Dispose();
                hv_HomMat2DTranslate.Dispose();
                hv_CroppedRow.Dispose();
                hv_CroppedCol.Dispose();
                hv_InterRow.Dispose();
                hv_InterColumn.Dispose();
                hv_IsOverlapping.Dispose();
                hv_Distance.Dispose();
                hv_RowProj.Dispose();
                hv_ColProj.Dispose();
                hv_OffsetDistance.Dispose();
                hv_Row.Dispose();
                hv_Col.Dispose();
                hv_Length.Dispose();
                hv_RowProj1.Dispose();
                hv_ColProj1.Dispose();
                hv_RowProj2.Dispose();
                hv_ColProj2.Dispose();

                throw HDevExpDefaultException;
            }
            ho_Image1.Dispose();
            ho_ShapeModel.Dispose();
            ho_ImageResult.Dispose();
            ho_IntersectionX.Dispose();
            ho_ContourAngle2.Dispose();
            ho_Image.Dispose();
            ho_ImageCleared.Dispose();
            ho_Rectangle1.Dispose();
            ho_ModeCroppedContours.Dispose();
            ho_region.Dispose();
            ho_ContoursAffinTrans.Dispose();
            ho_Rectangle.Dispose();
            ho_CroppedContours.Dispose();
            ho_Contour1.Dispose();
            ho_Contour2.Dispose();
            ho_ParallelContours.Dispose();
            ho_Contour3.Dispose();
            ho_Contour4.Dispose();

            hv_ModelID1.Dispose();
            hv_SearchRow1.Dispose();
            hv_SearchColumn1.Dispose();
            hv_SearchAngle1.Dispose();
            hv_Score1.Dispose();
            hv_Files.Dispose();
            hv_i.Dispose();
            hv_ObjectModel3D.Dispose();
            hv_Status.Dispose();
            hv_WindowHandle1.Dispose();
            hv_PoseOut.Dispose();
            hv_ObjectModel3D1.Dispose();
            hv_rang_zmax.Dispose();
            hv_rang_zmin.Dispose();
            hv_rang_xmax.Dispose();
            hv_rang_xmin.Dispose();
            hv_GenParamValuez.Dispose();
            hv_GenParamValuex.Dispose();
            hv_Max_z.Dispose();
            hv_Max_x.Dispose();
            hv_Min_z.Dispose();
            hv_Min_x.Dispose();
            hv_Thresholdedz.Dispose();
            hv_Thresholdedx.Dispose();
            hv_Width.Dispose();
            hv_Height.Dispose();
            hv_WindowHandle.Dispose();
            hv_BorderFact.Dispose();
            hv_IsTelecentric.Dispose();
            hv_translate1.Dispose();
            hv_revolve1.Dispose();
            hv_GenParamValuey.Dispose();
            hv_Min_y.Dispose();
            hv_translate2.Dispose();
            hv_Pose.Dispose();
            hv_CamParam.Dispose();
            hv_Labels.Dispose();
            hv_VisParamName.Dispose();
            hv_VisParamValue.Dispose();
            hv_Title.Dispose();
            hv_cropRow1.Dispose();
            hv_cropColumn1.Dispose();
            hv_cropRow2.Dispose();
            hv_cropColumn2.Dispose();
            hv_ModelID.Dispose();
            hv_ModeRow.Dispose();
            hv_ModeColumn.Dispose();
            hv_ModeAngle.Dispose();
            hv_ModeScore.Dispose();
            hv_DrawID.Dispose();
            hv_recrow1.Dispose();
            hv_recrow2.Dispose();
            hv_reccol1.Dispose();
            hv_reccol2.Dispose();
            hv_DrawID1.Dispose();
            hv_linerow1.Dispose();
            hv_linerow2.Dispose();
            hv_linecol1.Dispose();
            hv_linecol2.Dispose();
            hv_offsetrecrow1.Dispose();
            hv_offsetrecrow2.Dispose();
            hv_offsetreccol1.Dispose();
            hv_offsetreccol2.Dispose();
            hv_offsetlinerow1.Dispose();
            hv_offsetlinerow2.Dispose();
            hv_offsetlinecol1.Dispose();
            hv_offsetlinecol2.Dispose();
            hv_SearchRow.Dispose();
            hv_SearchColumn.Dispose();
            hv_SearchAngle.Dispose();
            hv_Score.Dispose();
            hv_HomMat2DIdentity.Dispose();
            hv_HomMat2DRotate.Dispose();
            hv_HomMat2DTranslate.Dispose();
            hv_CroppedRow.Dispose();
            hv_CroppedCol.Dispose();
            hv_InterRow.Dispose();
            hv_InterColumn.Dispose();
            hv_IsOverlapping.Dispose();
            hv_Distance.Dispose();
            hv_RowProj.Dispose();
            hv_ColProj.Dispose();
            hv_OffsetDistance.Dispose();
            hv_Row.Dispose();
            hv_Col.Dispose();
            hv_Length.Dispose();
            hv_RowProj1.Dispose();
            hv_ColProj1.Dispose();
            hv_RowProj2.Dispose();
            hv_ColProj2.Dispose();

        }



        //戴卡2310RANG_ZMIN = 40000
        const int RANG_ZMAX = 60000, RANG_ZMIN = -20000, RANG_XMAX = 70000, RANG_XMIN = 0;
        private void HKLaser_Load(object sender, EventArgs e)
        {
            btn_CreaTepattern.Enabled = false;
            btn_Save.Enabled = false;
            evenGetDatabasename(ref m_databasename ,ref numberofdecimal);
            connection = @"Server=" + m_databasename + ";Database=MyDB;Trusted_Connection=SSPI";

            HDevWindowStack.Push(hWindowControl1.HalconWindow);
            HDevWindowStack.SetActive(hWindowControl1.HalconWindow);
            //try
            //{
            //    m_HKVision_3D.eventProcessImage += processHImage1;
            //    m_HKVision_3D.DeviceListAcq(cbDeviceList);

            //}
            //catch
            //{

            //}
        }

  
        
        private void btn_CreaTepattern_Click(object sender, EventArgs e)
        {
            HTuple hv_cropRow1 = new HTuple(), hv_cropColumn1 = new HTuple();
            HTuple hv_cropRow2 = new HTuple(), hv_cropColumn2 = new HTuple();

            HObject ho_ImageResult = null, ho_Rectangle1 = null, ho_ModeCroppedContours = null;

            HOperatorSet.GenEmptyObj(out ho_Rectangle1);
            HOperatorSet.GenEmptyObj(out ho_ModeCroppedContours);
            try
            {
                HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");
                hWindowControl1.Focus();
                HOperatorSet.DispText(hWindowControl1.HalconWindow, "框选模板区域，右键结束", "window", "top",
                    "left", "green", "box", "false");
                hv_cropRow1.Dispose(); hv_cropColumn1.Dispose(); hv_cropRow2.Dispose(); hv_cropColumn2.Dispose();
                HOperatorSet.DrawRectangle1(hWindowControl1.HalconWindow, out hv_cropRow1, out hv_cropColumn1,
                    out hv_cropRow2, out hv_cropColumn2);
              
                ho_Rectangle1.Dispose();

                HOperatorSet.GenRectangle1(out ho_Rectangle1, hv_cropRow1, hv_cropColumn1, hv_cropRow2, hv_cropColumn2);

                ho_ModeCroppedContours.Dispose();
                HOperatorSet.CropContoursXld(ho_ContourAngle2, out ho_ModeCroppedContours,
                    hv_cropRow1, hv_cropColumn1, hv_cropRow2, hv_cropColumn2, "true");

                hv_DrawIDPattern.Dispose();
                HOperatorSet.CreateShapeModelXld(ho_ModeCroppedContours, "auto", -0.39,
                    0.79, "auto", "auto", "ignore_local_polarity", 5, out hv_DrawIDPattern);

                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);

                HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);

                hv_DrawID.Dispose();
                m_3dfun.CreateDrawingRectangle1(hWindowControl1.HalconWindow, 100, 100, 150, 120, "yellow", out hv_DrawID);
                hv_DrawID1.Dispose();
                m_3dfun.CreateDrawingLine(hWindowControl1.HalconWindow, "orange", 100, 50, 160, 50, out hv_DrawID1);

                btn_BaseLine.Enabled = false;
                btn_Save.Enabled = true;
            }
            catch
            {
                return;
            }
         


        }

      
        private void btn_Save_Click(object sender, EventArgs e)
        {
            HTuple hv_CenterRow = new HTuple();
            HTuple hv_CenterColumn = new HTuple(), hv_CenterAngle = new HTuple();
            HTuple hv_CenterScore = new HTuple();

            HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();

            HTuple hv_rowt = new HTuple(), hv_coll = new HTuple();
            HTuple hv_rowb = new HTuple(), hv_colr = new HTuple();

            HTuple hv_rec1row1 = new HTuple(), hv_rec1col1 = new HTuple();
            HTuple hv_rec1row2 = new HTuple(), hv_rec1col2 = new HTuple();

            HTuple hv_rec2rowt = new HTuple(), hv_rec2coll = new HTuple();
            HTuple hv_rec2rowb = new HTuple(), hv_rec2colr = new HTuple();

            HObject ho_region, ho_image, ho_imagecleard, ho_imageresult;

            HOperatorSet.GenEmptyObj(out ho_region);
            HOperatorSet.GenEmptyObj(out ho_image);
            HOperatorSet.GenEmptyObj(out ho_imagecleard);
            HOperatorSet.GenEmptyObj(out ho_imageresult);

            double[] rec1row1 = new double[4];
            double[] rec1col1 = new double[4];
            double[] rec1row2 = new double[4];
            double[] rec1col2 = new double[4];
            double[] rec2row1 = new double[4];
            double[] rec2col1 = new double[4];
            double[] rec2row2 = new double[4];
            double[] rec2col2 = new double[4];
            try
            {
               
                if (!string.IsNullOrEmpty(wheelmodol.Text))
                {
                    string savepath = Directory.GetCurrentDirectory() + "\\model\\lasershm\\";
                    if(!Directory.Exists(savepath))
                    {
                        Directory.CreateDirectory(savepath);
                    }
                    HOperatorSet.WriteShapeModel(hv_DrawIDPattern, savepath + wheelmodol.Text + "laser.shm");

                    //HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\lasershm\\" + typename + "circenter.shm",
                    //                                                   out hv_CenterModelID);


                    // HOperatorSet.GenRegionContourXld(ho_ContourAngle2, out ho_region, "filled");
                    HOperatorSet.GenImageConst(out ho_image, "byte", 500, 500);
                    HOperatorSet.GenImageProto(ho_image, out ho_imagecleard, 0);
                    HOperatorSet.PaintXld(ho_ContourAngle2, ho_imagecleard, out ho_imageresult, 255);
                    // HOperatorSet.PaintRegion(ho_region, ho_imagecleard, out ho_imageresult, 255, "fill");



                    HOperatorSet.FindShapeModel(ho_imageresult, hv_DrawIDPattern, 0, 0.79, 0.5, 1,
                     0.5, "least_squares", 5, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
                     out hv_CenterScore);

                    hv_row1.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row1", out hv_row1);
                    hv_row2.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row2", out hv_row2);
                    hv_col1.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column1", out hv_col1);
                    hv_col2.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column2", out hv_col2);

                    hv_row1.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row1", out hv_rowt);
                    hv_row2.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row2", out hv_rowb);
                    hv_col1.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column1", out hv_coll);
                    hv_col2.Dispose();
                    HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column2", out hv_colr);

                    hv_rec1row1 = hv_row1 - hv_CenterRow;
                    hv_rec1col1 = hv_col1 - hv_CenterColumn;
                    hv_rec1row2 = hv_row2 - hv_CenterRow;
                    hv_rec1col2 = hv_col2 - hv_CenterColumn;


                    hv_rec2rowt = hv_rowt - hv_CenterRow;
                    hv_rec2coll = hv_coll - hv_CenterColumn;
                    hv_rec2rowb = hv_rowb - hv_CenterRow;
                    hv_rec2colr = hv_colr - hv_CenterColumn;




                    //hv_rec1row1 = hv_row1;
                    //hv_rec1col1 = hv_col1;
                    //hv_rec1row2 = hv_row2;
                    //hv_rec1col2 = hv_col2;


                    //hv_rec2rowt = hv_rowt;
                    //hv_rec2coll = hv_coll;
                    //hv_rec2rowb = hv_rowb;
                    //hv_rec2colr = hv_colr;

                    rec1row1 = hv_rec1row1.ToDArr();
                    rec1col1 = hv_rec1col1.ToDArr();
                    rec1row2 = hv_rec1row2.ToDArr();
                    rec1col2 = hv_rec1col2.ToDArr();
                    rec2row1 = hv_rec2rowt.ToDArr();
                    rec2col1 = hv_rec2coll.ToDArr();
                    rec2row2 = hv_rec2rowb.ToDArr();
                    rec2col2 = hv_rec2colr.ToDArr();



                    SQlFun m_sqlfun = new SQlFun();
                    m_sqlfun.connection(connection);
                    m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        //判断是否已经存在
                        bool exist = m_sqlfun.Sql_ExistColumn("三维检测配方", "轮毂型号", wheelmodol.Text);

                        if (exist)
                        {
                            DialogResult dr = new DialogResult();
                            dr = MessageBox.Show("此轮毂已经录入，是否替换");

                            if (dr == DialogResult.OK)
                            {
                                m_sqlfun.Sql_modify("三维检测配方", "矩形1Row1", wheelmodol.Text, rec1row1[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形1Column1", wheelmodol.Text, rec1col1[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形1Row2", wheelmodol.Text, rec1row2[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形1Column2", wheelmodol.Text, rec1col2[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形2Row1", wheelmodol.Text, rec2row1[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形2Column1", wheelmodol.Text, rec2col1[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形2Row2", wheelmodol.Text, rec2row2[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "矩形2Column2", wheelmodol.Text, rec2col2[0]);
                                m_sqlfun.Sql_modify("三维检测配方", "测量点偏移", wheelmodol.Text, 0);
                            }
                        }
                        else
                        {
                            int databasemaxindex = m_sqlfun.Sql_indexmax("三维检测配方");
                            databasemaxindex++;
                            m_sqlfun.Sql_write_hatdeepprepare(databasemaxindex, wheelmodol.Text, rec1row1[0], rec1col1[0],
                             rec1row2[0], rec1col2[0], rec2row1[0], rec2col1[0], rec2row2[0], rec2col2[0], 0);
                        }

                        m_sqlfun.conn.Close();

                    }
                    else
                        MessageBox.Show("数据库未打开");
                    btn_BaseLine.Enabled = true;
                    btn_CreaTepattern.Enabled = false;
                    btn_Save.Enabled = false;
                    MessageBox.Show(wheelmodol.Text + "保存成功");

                }
                else
                    MessageBox.Show("轮毂型号为空，保存失败！");

            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败\n" + ex.Message);
            }


        }

        private void Form_Closed(object sender, FormClosedEventArgs e)
        {
           
            ho_Contour3.Dispose();
            ho_Contour4.Dispose();
            ho_ContourAngle2.Dispose();
            hv_ObjectModel3D.Dispose();
            hv_DrawIDPattern.Dispose();
            hv_DrawID.Dispose();
            hv_DrawID1.Dispose();
            hv_Row.Dispose(); hv_Col.Dispose(); hv_Distance.Dispose();


        }

        public HKLaser()
        {
            hv_ObjectModel3D = new HTuple();
            m_3dfun = new _3DFunc._3DFunc();
            hv_DrawIDPattern = new HTuple();
            hv_DrawID = new HTuple();
            hv_DrawID1 = new HTuple();

            HOperatorSet.GenEmptyObj(out ho_ContourAngle2);
            HOperatorSet.GenEmptyObj(out ho_Contour3);
            HOperatorSet.GenEmptyObj(out ho_Contour4);
            InitializeComponent();
        }
        private void hWindowControl1_HMouseMove(object sender, HMouseEventArgs e)
        {
            if (m_lock01)
            {
                x = Math.Round(e.X);
                y = Math.Round(e.Y);
            }

        }
        private bool checkpositonischange()
        {
            HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();

            HTuple hv_rowt = new HTuple(), hv_coll = new HTuple();
            HTuple hv_rowb = new HTuple(), hv_colr = new HTuple();
            hv_row1.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row1", out hv_rowt);
            hv_row2.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row2", out hv_rowb);
            hv_col1.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column1", out hv_coll);
            hv_col2.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column2", out hv_colr);

            hv_row1.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row1", out hv_row1);
            hv_row2.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row2", out hv_row2);
            hv_col1.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column1", out hv_col1);
            hv_col2.Dispose();
            HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column2", out hv_col2);

            if (Rec_Old_Row1 != hv_row1 || Rec_Old_Col1 != hv_col1 || Rec_Old_Row2 != hv_row2 || Rec_Old_Col2 != hv_col2)
                return true;


            if (Lin_Old_Row1 != hv_rowt || Lin_Old_Col1 != hv_coll || Lin_Old_Row2 != hv_rowb || Lin_Old_Col2 != hv_colr)
                return true;

            return false;

        }
        private void hWindowControl1_HMouseUp(object sender, HMouseEventArgs e)
        {
            try
            {
                if (e.Button == System.Windows.Forms.MouseButtons.Middle)
                {

                    HTuple hv_Length = new HTuple();
                    HTuple row1, col1, row2, col2, Row, Column, Button;
                    HOperatorSet.GetMposition(hWindowControl1.HalconWindow, out Row, out Column, out Button);
                    HTuple RowMove = Row - RowDown;   //鼠标弹起时的行坐标减去按下时的行坐标，得到行坐标的移动值
                    HTuple ColMove = Column - ColDown;//鼠标弹起时的列坐标减去按下时的列坐标，得到列坐标的移动值
                    HOperatorSet.GetPart(hWindowControl1.HalconWindow, out row1, out col1, out row2, out col2);//得到当前的窗口坐标
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, row1 - RowMove, col1 - ColMove, row2 - RowMove, col2 - ColMove);//这里可能有些不好理解。以左上角原点为参考点
                    HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);

                    if (ho_ContourAngle2.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                        HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);
                        HOperatorSet.DispObj(ho_Contour3, hWindowControl1.HalconWindow);
                        HOperatorSet.DispObj(ho_Contour4, hWindowControl1.HalconWindow);
                        hv_Length.Dispose();
                        HOperatorSet.TupleLength(hv_Row, out hv_Length);
                        m_3dfun.dev_Callout_Cad(hv_Row.TupleSelect(0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(
                                      hv_Length - 1), hv_Col.TupleSelect(hv_Length - 1), (((hv_Distance * 150) * 0.001)).TupleString(
            ".3"), hWindowControl1.HalconWindow);
                    }




                }

                if (m_lock01)
                {
                    HObject ho_CroppedContours, ho_Contour, ho_Contour1, ho_Contour2;
                    HObject ho_ParallelContours;

                    HOperatorSet.GenEmptyObj(out ho_CroppedContours);
                    HOperatorSet.GenEmptyObj(out ho_Contour);
                    HOperatorSet.GenEmptyObj(out ho_Contour1);
                    HOperatorSet.GenEmptyObj(out ho_Contour2);

                    HOperatorSet.GenEmptyObj(out ho_ParallelContours);

                    HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
                    HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();

                    HTuple hv_rowt = new HTuple(), hv_coll = new HTuple();
                    HTuple hv_rowb = new HTuple(), hv_colr = new HTuple();

                    HTuple hv_buttom = new HTuple();
                    HTuple hv_Distance1 = new HTuple();

                    HTuple hv_FitRowBegin = new HTuple(), hv_FitColBegin = new HTuple();
                    HTuple hv_FitRowEnd = new HTuple(), hv_FitColEnd = new HTuple();
                    HTuple hv_Nr = new HTuple(), hv_Nc = new HTuple(), hv_Dist = new HTuple();
                    HTuple hv_InterRow = new HTuple(), hv_InterColumn = new HTuple(), hv_IsOverlapping = new HTuple();
                    HTuple hv_RowProj = new HTuple();
                    HTuple hv_ColProj = new HTuple(), hv_OffsetDistance = new HTuple();
                    HTuple hv_Length = new HTuple(), hv_RowProj1 = new HTuple();
                    HTuple hv_ColProj1 = new HTuple(), hv_RowProj2 = new HTuple();
                    HTuple hv_ColProj2 = new HTuple();
                    //修改部分@1
                    HTuple hv_CroppedRow = new HTuple(), hv_CroppedCol = new HTuple();
                    try
                    {
                        hv_row1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row1", out hv_row1);
                        hv_row2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row2", out hv_row2);
                        hv_col1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column1", out hv_col1);
                        hv_col2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column2", out hv_col2);
                        hWindowControl1.Focus();

                        Rec_Old_Row1 = hv_row1;
                        Rec_Old_Col1 = hv_col1;
                        Rec_Old_Row2 = hv_row2;
                        Rec_Old_Col2 = hv_col2;

                        // HOperatorSet.GetGrayval(ho_Image, x, y, out gray);
                        //  HOperatorSet.GetMbuttonSubPix(hWindowControl1.HalconWindow, out hv_Row, out hv_Col, out hv_buttom);
                        hv_Distance.Dispose();
                        HTuple hv_x = new HTuple(), hv_y = new HTuple();
                        hv_x[0] = x;
                        hv_y[0] = y;
                        HOperatorSet.DistancePl(hv_y, hv_x, hv_row1, hv_col1, hv_row2, hv_col2, out hv_Distance1);

                        HTuple m_distance01 = new HTuple(), m_distance02 = new HTuple(), m_distance03 = new HTuple();

                        m_distance01 = (hv_x - hv_col1) * (hv_x - hv_col1) + (hv_y - hv_row1) * (hv_y - hv_row1);
                        m_distance02 = (hv_x - hv_col2) * (hv_x - hv_col2) + (hv_y - hv_row2) * (hv_y - hv_row2);
                        m_distance03 = (hv_col1 - hv_col2) * (hv_col1 - hv_col2) + (hv_row1 - hv_row2) * (hv_row1 - hv_row2);

                        if (checkpositonischange())
                            //if (m_distance01 < m_distance03 && m_distance02 < m_distance03)
                            //{
                            //    if (hv_Distance1[0] < 10 && e.Button == System.Windows.Forms.MouseButtons.Left)
                            //    {


                            HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                        HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);


                        hv_row1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row1", out hv_rowt);
                        hv_row2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row2", out hv_rowb);
                        hv_col1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column1", out hv_coll);
                        hv_col2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column2", out hv_colr);

                        ho_CroppedContours.Dispose();
                        HOperatorSet.CropContoursXld(ho_ContourAngle2, out ho_CroppedContours, hv_rowt,
                            hv_coll, hv_rowb, hv_colr, "true");
                        //修改部分@2
                        hv_CroppedRow.Dispose(); hv_CroppedCol.Dispose();
                        HOperatorSet.GetContourXld(ho_CroppedContours, out hv_CroppedRow, out hv_CroppedCol);


                        hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose(); hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose(); hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
                        //HOperatorSet.FitLineContourXld(ho_CroppedContours, "tukey", -1, 0, 5, 2, out hv_FitRowBegin,
                        //    out hv_FitColBegin, out hv_FitRowEnd, out hv_FitColEnd, out hv_Nr, out hv_Nc,
                        //    out hv_Dist);

                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "blue");

                        //ho_Contour.Dispose();
                        //HOperatorSet.GenContourPolygonXld(out ho_Contour, hv_FitRowBegin.TupleConcat(
                        //    hv_FitRowEnd), hv_FitColBegin.TupleConcat(hv_FitColEnd));

                        hv_row1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row1", out hv_row1);
                        hv_row2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row2", out hv_row2);
                        hv_col1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column1", out hv_col1);
                        hv_col2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column2", out hv_col2);

                        Lin_Old_Row1 = hv_row1;
                        Lin_Old_Col1 = hv_col1;
                        Lin_Old_Row2 = hv_row2;
                        Lin_Old_Col2 = hv_col2;



                        ho_Contour1.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_row1.TupleConcat(hv_row2),
                            hv_col1.TupleConcat(hv_col2));

                        //求竖直轴与XLD的交线
                        hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                        HOperatorSet.IntersectionContoursXld(ho_ContourAngle2, ho_Contour1, "all",
                            out hv_InterRow, out hv_InterColumn, out hv_IsOverlapping);
                        //求点到直线的距离
                        //修改部分@3
                        hv_Distance.Dispose();
                        //HOperatorSet.DistancePl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                        //    hv_FitRowEnd, hv_FitColEnd, out hv_Distance);
                        hv_Distance = hv_InterRow - (hv_CroppedRow.TupleSelect(
  (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
  ));

                        HTuple ExpTmpOutVar_0;
                        HOperatorSet.TupleAbs(hv_Distance, out ExpTmpOutVar_0);
                        hv_Distance.Dispose();
                        hv_Distance = ExpTmpOutVar_0;

                        //显示标注
                        hv_RowProj.Dispose(); hv_ColProj.Dispose();
                        //修改部分@4
                        //HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                        //    hv_FitRowEnd, hv_FitColEnd, out hv_RowProj, out hv_ColProj);
                        HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_CroppedRow.TupleSelect(
(new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
), hv_CroppedCol.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
)) / 2) - 1)).TupleInt()), hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
)) / 2) - 1)).TupleInt()), 0, out hv_RowProj, out hv_ColProj);

                        ho_Contour2.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour2, hv_InterRow.TupleConcat(
                            hv_RowProj), hv_InterColumn.TupleConcat(hv_ColProj));

                        hv_OffsetDistance.Dispose();
                        hv_OffsetDistance = 5;
                        ho_ParallelContours.Dispose();
                        HOperatorSet.GenParallelContourXld(ho_Contour2, out ho_ParallelContours, "regression_normal",
                            hv_OffsetDistance);
                        hv_Row.Dispose(); hv_Col.Dispose();
                        HOperatorSet.GetContourXld(ho_ParallelContours, out hv_Row, out hv_Col);
                        hv_Length.Dispose();
                        HOperatorSet.TupleLength(hv_Row, out hv_Length);


                        hv_RowProj1.Dispose(); hv_ColProj1.Dispose();
                        HOperatorSet.ProjectionPl(hv_RowProj, hv_ColProj, hv_Row.TupleSelect(0), hv_Col.TupleSelect(
                            0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(1), out hv_RowProj1, out hv_ColProj1);


                        hv_RowProj2.Dispose(); hv_ColProj2.Dispose();
                        HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_Row.TupleSelect(0),
                            hv_Col.TupleSelect(0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(1), out hv_RowProj2,
                            out hv_ColProj2);

                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");

                        ho_Contour3.Dispose();
                        //修改部分@5
                        //HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
                        //    hv_FitRowBegin), hv_ColProj1.TupleConcat(hv_FitColBegin));
                        HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
   hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
   )) / 2) - 1)).TupleInt())), hv_ColProj1.TupleConcat(hv_CroppedCol.TupleSelect(
   (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
   )));

                        ho_Contour4.Dispose();
                        HOperatorSet.GenContourPolygonXld(out ho_Contour4, hv_RowProj2.TupleConcat(
                            hv_InterRow), hv_ColProj2.TupleConcat(hv_InterColumn));


                        HOperatorSet.DispObj(ho_Contour3, hWindowControl1.HalconWindow);

                        HOperatorSet.DispObj(ho_Contour4, hWindowControl1.HalconWindow);

                        m_3dfun.dev_Callout_Cad(hv_Row.TupleSelect(0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(
                                hv_Length - 1), hv_Col.TupleSelect(hv_Length - 1), (((hv_Distance * 150) * 0.001)).TupleString(
    ".3"), hWindowControl1.HalconWindow);





                        //    }
                        //}
                    }
                    catch (HalconException ex)
                    {



                        ho_CroppedContours.Dispose();
                        ho_Contour.Dispose();
                        ho_Contour1.Dispose();
                        ho_Contour2.Dispose();
                        ho_ParallelContours.Dispose();


                        // hv_ObjectModel3D.Dispose();

                        hv_row1.Dispose();
                        hv_row2.Dispose();
                        hv_col1.Dispose();
                        hv_col2.Dispose();

                        hv_FitRowBegin.Dispose();
                        hv_FitColBegin.Dispose();
                        hv_FitRowEnd.Dispose();
                        hv_FitColEnd.Dispose();
                        hv_Nr.Dispose();
                        hv_Nc.Dispose();
                        hv_Dist.Dispose();

                        hv_InterRow.Dispose();
                        hv_InterColumn.Dispose();
                        hv_IsOverlapping.Dispose();

                        hv_RowProj.Dispose();
                        hv_ColProj.Dispose();
                        hv_OffsetDistance.Dispose();
                        hv_Length.Dispose();
                        hv_RowProj1.Dispose();
                        hv_ColProj1.Dispose();
                        hv_RowProj2.Dispose();
                        hv_ColProj2.Dispose();

                    }



                    ho_CroppedContours.Dispose();
                    ho_Contour.Dispose();
                    ho_Contour1.Dispose();
                    ho_Contour2.Dispose();
                    ho_ParallelContours.Dispose();


                    //  hv_ObjectModel3D.Dispose();

                    hv_row1.Dispose();
                    hv_row2.Dispose();
                    hv_col1.Dispose();
                    hv_col2.Dispose();

                    hv_FitRowBegin.Dispose();
                    hv_FitColBegin.Dispose();
                    hv_FitRowEnd.Dispose();
                    hv_FitColEnd.Dispose();
                    hv_Nr.Dispose();
                    hv_Nc.Dispose();
                    hv_Dist.Dispose();

                    hv_InterRow.Dispose();
                    hv_InterColumn.Dispose();
                    hv_IsOverlapping.Dispose();

                    hv_RowProj.Dispose();
                    hv_ColProj.Dispose();
                    hv_OffsetDistance.Dispose();
                    hv_Length.Dispose();
                    hv_RowProj1.Dispose();
                    hv_ColProj1.Dispose();
                    hv_RowProj2.Dispose();
                    hv_ColProj2.Dispose();
                }
            }
            catch
            {

            }








        }

        private void hWindowControl2_HMouseDown(object sender, HMouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Middle)
            {
                HTuple hv_k = null;
                HTuple Row, Column, Button;
                HOperatorSet.GetMposition(hWindowControl1.HalconWindow, out Row, out Column, out Button);
                RowDown = Row;    //鼠标按下时的行坐标
                ColDown = Column; //鼠标按下时的列坐标

            }
        }

        private void hWindowControl1_HMouseWheel(object sender, HMouseEventArgs e)
        {
           
            HTuple hv_row1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_col2 = new HTuple();



            HTuple hv_k = null;
            HTuple Zoom, Row, Col, Button;
            HTuple Row0, Column0, Row00, Column00, Ht, Wt, r1, c1, r2, c2;
            if (e.Delta > 0)
            {
                Zoom = 1.5;
            }
            else
            {
                Zoom = 0.5;
            }
            HOperatorSet.GetMposition(hWindowControl1.HalconWindow, out Row, out Col, out Button);
            HOperatorSet.GetPart(hWindowControl1.HalconWindow, out Row0, out Column0, out Row00, out Column00);
            Ht = Row00 - Row0;
            Wt = Column00 - Column0;
            if (Ht * Wt < 32000 * 32000 || Zoom == 1.5)//普通版halcon能处理的图像最大尺寸是32K*32K。如果无限缩小原图像，导致显示的图像超出限制，则会造成程序崩溃
            {
                r1 = (Row0 + ((1 - (1.0 / Zoom)) * (Row - Row0)));
                c1 = (Column0 + ((1 - (1.0 / Zoom)) * (Col - Column0)));
                r2 = r1 + (Ht / Zoom);
                c2 = c1 + (Wt / Zoom);
                HOperatorSet.SetPart(hWindowControl1.HalconWindow, r1, c1, r2, c2);
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                //  HOperatorSet.DispObj(ho_ImageReduced, hWindowControl1.HalconWindow);
            }
            try
            {
                if (hv_DrawID.Length != 0)
                    HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID);
                if (hv_DrawID1.Length != 0)
                    HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID1);
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);


                HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");
                HTuple hv_num = new HTuple();
                HOperatorSet.CountObj(ho_Contour3, out hv_num);
                if (hv_num != 0)
                    HOperatorSet.DispObj(ho_Contour3, hWindowControl1.HalconWindow);
                HOperatorSet.CountObj(ho_Contour4, out hv_num);
                if (hv_num != 0)
                    HOperatorSet.DispObj(ho_Contour4, hWindowControl1.HalconWindow);
                if (m_lock01)
                {


                    if (hv_DrawID.Length != 0)
                    {
                        hv_row1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row1", out hv_row1);
                        hv_row2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "row2", out hv_row2);
                        hv_col1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column1", out hv_col1);
                        hv_col2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID, "column2", out hv_col2);
                        hv_DrawID.Dispose();
                        m_3dfun.CreateDrawingRectangle1(hWindowControl1.HalconWindow, hv_row1, hv_col1, hv_row2, hv_col2, "yellow", out hv_DrawID);
                    }
                    if (hv_DrawID1.Length != 0)
                    {
                        hv_row1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row1", out hv_row1);
                        hv_row2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "row2", out hv_row2);
                        hv_col1.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column1", out hv_col1);
                        hv_col2.Dispose();
                        HOperatorSet.GetDrawingObjectParams(hv_DrawID1, "column2", out hv_col2);


                        hv_DrawID1.Dispose();
                        m_3dfun.CreateDrawingLine(hWindowControl1.HalconWindow, "orange", hv_row1, hv_col1, hv_row2, hv_col2, out hv_DrawID1);
                    }
                       
                }

                HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                HOperatorSet.CountObj(ho_ContourAngle2, out hv_num);
                if (hv_num != 0)
                    HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);


                m_3dfun.dev_Callout_Cad(hv_Row.TupleSelect(0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(
                                       1), hv_Col.TupleSelect(1), (((hv_Distance * 150) * 0.001)).TupleString(
    ".3"), hWindowControl1.HalconWindow);
            }
            catch
            {

            }




        }
        private void ReadImage_button_Click(object sender, EventArgs e)
        {
            if (hv_ObjectModel3D.Length != 0)
            {
                m_3dfun.m_stoploop = true;
                t_task.Wait();
                Thread.Sleep(100);
            }


            m_lock01 = false;
            hWindowControl1.Enabled = false;
            OpenFileDialog dialog = new OpenFileDialog();
            // Local control variables 


            HTuple hv_Status = new HTuple();
            HTuple hv_PoseOut = new HTuple();
            HTuple hv_VisParamName = new HTuple(), hv_VisParamValue = new HTuple();

            hv_Distance = new HTuple();
            hv_Row = new HTuple(); hv_Col = new HTuple();
            HOperatorSet.GenEmptyObj(out ho_ContourAngle2);
            HOperatorSet.GenEmptyObj(out ho_Contour3);
            HOperatorSet.GenEmptyObj(out ho_Contour4);

            

            string file = null;

            dialog.Multiselect = true;//该值确定是否可以选择多个文件
            dialog.Title = "请选择文件夹";
            dialog.Filter = "所有文件(*.*)|*.ply";

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                file = dialog.FileName;
            }
            if (!(file == null))
            {
                try
                {
                    hv_ObjectModel3D.Dispose();

                    HOperatorSet.ReadObjectModel3d(file, "m", new HTuple(), new HTuple(), out hv_ObjectModel3D, out hv_Status);
                    if (hv_DrawID.Length != 0)
                        HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID);
                    if (hv_DrawID1.Length != 0)
                        HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID1);

                   
                   
                   
                    HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                    t_task = Task.Factory.StartNew(() =>
                    {
                        m_3dfun.m_stoploop = false;
                        // ct.ThrowIfCancellationRequested();

                        hWindowControl1.Invoke(new MethodInvoker(delegate
                        {
                            hWindowControl1.Enabled = true;


                        }));

                        HTuple color = new HTuple();
                        HTuple pose = new HTuple(), poseout = new HTuple();
                        HOperatorSet.CreatePose(-0.02, 0.01, 200000, 90, 0, 0, "Rp+T", "gba", "point",
                                 out pose);
                        color[0] = "";
                        //lock(hv_ObjectModel3D)
                        //{
                        //m_3dfun.visualizeObject(this.hWindowControl1.HalconWindow, hv_ObjectModel3D, false, pose, color);
                        m_3dfun.visualizeObjectWithMouse(this.hWindowControl1.HalconWindow, hv_ObjectModel3D, false, color, "", ct, out poseout);
                        // hv_ObjectModel3D.Dispose();
                        //}



                    }, ct);





                }
                catch
                {

                    // hv_ObjectModel3D.Dispose();
                    hv_Status.Dispose();

                    hv_VisParamName.Dispose();
                    hv_VisParamValue.Dispose();

                    hv_PoseOut.Dispose();
                    return;
                }


                hv_Status.Dispose();

                hv_VisParamName.Dispose();
                hv_VisParamValue.Dispose();

                hv_PoseOut.Dispose();
            }
        }

        private void btn_BaseLine_Click(object sender, EventArgs e)
        {
            //CancellationTokenSource cts=new CancellationTokenSource();
            //ct = cts.Token;
            //cts.Cancel();




            HObject ho_IntersectionX;


            HTuple hv_ObjectModel3D1 = new HTuple(), hv_Width = new HTuple();
            HTuple hv_Height = new HTuple(), hv_WindowHandle = new HTuple();
            HTuple hv_BorderFact = new HTuple(), hv_IsTelecentric = new HTuple();
            HTuple hv_Pose = new HTuple(), hv_CamParam = new HTuple();
            HTuple hv_Labels = new HTuple(), hv_VisParamName = new HTuple();
            HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();
            HTuple hv_Row = new HTuple(), hv_Col = new HTuple();

            HTuple hv_rang_zmax = new HTuple();
            HTuple hv_rang_zmin = new HTuple(), hv_rang_xmax = new HTuple();
            HTuple hv_rang_xmin = new HTuple(), hv_Thresholdedz = new HTuple();
            HTuple hv_Thresholdedx = new HTuple(), hv_GenParamValuez = new HTuple();
            HTuple hv_GenParamValuex = new HTuple(), hv_Max_z = new HTuple();
            HTuple hv_Max_x = new HTuple(), hv_Min_z = new HTuple();
            HTuple hv_Min_x = new HTuple(), hv_x_dis = new HTuple();
            HTuple hv_translate1 = new HTuple();
            HTuple hv_revolve1 = new HTuple(), hv_GenParamValuey = new HTuple();
            HTuple hv_Min_y = new HTuple(), hv_translate2 = new HTuple();
            HTuple hv_z_dis = new HTuple(), hv_scale_x = new HTuple();
            HTuple hv_scale_z = new HTuple(), hv_scale = new HTuple();
            try
            {
                m_3dfun.m_stoploop = true;
                t_task.Wait();
                Thread.Sleep(100);

                HOperatorSet.GenEmptyObj(out ho_IntersectionX);

                hv_ObjectModel3D1.Dispose();

                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
               // m_3dfun.filterdata(hv_ObjectModel3D, -32768, out hv_ObjectModel3D1);
                hv_rang_zmax.Dispose();
                hv_rang_zmax = RANG_ZMAX;
                hv_rang_zmin.Dispose();
                hv_rang_zmin = RANG_ZMIN;
                hv_rang_xmax.Dispose();
                hv_rang_xmax = RANG_XMAX;
                hv_rang_xmin.Dispose();
                hv_rang_xmin = RANG_XMIN;


                hv_Thresholdedz.Dispose();
                HOperatorSet.SelectPointsObjectModel3d(hv_ObjectModel3D, "point_coord_z",
                    hv_rang_zmin, hv_rang_zmax, out hv_Thresholdedz);
                hv_Thresholdedx.Dispose();
                HOperatorSet.SelectPointsObjectModel3d(hv_Thresholdedz, "point_coord_x", hv_rang_xmin,
                    hv_rang_xmax, out hv_Thresholdedx);

                hv_GenParamValuez.Dispose();
                HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_z", out hv_GenParamValuez);
                hv_GenParamValuex.Dispose();
                HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_x", out hv_GenParamValuex);
                hv_Max_z.Dispose();
                HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                hv_Max_x.Dispose();
                HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);
                hv_Min_z.Dispose();
                HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                hv_Min_x.Dispose();
                HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);

                //HTuple
                //  ExpTmpLocalVar_GenParamValuez = hv_GenParamValuez - hv_Min_z;
                //hv_GenParamValuez.Dispose();
                //hv_GenParamValuez = ExpTmpLocalVar_GenParamValuez;


                //HTuple
                //  ExpTmpLocalVar_GenParamValuex = hv_GenParamValuex - hv_Min_x;
                //hv_GenParamValuex.Dispose();
                //hv_GenParamValuex = ExpTmpLocalVar_GenParamValuex;
                /*此段用于测试比例用*/
                hv_x_dis.Dispose();
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_x_dis = hv_Max_x - hv_Min_x;
                }
                hv_z_dis.Dispose();
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_z_dis = hv_Max_z - hv_Min_z;
                }
                hv_scale_x.Dispose();
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_scale_x = hv_x_dis / 500;
                }
                hv_scale_z.Dispose();
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_scale_z = hv_z_dis / 500;
                }
                if ((int)(new HTuple(hv_scale_x.TupleGreater(hv_scale_z))) != 0)
                {
                    hv_scale.Dispose();
                    hv_scale = new HTuple(hv_scale_x);
                }
                else
                {
                    hv_scale.Dispose();
                    hv_scale = new HTuple(hv_scale_z);
                }

                //////////////////////////////
                hv_translate1.Dispose();
                HOperatorSet.RigidTransObjectModel3d(hv_Thresholdedx, ((((((-hv_Min_x)).TupleConcat(
                    0))).TupleConcat(-hv_Min_z))).TupleConcat((((new HTuple(0)).TupleConcat(
                    0)).TupleConcat(0)).TupleConcat(0)), out hv_translate1);

                hv_revolve1.Dispose();
                HOperatorSet.RigidTransObjectModel3d(hv_translate1, ((((((new HTuple(0)).TupleConcat(
                    0)).TupleConcat(0)).TupleConcat(90)).TupleConcat(0)).TupleConcat(0)).TupleConcat(
                    0), out hv_revolve1);
                hv_GenParamValuey.Dispose();
                HOperatorSet.GetObjectModel3dParams(hv_revolve1, "point_coord_y", out hv_GenParamValuey);
                hv_Min_y.Dispose();
                HOperatorSet.TupleMin(hv_GenParamValuey, out hv_Min_y);

                hv_translate2.Dispose();
                HOperatorSet.RigidTransObjectModel3d(hv_revolve1, (((new HTuple(0)).TupleConcat(
                    -hv_Min_y))).TupleConcat(((((new HTuple(0)).TupleConcat(0)).TupleConcat(
                    0)).TupleConcat(0)).TupleConcat(0)), out hv_translate2);


                //定义虚拟相机分辨率.
                //hv_Width.Dispose();
                //hv_Width = 500;
                //hv_Height.Dispose();
                //hv_Height = 500;
                //if (HDevWindowStack.IsOpen())
                //{
                //    HOperatorSet.CloseWindow(HDevWindowStack.Pop());
                //}
                //HOperatorSet.SetWindowAttr("background_color", "black");
                //HOperatorSet.OpenWindow(0, 0, hv_Width, hv_Height, 0, "visible", "", out hv_WindowHandle);
                //HDevWindowStack.Push(hv_WindowHandle);

                //定义一个系数用于在3D模型中计算图像的边缘
                hv_BorderFact.Dispose();
                hv_BorderFact = 1.5;
                //是否选择镜头.
                hv_IsTelecentric.Dispose();
                hv_IsTelecentric = 1;

                //定义相机参数以及相机初始位姿.

                hv_Pose.Dispose();
                HOperatorSet.CreatePose(0, 0, 0, 0, 0, 0, "Rp+T", "gba", "point", out hv_Pose);
                hv_CamParam.Dispose();
                hv_CamParam = new HTuple();
                hv_CamParam[0] = 0;
                hv_CamParam[1] = 0;
                hv_CamParam[2] = UM_PIX;
                hv_CamParam[3] = UM_PIX;
                hv_CamParam[4] = 0;
                hv_CamParam[5] = 0;
                hv_CamParam[6] = 500;
                hv_CamParam[7] = 500;
                ho_IntersectionX.Dispose();
                HOperatorSet.ProjectObjectModel3d(out ho_IntersectionX, hv_translate2,
                    hv_CamParam, hv_Pose, (((new HTuple("data")).TupleConcat("point_shape")).TupleConcat(
                    "point_size")).TupleConcat("union_adjacent_contours"), (((new HTuple("points")).TupleConcat(
                    "cross")).TupleConcat(1)).TupleConcat("true"));

                ho_ContourAngle2.Dispose();
                m_3dfun.PointTransXld(ho_IntersectionX, out ho_ContourAngle2);
                HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, 500, 500);
                HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);


                btn_CreaTepattern.Enabled =true;
              
                m_lock01 = true;

                hv_WindowHandle.Dispose();
                hv_Width.Dispose();
                hv_Height.Dispose();

                hv_BorderFact.Dispose();
                hv_IsTelecentric.Dispose();

                hv_row1.Dispose();
                hv_row2.Dispose();
                hv_col1.Dispose();
                hv_col2.Dispose();
                hv_Row.Dispose();
                hv_Col.Dispose();
                hv_Labels.Dispose();
                hv_VisParamName.Dispose();

               // hv_ObjectModel3D.Dispose();
                hv_ObjectModel3D1.Dispose();
                hv_rang_zmax.Dispose();
                hv_rang_zmin.Dispose();
                hv_rang_xmax.Dispose();
                hv_rang_xmin.Dispose();
                hv_Thresholdedz.Dispose();
                hv_Thresholdedx.Dispose();
                hv_GenParamValuez.Dispose();
                hv_GenParamValuex.Dispose();
                hv_Max_z.Dispose();
                hv_Max_x.Dispose();
                hv_Min_z.Dispose();
                hv_Min_x.Dispose();
                hv_x_dis.Dispose();
                hv_z_dis.Dispose();
                hv_scale_x.Dispose();
                hv_scale_z.Dispose();
                hv_scale.Dispose();



            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                hv_WindowHandle.Dispose();
                hv_Width.Dispose();
                hv_Height.Dispose();

                hv_BorderFact.Dispose();
                hv_IsTelecentric.Dispose();

                hv_row1.Dispose();
                hv_row2.Dispose();
                hv_col1.Dispose();
                hv_col2.Dispose();
                hv_Row.Dispose();
                hv_Col.Dispose();
                hv_Labels.Dispose();
                hv_VisParamName.Dispose();

               // hv_ObjectModel3D.Dispose();
                hv_ObjectModel3D1.Dispose();
                hv_rang_zmax.Dispose();
                hv_rang_zmin.Dispose();
                hv_rang_xmax.Dispose();
                hv_rang_xmin.Dispose();
                hv_Thresholdedz.Dispose();
                hv_Thresholdedx.Dispose();
                hv_GenParamValuez.Dispose();
                hv_GenParamValuex.Dispose();
                hv_Max_z.Dispose();
                hv_Max_x.Dispose();
                hv_Min_z.Dispose();
                hv_Min_x.Dispose();
                hv_x_dis.Dispose();
                hv_z_dis.Dispose();
                hv_scale_x.Dispose();
                hv_scale_z.Dispose();
                hv_scale.Dispose();
            }



        }

        private void btn_Test_Click(object sender, EventArgs e)
        {
            string m_savepath = Directory.GetCurrentDirectory() + "\\model\\lasershm\\" + wheelmodol.Text + "laser.shm";


            HObject ho_IntersectionX;
            HObject ho_region, ho_image, ho_imagecleard, ho_imageresult, ho_rec1;

            HObject ho_CroppedContours, ho_Contour, ho_Contour1, ho_Contour2;
            HObject ho_ParallelContours;

            HOperatorSet.GenEmptyObj(out ho_CroppedContours);
            HOperatorSet.GenEmptyObj(out ho_Contour);
            HOperatorSet.GenEmptyObj(out ho_Contour1);
            HOperatorSet.GenEmptyObj(out ho_Contour2);

            HOperatorSet.GenEmptyObj(out ho_ParallelContours);
            HOperatorSet.GenEmptyObj(out ho_rec1);
            HOperatorSet.GenEmptyObj(out ho_IntersectionX);
            HOperatorSet.GenEmptyObj(out ho_region);
            HOperatorSet.GenEmptyObj(out ho_image);
            HOperatorSet.GenEmptyObj(out ho_imagecleard);
            HOperatorSet.GenEmptyObj(out ho_imageresult);

            

            HTuple hv_ObjectModel3D1 = new HTuple(), hv_Width = new HTuple();
            HTuple hv_Height = new HTuple(), hv_WindowHandle = new HTuple();
            HTuple hv_BorderFact = new HTuple(), hv_IsTelecentric = new HTuple();
            HTuple hv_Pose = new HTuple(), hv_CamParam = new HTuple();
            HTuple hv_Labels = new HTuple(), hv_VisParamName = new HTuple();
            HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();

            HTuple hv_rowtran1 = new HTuple(), hv_coltran1 = new HTuple();
            HTuple hv_rowtran2 = new HTuple(), hv_coltran2 = new HTuple();

                       
            HTuple hv_rowt = new HTuple(), hv_coll = new HTuple();
            HTuple hv_rowb = new HTuple(), hv_colr = new HTuple();

            HTuple hv_rowtrant = new HTuple(), hv_coltranl = new HTuple();
            HTuple hv_rowtranb = new HTuple(), hv_coltranr = new HTuple();

            HTuple hv_ModelID3 = new HTuple(), hv_CenterRow = new HTuple();
            HTuple hv_CenterColumn = new HTuple(), hv_CenterAngle = new HTuple();
            HTuple hv_CenterScore = new HTuple();

            HTuple hv_homMat2DIdentity = new HTuple();
            HTuple hv_homMat2DRotate = new HTuple();
            HTuple hv_HomMat2DTranslate = new HTuple();


            HTuple hv_FitRowBegin = new HTuple(), hv_FitColBegin = new HTuple();
            HTuple hv_FitRowEnd = new HTuple(), hv_FitColEnd = new HTuple();
            HTuple hv_Nr = new HTuple(), hv_Nc = new HTuple(), hv_Dist = new HTuple();
            HTuple hv_InterRow = new HTuple(), hv_InterColumn = new HTuple(), hv_IsOverlapping = new HTuple();
            HTuple hv_RowProj = new HTuple();
            HTuple hv_ColProj = new HTuple(), hv_OffsetDistance = new HTuple();
            HTuple hv_Length = new HTuple(), hv_RowProj1 = new HTuple();
            HTuple hv_ColProj1 = new HTuple(), hv_RowProj2 = new HTuple();
            HTuple hv_ColProj2 = new HTuple();

            HTuple hv_rang_zmax = new HTuple();
            HTuple hv_rang_zmin = new HTuple(), hv_rang_xmax = new HTuple();
            HTuple hv_rang_xmin = new HTuple(), hv_Thresholdedz = new HTuple();
            HTuple hv_Thresholdedx = new HTuple(), hv_GenParamValuez = new HTuple();
            HTuple hv_GenParamValuex = new HTuple(), hv_Max_z = new HTuple();
            HTuple hv_Max_x = new HTuple(), hv_Min_z = new HTuple();
            HTuple hv_Min_x = new HTuple(), hv_x_dis = new HTuple();
            HTuple hv_translate1 = new HTuple();
            HTuple hv_revolve1 = new HTuple(), hv_GenParamValuey = new HTuple();
            HTuple hv_Min_y = new HTuple(), hv_translate2 = new HTuple();
            HTuple hv_z_dis = new HTuple(), hv_scale_x = new HTuple();
            HTuple hv_scale_z = new HTuple(), hv_scale = new HTuple();

            //修改部分@1
            HTuple hv_CroppedRow = new HTuple(), hv_CroppedCol = new HTuple();



           

            double[] m_parameter = new double[10];

            SQlFun m_sqlfun = new SQlFun();

            try
            {
                m_lock01 = false;

                m_3dfun.m_stoploop = true;
                t_task.Wait();
                Thread.Sleep(100);

                if (hv_DrawID.Length != 0)
                    HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID);
                if (hv_DrawID1.Length != 0)
                    HOperatorSet.DetachDrawingObjectFromWindow(hWindowControl1.HalconWindow, hv_DrawID1);
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);


                hv_ObjectModel3D1.Dispose();
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                if (hv_ObjectModel3D.Length != 0)
                {
                 //   m_3dfun.filterdata(hv_ObjectModel3D, -32768, out hv_ObjectModel3D1);
                    
                    hv_rang_zmax.Dispose();
                    hv_rang_zmax = RANG_ZMAX;
                    hv_rang_zmin.Dispose();
                    hv_rang_zmin = RANG_ZMIN;
                    hv_rang_xmax.Dispose();
                    hv_rang_xmax = RANG_XMAX;
                    hv_rang_xmin.Dispose();
                    hv_rang_xmin = RANG_XMIN;
                    ////
                    //hv_GenParamValuez.Dispose();
                    //HOperatorSet.GetObjectModel3dParams(hv_ObjectModel3D1, "point_coord_z", out hv_GenParamValuez);
                    //hv_GenParamValuex.Dispose();
                    //HOperatorSet.GetObjectModel3dParams(hv_ObjectModel3D1, "point_coord_x", out hv_GenParamValuex);
                    //hv_Max_z.Dispose();
                    //HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                    //// MessageBox.Show(hv_Max_z.ToString());
                    //hv_Max_x.Dispose();
                    //HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);
                    ////  MessageBox.Show(hv_Max_x.ToString());
                    //hv_Min_z.Dispose();
                    //HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                    //hv_Min_x.Dispose();
                    //HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);
                    /////////////////////////////////
                    hv_Thresholdedz.Dispose();
                    HTuple num = new HTuple();

                    HOperatorSet.SelectPointsObjectModel3d(hv_ObjectModel3D, "point_coord_z",
                        hv_rang_zmin, hv_rang_zmax, out hv_Thresholdedz);

                    HOperatorSet.SelectPointsObjectModel3d(hv_Thresholdedz, "point_coord_x", hv_rang_xmin,
                        hv_rang_xmax, out hv_Thresholdedx);

                    //HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "num_points", out num);
                    //MessageBox.Show(num.ToString());

                    hv_GenParamValuez.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_z", out hv_GenParamValuez);
                    hv_GenParamValuex.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_x", out hv_GenParamValuex);
                    hv_Max_z.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                    hv_Max_x.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);
                    hv_Min_z.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                    hv_Min_x.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);

                    HTuple
                      ExpTmpLocalVar_GenParamValuez = hv_GenParamValuez - hv_Min_z;
                    hv_GenParamValuez.Dispose();
                    hv_GenParamValuez = ExpTmpLocalVar_GenParamValuez;


                    HTuple
                      ExpTmpLocalVar_GenParamValuex = hv_GenParamValuex - hv_Min_x;
                    hv_GenParamValuex.Dispose();
                    hv_GenParamValuex = ExpTmpLocalVar_GenParamValuex;



                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_translate1.Dispose();
                        HOperatorSet.RigidTransObjectModel3d(hv_Thresholdedx, ((((((-hv_Min_x)).TupleConcat(
                            0))).TupleConcat(-hv_Min_z))).TupleConcat((((new HTuple(0)).TupleConcat(
                            0)).TupleConcat(0)).TupleConcat(0)), out hv_translate1);
                    }
                    hv_revolve1.Dispose();
                    HOperatorSet.RigidTransObjectModel3d(hv_translate1, ((((((new HTuple(0)).TupleConcat(
                        0)).TupleConcat(0)).TupleConcat(90)).TupleConcat(0)).TupleConcat(0)).TupleConcat(
                        0), out hv_revolve1);
                    hv_GenParamValuey.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_revolve1, "point_coord_y", out hv_GenParamValuey);
                    hv_Min_y.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuey, out hv_Min_y);
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_translate2.Dispose();
                        HOperatorSet.RigidTransObjectModel3d(hv_revolve1, (((new HTuple(0)).TupleConcat(
                            -hv_Min_y))).TupleConcat(((((new HTuple(0)).TupleConcat(0)).TupleConcat(
                            0)).TupleConcat(0)).TupleConcat(0)), out hv_translate2);
                    }

                    //定义一个系数用于在3D模型中计算图像的边缘
                    hv_BorderFact.Dispose();
                    hv_BorderFact = 1.5;
                    //是否选择镜头.
                    hv_IsTelecentric.Dispose();
                    hv_IsTelecentric = 1;

                    //定义相机参数以及相机初始位姿.

                    hv_Pose.Dispose();
                    HOperatorSet.CreatePose(0, 0, 0, 0, 0, 0, "Rp+T", "gba", "point", out hv_Pose);
                    hv_CamParam.Dispose();
                    hv_CamParam = new HTuple();
                    hv_CamParam[0] = 0;
                    hv_CamParam[1] = 0;
                    hv_CamParam[2] = UM_PIX;
                    hv_CamParam[3] = UM_PIX;
                    hv_CamParam[4] = 0;
                    hv_CamParam[5] = 0;
                    hv_CamParam[6] = 500;
                    hv_CamParam[7] = 500;

                    ho_IntersectionX.Dispose();
                    HOperatorSet.ProjectObjectModel3d(out ho_IntersectionX, hv_translate2, hv_CamParam,
                        hv_Pose, (((new HTuple("data")).TupleConcat("point_shape")).TupleConcat(
                        "point_size")).TupleConcat("union_adjacent_contours"), (((new HTuple("auto")).TupleConcat(
                        "circle")).TupleConcat(1)).TupleConcat("true"));

                    ho_ContourAngle2.Dispose();
                    m_3dfun.PointTransXld(ho_IntersectionX, out ho_ContourAngle2);
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, 500, 500);
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                    HOperatorSet.DispObj(ho_ContourAngle2, hWindowControl1.HalconWindow);

                  //  HOperatorSet.GenRegionContourXld(ho_ContourAngle2, out ho_region, "filled");
                    HOperatorSet.GenImageConst(out ho_image, "byte", 500, 500);
                    HOperatorSet.GenImageProto(ho_image, out ho_imagecleard, 0);
                    HOperatorSet.PaintXld(ho_ContourAngle2, ho_imagecleard, out ho_imageresult, 255);
                  //  HOperatorSet.PaintRegion(ho_region, ho_imagecleard, out ho_imageresult, 255, "fill");
                  // HOperatorSet.WriteImage(ho_imageresult, "bmp", 0, "C://Users//hainajinghua//Desktop//测试精度//1");



                   if (File.Exists(m_savepath))
                       HOperatorSet.ReadShapeModel(m_savepath, out hv_ModelID3);
                       HOperatorSet.FindShapeModel(ho_imageresult, hv_ModelID3, 0, (new HTuple(180)).TupleRad()
            , 0.5, 1, 0.5, "least_squares", 8, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
                     out hv_CenterScore);

                    hwindows.hwindows m_halconwindow=new hwindows.hwindows();
                    m_halconwindow.Translate(hv_CenterAngle, hv_CenterRow, hv_CenterColumn, out hv_HomMat2DTranslate);
                   

                    m_sqlfun.connection(connection);
                    m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        bool exist = m_sqlfun.Sql_ExistColumn("三维检测配方", "轮毂型号", wheelmodol.Text);

                        if (exist)
                        {
                            m_sqlfun.Sql_Find("矩形1Row1", "三维检测配方", wheelmodol.Text, out m_parameter[0]);
                            m_sqlfun.Sql_Find("矩形1Column1", "三维检测配方", wheelmodol.Text, out m_parameter[1]);
                            m_sqlfun.Sql_Find("矩形1Row2", "三维检测配方", wheelmodol.Text, out m_parameter[2]);
                            m_sqlfun.Sql_Find("矩形1Column2", "三维检测配方", wheelmodol.Text, out m_parameter[3]);
                            m_sqlfun.Sql_Find("矩形2Row1", "三维检测配方", wheelmodol.Text, out m_parameter[4]);
                            m_sqlfun.Sql_Find("矩形2Column1", "三维检测配方", wheelmodol.Text, out m_parameter[5]);
                            m_sqlfun.Sql_Find("矩形2Row2", "三维检测配方", wheelmodol.Text, out m_parameter[6]);
                            m_sqlfun.Sql_Find("矩形2Column2", "三维检测配方", wheelmodol.Text, out m_parameter[7]);
                            m_sqlfun.Sql_Find("测量点偏移", "三维检测配方", wheelmodol.Text, out m_parameter[8]);

                            m_sqlfun.conn.Close();

                            //直线用参数
                            hv_row1[0] = m_parameter[0];
                            hv_col1[0] = m_parameter[1];
                            hv_row2[0] = m_parameter[2];
                            hv_col2[0] = m_parameter[3];
                            //矩形用参数
                            hv_rowt[0] = m_parameter[4];
                            hv_coll[0] = m_parameter[5];
                            hv_rowb[0] = m_parameter[6];
                            hv_colr[0] = m_parameter[7];

                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_row1, hv_col1, out hv_rowtran1, out hv_coltran1);
                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_row2, hv_col2, out hv_rowtran2, out hv_coltran2);

                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_rowt, hv_coll, out hv_rowtrant, out hv_coltranl);
                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_rowb, hv_colr, out hv_rowtranb, out hv_coltranr);

                            HOperatorSet.GenRectangle1(out ho_rec1, hv_rowtrant, hv_coltranl, hv_rowtranb, hv_coltranr);



                            HOperatorSet.SetDraw(hWindowControl1.HalconWindow, "margin");
                            HOperatorSet.SetColor(hWindowControl1.HalconWindow, "yellow");
                            HOperatorSet.DispObj(ho_rec1, hWindowControl1.HalconWindow);
                            ho_CroppedContours.Dispose();
                            HOperatorSet.CropContoursXld(ho_ContourAngle2, out ho_CroppedContours, hv_rowtrant, hv_coltranl, hv_rowtranb, hv_coltranr, "true");
                            //   HOperatorSet.CropContoursXld(ho_ContourAngle2, out ho_CroppedContours, hv_rowt, hv_coll, hv_rowb, hv_colr, "true");
                            //修改部分@2
                            hv_CroppedRow.Dispose(); hv_CroppedCol.Dispose();
                            HOperatorSet.GetContourXld(ho_CroppedContours, out hv_CroppedRow, out hv_CroppedCol);

                            hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose(); hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose(); hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
                           

                            HOperatorSet.SetColor(hWindowControl1.HalconWindow, "blue");

                            ho_Contour1.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_rowtran1.TupleConcat(hv_rowtran2),
                                hv_coltran1.TupleConcat(hv_coltran2));
                            //HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_row1.TupleConcat(hv_row2),
                            //   hv_col1.TupleConcat(hv_col2));

                            HOperatorSet.DispObj(ho_Contour1, hWindowControl1.HalconWindow);

                            //求竖直轴与XLD的交线
                            hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                            HOperatorSet.IntersectionContoursXld(ho_ContourAngle2, ho_Contour1, "all",
                                out hv_InterRow, out hv_InterColumn, out hv_IsOverlapping);
                            //求点到直线的距离
                            //修改部分@3
                            hv_Distance.Dispose();
                            //HOperatorSet.DistancePl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                            //    hv_FitRowEnd, hv_FitColEnd, out hv_Distance);
                            hv_Distance = hv_InterRow - (hv_CroppedRow.TupleSelect(
      (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
      ));
                        }
                        {
                            HTuple ExpTmpOutVar_0;
                            HOperatorSet.TupleAbs(hv_Distance, out ExpTmpOutVar_0);
                            hv_Distance.Dispose();
                            hv_Distance = ExpTmpOutVar_0;


                            //显示标注
                            hv_RowProj.Dispose(); hv_ColProj.Dispose();

                            //修改部分@4
                            //HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                            //    hv_FitRowEnd, hv_FitColEnd, out hv_RowProj, out hv_ColProj);
                            HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_CroppedRow.TupleSelect(
 (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
 ), hv_CroppedCol.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
 )) / 2) - 1)).TupleInt()), hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
 )) / 2) - 1)).TupleInt()), 0, out hv_RowProj, out hv_ColProj);


                            ho_Contour2.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contour2, hv_InterRow.TupleConcat(
                                hv_RowProj), hv_InterColumn.TupleConcat(hv_ColProj));

                            hv_OffsetDistance.Dispose();
                            hv_OffsetDistance = 5;
                            ho_ParallelContours.Dispose();
                            HOperatorSet.GenParallelContourXld(ho_Contour2, out ho_ParallelContours, "regression_normal",
                                hv_OffsetDistance);
                            hv_Row.Dispose(); hv_Col.Dispose();
                            HOperatorSet.GetContourXld(ho_ParallelContours, out hv_Row, out hv_Col);
                            hv_Length.Dispose();
                            HOperatorSet.TupleLength(hv_Row, out hv_Length);


                            hv_RowProj1.Dispose(); hv_ColProj1.Dispose();
                            HOperatorSet.ProjectionPl(hv_RowProj, hv_ColProj, hv_Row.TupleSelect(0), hv_Col.TupleSelect(
                                0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(1), out hv_RowProj1, out hv_ColProj1);


                            hv_RowProj2.Dispose(); hv_ColProj2.Dispose();
                            HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_Row.TupleSelect(0),
                                hv_Col.TupleSelect(0), hv_Row.TupleSelect(1), hv_Col.TupleSelect(1), out hv_RowProj2,
                                out hv_ColProj2);

                            HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");

                            ho_Contour3.Dispose();
                            //修改部分@5
                            //HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
                            //    hv_FitRowBegin), hv_ColProj1.TupleConcat(hv_FitColBegin));
                            HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
       hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
       )) / 2) - 1)).TupleInt())), hv_ColProj1.TupleConcat(hv_CroppedCol.TupleSelect(
       (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
       )));


                            ho_Contour4.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contour4, hv_RowProj2.TupleConcat(
                                hv_InterRow), hv_ColProj2.TupleConcat(hv_InterColumn));


                            HOperatorSet.DispObj(ho_Contour3, hWindowControl1.HalconWindow);

                            HOperatorSet.DispObj(ho_Contour4, hWindowControl1.HalconWindow);
                            //(((hv_Distance * 150) * 0.001)).TupleString( ".3")

                            m_3dfun.dev_Callout_Cad(hv_Row.TupleSelect(0), hv_Col.TupleSelect(0), hv_Row.TupleSelect(
                                    hv_Length - 1), hv_Col.TupleSelect(hv_Length - 1), (hv_Distance * 150) * 0.001, hWindowControl1.HalconWindow);

                            


                        }


                    }
                }


            }
            catch (Exception ex)

            {
                MessageBox.Show(ex.ToString());
                ho_IntersectionX.Dispose();
                ho_region.Dispose(); ho_image.Dispose(); ho_imagecleard.Dispose(); ho_imageresult.Dispose(); ho_rec1.Dispose();

                ho_CroppedContours.Dispose(); ho_Contour.Dispose(); ho_Contour1.Dispose(); ho_Contour2.Dispose();
                ho_ParallelContours.Dispose();

                hv_ObjectModel3D1.Dispose(); hv_Width.Dispose();
                hv_Height.Dispose(); hv_WindowHandle.Dispose();
                hv_BorderFact.Dispose(); hv_IsTelecentric.Dispose();
                hv_Pose.Dispose(); hv_CamParam.Dispose();
                hv_Labels.Dispose(); hv_VisParamName.Dispose();
                hv_row1.Dispose(); hv_col1.Dispose();
                hv_row2.Dispose(); hv_col2.Dispose();

                hv_rowtran1.Dispose(); hv_coltran1.Dispose();
                hv_rowtran2.Dispose(); hv_coltran2.Dispose();


                hv_rowt.Dispose(); hv_coll.Dispose();
                hv_rowb.Dispose(); hv_colr.Dispose();

                hv_rowtrant.Dispose(); hv_coltranl.Dispose();
                hv_rowtranb.Dispose(); hv_coltranr.Dispose();

                hv_ModelID3.Dispose(); hv_CenterRow.Dispose();
                hv_CenterColumn.Dispose(); hv_CenterAngle.Dispose();
                hv_CenterScore.Dispose();

                hv_homMat2DIdentity.Dispose();
                hv_homMat2DRotate.Dispose();
                hv_HomMat2DTranslate.Dispose();


                hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose();
                hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose();
                hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
                hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                hv_RowProj.Dispose();
                hv_ColProj.Dispose(); hv_OffsetDistance.Dispose();
                hv_Length.Dispose(); hv_RowProj1.Dispose();
                hv_ColProj1.Dispose(); hv_RowProj2.Dispose();
                hv_ColProj2.Dispose();
                MessageBox.Show("检测失败");

            }
            ho_IntersectionX.Dispose();
            ho_region.Dispose(); ho_image.Dispose(); ho_imagecleard.Dispose(); ho_imageresult.Dispose(); ho_rec1.Dispose();

            ho_CroppedContours.Dispose(); ho_Contour.Dispose(); ho_Contour1.Dispose(); ho_Contour2.Dispose();
            ho_ParallelContours.Dispose();

            hv_ObjectModel3D1.Dispose(); hv_Width.Dispose();
            hv_Height.Dispose(); hv_WindowHandle.Dispose();
            hv_BorderFact.Dispose(); hv_IsTelecentric.Dispose();
            hv_Pose.Dispose(); hv_CamParam.Dispose();
            hv_Labels.Dispose(); hv_VisParamName.Dispose();
            hv_row1.Dispose(); hv_col1.Dispose();
            hv_row2.Dispose(); hv_col2.Dispose();

            hv_rowtran1.Dispose(); hv_coltran1.Dispose();
            hv_rowtran2.Dispose(); hv_coltran2.Dispose();






            hv_rowt.Dispose(); hv_coll.Dispose();
            hv_rowb.Dispose(); hv_colr.Dispose();

            hv_rowtrant.Dispose(); hv_coltranl.Dispose();
            hv_rowtranb.Dispose(); hv_coltranr.Dispose();

            hv_ModelID3.Dispose(); hv_CenterRow.Dispose();
            hv_CenterColumn.Dispose(); hv_CenterAngle.Dispose();
            hv_CenterScore.Dispose();

            hv_homMat2DIdentity.Dispose();
            hv_homMat2DRotate.Dispose();
            hv_HomMat2DTranslate.Dispose();


            hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose();
            hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose();
            hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
            hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
            hv_RowProj.Dispose();
            hv_ColProj.Dispose(); hv_OffsetDistance.Dispose();
            hv_Length.Dispose(); hv_RowProj1.Dispose();
            hv_ColProj1.Dispose(); hv_RowProj2.Dispose();
            hv_ColProj2.Dispose();



        }
    }
}
