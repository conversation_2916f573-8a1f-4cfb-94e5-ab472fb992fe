using System;
using System.Collections.Generic;
using System.Drawing;
using CameraTechVerify.Interfaces;
using CameraTechVerify.Models;
using CameraTechVerify.Factories;

namespace CameraTechVerify.Examples
{
    /// <summary>
    /// 视觉算法引擎使用示例
    /// </summary>
    public class VisionEngineUsageExample
    {
        private IVisionAlgorithmEngine _engine;

        public VisionEngineUsageExample()
        {
            // 创建 Halcon 引擎
            var config = VisionEngineFactory.CreateDefaultConfig(VisionEngineFactory.EngineType.Halcon);
            _engine = VisionEngineFactory.CreateEngine(VisionEngineFactory.EngineType.Halcon, config);
            
            // 订阅事件
            _engine.ProgressChanged += OnProgressChanged;
            _engine.AlgorithmCompleted += OnAlgorithmCompleted;
            _engine.ErrorOccurred += OnErrorOccurred;
        }

        /// <summary>
        /// 基础图像处理示例
        /// </summary>
        public void BasicImageProcessingExample()
        {
            try
            {
                // 1. 加载图像
                var image = _engine.LoadImage(@"C:\test\image.bmp");
                if (image == null)
                {
                    Console.WriteLine("Failed to load image");
                    return;
                }

                // 2. 图像阈值分割
                var region = _engine.Threshold(image, 100, 200);
                if (region != null)
                {
                    Console.WriteLine($"Region area: {region.Area}");
                    Console.WriteLine($"Region centroid: ({region.Centroid.X}, {region.Centroid.Y})");
                }

                // 3. 边缘检测
                var edges = _engine.EdgeDetection(image, EdgeDetectionMethod.Canny, 
                    new Dictionary<string, object>
                    {
                        ["alpha"] = 1.0,
                        ["low"] = 20.0,
                        ["high"] = 40.0
                    });

                if (edges != null)
                {
                    Console.WriteLine($"Edge contour length: {edges.Length}");
                    Console.WriteLine($"Number of edge points: {edges.Points.Count}");
                }

                // 4. 形态学操作
                if (region != null)
                {
                    var openedRegion = _engine.MorphologyOperation(region, 
                        MorphologyOperation.Opening, StructuringElement.Circle);
                    
                    if (openedRegion != null)
                    {
                        Console.WriteLine($"Opened region area: {openedRegion.Area}");
                    }
                }

                // 清理资源
                image?.Dispose();
                region?.Dispose();
                edges?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in basic image processing: {ex.Message}");
            }
        }

        /// <summary>
        /// 模板匹配示例
        /// </summary>
        public void TemplateMatchingExample()
        {
            try
            {
                // 1. 加载模板图像
                var templateImage = _engine.LoadImage(@"C:\test\template.bmp");
                if (templateImage == null)
                {
                    Console.WriteLine("Failed to load template image");
                    return;
                }

                // 2. 创建形状模板
                var modelId = _engine.CreateShapeModel(templateImage, null, 
                    new Dictionary<string, object>
                    {
                        ["angleStart"] = -Math.PI / 4,
                        ["angleExtent"] = Math.PI / 2,
                        ["minContrast"] = 30.0
                    });

                if (string.IsNullOrEmpty(modelId))
                {
                    Console.WriteLine("Failed to create shape model");
                    return;
                }

                // 3. 加载搜索图像
                var searchImage = _engine.LoadImage(@"C:\test\search.bmp");
                if (searchImage == null)
                {
                    Console.WriteLine("Failed to load search image");
                    return;
                }

                // 4. 查找模板
                var matches = _engine.FindShapeModel(searchImage, modelId, 
                    new Dictionary<string, object>
                    {
                        ["minScore"] = 0.7,
                        ["numMatches"] = 5
                    });

                Console.WriteLine($"Found {matches.Count} matches:");
                foreach (var match in matches)
                {
                    Console.WriteLine($"  Position: ({match.Row:F2}, {match.Column:F2}), " +
                                    $"Angle: {match.Angle:F3}, Score: {match.Score:F3}");
                }

                // 5. 清理模板
                _engine.ClearShapeModel(modelId);

                // 清理资源
                templateImage?.Dispose();
                searchImage?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in template matching: {ex.Message}");
            }
        }

        /// <summary>
        /// 测量功能示例
        /// </summary>
        public void MeasurementExample()
        {
            try
            {
                // 1. 加载图像
                var image = _engine.LoadImage(@"C:\test\measurement.bmp");
                if (image == null)
                {
                    Console.WriteLine("Failed to load measurement image");
                    return;
                }

                // 2. 创建圆形测量工具
                var circleToolId = _engine.CreateMeasureTool(MeasureType.Circle, 
                    new Dictionary<string, object>
                    {
                        ["row"] = 200,
                        ["column"] = 200,
                        ["radius"] = 50,
                        ["measureLength1"] = 20,
                        ["measureLength2"] = 5,
                        ["threshold"] = 30
                    });

                if (!string.IsNullOrEmpty(circleToolId))
                {
                    // 3. 执行测量
                    var result = _engine.ExecuteMeasure(image, circleToolId);
                    if (result.IsValid)
                    {
                        Console.WriteLine("Circle measurement results:");
                        foreach (var kvp in result.Values)
                        {
                            Console.WriteLine($"  {kvp.Key}: {kvp.Value:F3}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Measurement failed: {result.ErrorMessage}");
                    }
                }

                // 4. 边缘检测后拟合圆形
                var edges = _engine.EdgeDetection(image, EdgeDetectionMethod.Canny);
                if (edges != null)
                {
                    var circleResult = _engine.FitCircle(edges, FittingMethod.LeastSquares);
                    if (circleResult.IsValid)
                    {
                        Console.WriteLine($"Fitted circle: Center=({circleResult.CenterX:F2}, {circleResult.CenterY:F2}), " +
                                        $"Radius={circleResult.Radius:F2}");
                    }
                }

                // 5. 计算距离和角度
                var point1 = new VisionPoint(100, 100);
                var point2 = new VisionPoint(200, 200);
                var distance = _engine.CalculateDistance(point1, point2);
                Console.WriteLine($"Distance between points: {distance:F2}");

                var center = new VisionPoint(150, 150);
                var angle = _engine.CalculateAngle(center, point1, point2);
                Console.WriteLine($"Angle: {angle * 180 / Math.PI:F2} degrees");

                // 清理资源
                image?.Dispose();
                edges?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in measurement: {ex.Message}");
            }
        }

        /// <summary>
        /// 3D 处理示例
        /// </summary>
        public void ThreeDProcessingExample()
        {
            try
            {
                // 1. 创建点云数据
                var pointCloud = new VisionPointCloud();
                for (int i = 0; i < 100; i++)
                {
                    pointCloud.Points3D.Add(new Pose3D(
                        Math.Random.Shared.NextDouble() * 100,
                        Math.Random.Shared.NextDouble() * 100,
                        Math.Random.Shared.NextDouble() * 100,
                        0, 0, 0));
                }

                // 2. 创建3D模型
                var modelId = _engine.Create3DModel(pointCloud);
                if (!string.IsNullOrEmpty(modelId))
                {
                    Console.WriteLine($"Created 3D model: {modelId}");

                    // 3. 3D可视化
                    var pose = new Pose3D(0, 0, 100, 0, 0, 0);
                    var visualResult = _engine.Visualize3D(modelId, pose);
                    if (visualResult.IsSuccess)
                    {
                        Console.WriteLine("3D visualization successful");
                    }

                    // 4. 平面切割
                    var planePose = new Pose3D(0, 0, 50, 0, 0, 0);
                    var intersectionModelId = _engine.IntersectPlane3D(modelId, planePose);
                    if (!string.IsNullOrEmpty(intersectionModelId))
                    {
                        Console.WriteLine($"Created intersection model: {intersectionModelId}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in 3D processing: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public void RunAllExamples()
        {
            Console.WriteLine("=== Vision Engine Usage Examples ===");
            Console.WriteLine($"Engine: {_engine.EngineName} v{_engine.EngineVersion}");
            Console.WriteLine();

            Console.WriteLine("1. Basic Image Processing Example:");
            BasicImageProcessingExample();
            Console.WriteLine();

            Console.WriteLine("2. Template Matching Example:");
            TemplateMatchingExample();
            Console.WriteLine();

            Console.WriteLine("3. Measurement Example:");
            MeasurementExample();
            Console.WriteLine();

            Console.WriteLine("4. 3D Processing Example:");
            ThreeDProcessingExample();
            Console.WriteLine();

            Console.WriteLine("=== Examples Completed ===");
        }

        #region 事件处理

        private void OnProgressChanged(object sender, AlgorithmProgressEventArgs e)
        {
            Console.WriteLine($"Progress: {e.AlgorithmName} - {e.ProgressPercentage}% - {e.StatusMessage}");
        }

        private void OnAlgorithmCompleted(object sender, AlgorithmCompletedEventArgs e)
        {
            Console.WriteLine($"Completed: {e.AlgorithmName} - Success: {e.IsSuccess} - Time: {e.ExecutionTime.TotalMilliseconds}ms");
        }

        private void OnErrorOccurred(object sender, AlgorithmErrorEventArgs e)
        {
            Console.WriteLine($"Error: {e.AlgorithmName} - {e.ErrorMessage}");
        }

        #endregion

        public void Dispose()
        {
            _engine?.Dispose();
        }
    }
}
