﻿using System;
using System.Collections.Generic;

using System.Linq;
using System.Text;
using System.Data;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Windows.Forms;
using System.Data.SqlClient;

using System.Collections.Generic;
using System.Text;
using System.Web.UI.WebControls;
using System.Data;
using System.Web.UI;
using System.Resources;
using BingSolhalcon.resources;

namespace BingSolhalcon
{
    class NPOI
    {

        public DataTable dattable;


        ////创建列
        //        public void CreateDataColumn(string columnsname)
        //        {

        ////DataColumn dc = new DataColumn();
        ////dattable.Columns.Add(dc);
        ////2.创建带列名和类型名的列(两种方式任选其一)
        //            dattable.Columns.Add("columnsname", System.Type.GetType("System.String"));
        ////dattable.Columns.Add("column0", typeof(String));

        //        }

        ////创建行
        //        public void CreateDataRow()
        //        {

        //            DataRow dr = dattable.NewRow();
        //            dattable.Rows.Add(dr);

        //            //dattable.Rows.Add();

        //          //  dattable.Rows.Add("张三", DateTime.Now);//Add里面参数的数据顺序要和dt中的列的顺序对应 

        //         //   dattable.Rows.Add(dt2.Rows[i].ItemArray);
        //        }



        public static ResourceManager res = new ResourceManager(typeof(nonUIresx)); //自定义资源字段

        //   private System.Data.DataSet dataSet;
        /// <summary>
        /// Excel导入成Datable
        /// </summary>
        /// <param name="file">导入路径(包含文件名与扩展名)</param>
        /// <returns></returns>
        /// 
        public void Import(string file, DataGridView dgv)
        {
            //DataTable dt = new DataTable();
            //dataSet = new System.Data.DataSet();
            //dataSet.Tables.Add(ExcelToTable(file));
            //dgv.DataSource = dataSet.Tables[0];
            dgv.DataSource = ExcelToTable(file);
        }

        public void Export(string file, DataTable dt)
        {

           // DataTable dt = new DataTable();

          //  dt = GetDgvToTable(dgv);
            //  dataSet1.Tables[0]=dataGridView1.d;
            TableToExcel(dt, file);
        }



        public  DataTable ExcelToTable(string file)
        {
            DataTable dt = new DataTable();
            IWorkbook workbook;
            string fileExt = Path.GetExtension(file).ToLower();
            try
            {
                using (FileStream fs = new FileStream(file, FileMode.Open, FileAccess.Read))
                {
                    //XSSFWorkbook 适用XLSX格式，HSSFWorkbook 适用XLS格式
                    if (fileExt == ".xlsx") { workbook = new XSSFWorkbook(fs); } else if (fileExt == ".xls") { workbook = new HSSFWorkbook(fs); } else { workbook = null; }
                    if (workbook == null) { return null; }
                    ISheet sheet = workbook.GetSheetAt(0);

                    //表头  
                    IRow header = sheet.GetRow(sheet.FirstRowNum);
                    List<int> columns = new List<int>();
                    for (int i = 0; i < header.LastCellNum; i++)
                    {
                        object obj = GetValueType(header.GetCell(i));
                        if (obj == null || obj.ToString() == string.Empty)
                        {
                            dt.Columns.Add(new DataColumn("Columns" + i.ToString()));
                        }
                        else
                            dt.Columns.Add(new DataColumn(obj.ToString()));
                        columns.Add(i);
                    }
                    //数据  
                    for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                    {
                        DataRow dr = dt.NewRow();
                        bool hasValue = false;
                        foreach (int j in columns)
                        {
                            dr[j] = GetValueType(sheet.GetRow(i).GetCell(j));
                            if (dr[j] != null && dr[j].ToString() != string.Empty)
                            {
                                hasValue = true;
                            }
                        }
                        if (hasValue)
                        {
                            dt.Rows.Add(dr);
                        }
                    }
                }
                return dt;
            }
            catch(Exception ex)
            {
                return null;
                
               
            }
           

        }


        /// <summary>
        /// Datable导出成Excel
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="file">导出路径(包括文件名与扩展名)</param>
        private  void TableToExcel(DataTable dt, string file)
        {
            IWorkbook workbook;
            string fileExt = Path.GetExtension(file).ToLower();
            if (fileExt == ".xlsx") { workbook = new XSSFWorkbook(); } else if (fileExt == ".xls") { workbook = new HSSFWorkbook(); } else { workbook = null; }
            if (workbook == null) { return; }
            ISheet sheet = string.IsNullOrEmpty(dt.TableName) ? workbook.CreateSheet("Sheet1") : workbook.CreateSheet(dt.TableName);

            //表头  
            IRow row = sheet.CreateRow(0);
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                ICell cell = row.CreateCell(i);
                cell.SetCellValue(dt.Columns[i].ColumnName);
            }

            //数据  
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                IRow row1 = sheet.CreateRow(i + 1);
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    ICell cell = row1.CreateCell(j);
                    cell.SetCellValue(dt.Rows[i][j].ToString());
                }
            }

            //转为字节数组  
            MemoryStream stream = new MemoryStream();
            workbook.Write(stream);
            var buf = stream.ToArray();

            //保存为Excel文件  
            using (FileStream fs = new FileStream(file, FileMode.Create, FileAccess.Write))
            {
                fs.Write(buf, 0, buf.Length);
                fs.Flush();
            }
        }

        /// <summary>
        /// 获取单元格类型
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        private static object GetValueType(ICell cell)
        {
            if (cell == null)
                return null;
            switch (cell.CellType)
            {
                case CellType.Blank: //BLANK:  
                    return null;
                case CellType.Boolean: //BOOLEAN:  
                    return cell.BooleanCellValue;
                case CellType.Numeric: //NUMERIC:  
                    return cell.NumericCellValue;
                case CellType.String: //STRING:  
                    return cell.StringCellValue;
                case CellType.Error: //ERROR:  
                    return cell.ErrorCellValue;
                case CellType.Formula: //FORMULA:  
                default:
                    return "=" + cell.CellFormula;
            }
        }


        private DataTable GetDgvToTable(DataGridView dgv)
        {
            DataTable dt = new DataTable();

            // 列强制转换
            for (int count = 0; count < dgv.Columns.Count; count++)
            {
                DataColumn dc = new DataColumn(dgv.Columns[count].Name.ToString());
                dt.Columns.Add(dc);
            }

            // 循环行
            for (int count = 0; count < dgv.Rows.Count; count++)
            {
                DataRow dr = dt.NewRow();
                for (int countsub = 0; countsub < dgv.Columns.Count; countsub++)
                {
                    dr[countsub] = Convert.ToString(dgv.Rows[count].Cells[countsub].Value);
                }
                dt.Rows.Add(dr);
            }
            return dt;

        }

        public void SqlbulkcopyInsert(string connectString, DataSet dataset)
        {
            string ie;
            double tmp;
            try
            {
                if (dataset.Tables.Count > 0)
                {
                    foreach (DataTable itemTable in dataset.Tables)
                    {
                        SqlBulkCopy sqlbulkcopy = new SqlBulkCopy(connectString, SqlBulkCopyOptions.UseInternalTransaction);
                        sqlbulkcopy.DestinationTableName = itemTable.TableName;//数据库中的表名
                        for (int i = 1; i < itemTable.Rows.Count; i++)
                        {

                            for (int j = 2; j < itemTable.Columns.Count; ++j)
                            {
                                ie = itemTable.Rows[i][j].ToString();
                                try
                                {
                                    if (!string.IsNullOrEmpty(ie))
                                    {
                                        tmp = double.Parse(ie);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    MessageBoxEX.Show(i.ToString() + "行, " + j.ToString() + "列," + ie + "\n" + ex.Message, "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                                    return;
                                }
                            }
                        }
                        sqlbulkcopy.WriteToServer(itemTable);
                    }
                }
            }
           catch(Exception ex)
            {
                     MessageBoxEX.Show(res.GetString("datatype") + "\n" + ex.Message, "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }

        }







    }
}
