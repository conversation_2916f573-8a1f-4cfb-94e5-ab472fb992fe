﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading;
using System.Drawing;
using System.Windows.Forms;
using System.Drawing.Imaging;
using MvStereoAppSDKNet;
using HalconDotNet;

using STC_Object = System.IntPtr;
using STC_DataSet = System.IntPtr;
namespace BingSolhalcon
{

    class HKVision_3D
    {
        private static readonly object Lock = new object();

        MvStereoApp.cbPointCloudMosaic cbPointCloudMosaicCallback = new MvStereoApp.cbPointCloudMosaic(cbPointCloudMosaicCallback_fun);

        MvStereoApp.MV_STEREOCAM_NET_INFO_LIST m_stDeviceList = new MvStereoApp.MV_STEREOCAM_NET_INFO_LIST();
        private MvStereoApp m_MyCamera = new MvStereoApp();
        bool m_bGrabbing = false;
        Thread m_hReceiveThread = null;


        // 原始图
        static MvStereoApp.STC_DATA_IMAGE m_stImageInfo = new MvStereoApp.STC_DATA_IMAGE(); //图片参数信息
        static UInt32 m_MaxImageSize = 1024 * 1024 * 30;
        static byte[] m_pcDataBuf = new byte[m_MaxImageSize];


        // ch:用于从驱动获取图像的缓存 | en:Buffer for getting image from driver
        byte[] m_BufForDriver = new byte[1024 * 1024 * 20];

        List<string> m_CameralName = new List<string>();
        // 显示
        Bitmap bmp = null;
        Graphics gra = null;
        Pen pen = new Pen(Color.Blue, 3);                   // 画笔颜色
        Point[] stPointList = new Point[4];                 // 条码位置的4个点坐标

        /// 图像处理自定义委托
        /// </summary>
        /// <param name="hImage">halcon图像变量</param>
        public delegate void delegateProcessHImage(MvStereoApp.STC_DATA_IMAGE stImg);

        /// <summary>
        /// 图像处理委托事件
        /// </summary>
        public event delegateProcessHImage eventProcessImage;

        //枚举相机
        public void DeviceListAcq(ref List<string> cbDeviceList)
        {
            // ch:创建设备列表 | en:Create Device List
            System.GC.Collect();
            //  cbDeviceList.Items.Clear();
            cbDeviceList = new List<string>();
            m_stDeviceList.nDeviceNum = 0;
            int nRet = MvStereoApp.MV_STA_EnumStereoCam_NET(ref m_stDeviceList);
            if (0 != nRet)
            {
                ShowErrorMsg("Enumerate devices fail!", nRet);
                return;
            }

            // ch:在窗体列表中显示设备名 | en:Display device name in the form list
            for (int i = 0; i < m_stDeviceList.nDeviceNum; i++)
            {
                MvStereoApp.MV_STEREOCAM_NET_INFO device = (MvStereoApp.MV_STEREOCAM_NET_INFO)Marshal.PtrToStructure(m_stDeviceList.pDeviceInfo[i], typeof(MvStereoApp.MV_STEREOCAM_NET_INFO));

                string strManufacturerName = System.Text.Encoding.Default.GetString(device.chManufacturerName);
                strManufacturerName = strManufacturerName.TrimEnd('\0');

                string stModelName = System.Text.Encoding.UTF8.GetString(device.chModelName);
                stModelName = stModelName.TrimEnd('\0');

                string strSerialNumber = System.Text.Encoding.UTF8.GetString(device.chSerialNumber);
                strSerialNumber = strSerialNumber.TrimEnd('\0');

                string stID = System.Text.Encoding.UTF8.GetString(device.chUserDefinedName);
                stID = stID.TrimEnd('\0');
                cbDeviceList.Add(stID);
            }

            // ch:选择第一项 | en:Select the first item


        }
        //打开设备
        public int Open_Camera(string cameraname, List<string> listcamera)
        {
            if (m_stDeviceList.nDeviceNum == 0)
            {
                ShowErrorMsg("No device, please select", 0);
                return -1;
            }
            int index = listcamera.IndexOf(cameraname);
            // ch:获取选择的设备信息 | en:Get selected device information
            MvStereoApp.MV_STEREOCAM_NET_INFO device = (MvStereoApp.MV_STEREOCAM_NET_INFO)Marshal.PtrToStructure(m_stDeviceList.pDeviceInfo[
index], typeof(MvStereoApp.MV_STEREOCAM_NET_INFO));


            // ch:打开设备 | en:Open device
            if (null == m_MyCamera)
            {
                m_MyCamera = new MvStereoApp();
                if (null == m_MyCamera)
                {
                    return -1;
                }
            }

            int nRet = m_MyCamera.MV_STA_CreateHandleByCameraInfo_NET(ref device);
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                ShowErrorMsg("Create handle Failed ", nRet);
                return -1;
            }


            IntPtr pUser = new IntPtr(0);
            //MvStereoApp.cbPointCloudMosaic cbPointCloudMosaicCallback;
            //cbPointCloudMosaicCallback = new MvStereoApp.cbPointCloudMosaic(cbPointCloudMosaicCallback_fun);
            nRet = m_MyCamera.MV_STA_RegisterPointCloudMosaicImageCallBack_NET(cbPointCloudMosaicCallback, pUser);
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                ShowErrorMsg("MV_STA_RegisterPointCloudMosaicImageCallBack_NET Failed ", nRet);
                return -1;
            }

            MvStereoApp.STC_PointCloudMosaicConfigParam pstConfigInfo;
            pstConfigInfo.nExpanRatio = 100;
            pstConfigInfo.nLineNum = 100;
            pstConfigInfo.nReserved = null;

            nRet = m_MyCamera.MV_STA_SetPointCloudMosaicConfigParam_NET(ref pstConfigInfo);
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                ShowErrorMsg("MV_STA_SetPointCloudMosaicConfigParam_NET Failed ", nRet);
                return -1;
            }

            //开启设备
            nRet = m_MyCamera.MV_STA_OpenDevice_NET();
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                ShowErrorMsg("Open Device Failed ", nRet);
                return -1;
            }

            return 0;
            // bnGetParam_Click(null, null);// ch:获取参数 | en:Get parameters


        }
        //关闭设备
        public void Close_Camera()
        {
            // ch:取流标志位清零 | en:Reset flow flag bit
            if (m_bGrabbing == true)
            {
                m_bGrabbing = false;
                m_hReceiveThread.Join();
            }



            // ch:关闭设备 | en:Close Device
            m_MyCamera.MV_STA_Stop_NET();
            m_MyCamera.MV_STA_DestroyHandle_NET();


        }
        //设置为连续模式
        public void SetContinuesMode()
        {
            m_MyCamera.MV_STA_SetEnumValue_NET("TriggerMode", 0);
        }
        //设置触发模式
        public void SetTriggerMode()
        {
           
            m_MyCamera.MV_STA_SetEnumValue_NET("TriggerMode", 1);
          //  m_MyCamera.MV_CC_SetEnumValue_NET("TriggerMode", 1);

        }
        public void SetSoftTrigger()
        {
            // ch: 触发源选择:0 - Line0 || en :TriggerMode select;
            //           1 - Line1;
            //           2 - Line2;
            //           3 - Line3;
            //           4 - Counter;
            //           7 - Software;
            m_MyCamera.MV_STA_SetEnumValue_NET("TriggerSource", 7);
        }
        //开始采集
        public void StartGrab(int SelectedIndex)
        {
            // ch:标志位置位true | en:Set position bit true
            m_bGrabbing = true;
            m_hReceiveThread = new Thread(new ParameterizedThreadStart(ReceiveThreadProcess));
            //m_hReceiveThread = new Thread(ReceiveThreadProcess);
            m_hReceiveThread.Start(SelectedIndex);

            // ch:开始采集 | en:Start Grabbing
            int nRet = m_MyCamera.MV_STA_Start_NET();
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                m_bGrabbing = false;
                m_hReceiveThread.Join();
                ShowErrorMsg("Start Grabbing Fail!", nRet);
                return;
            }

        }
        //关闭采集
        public void StopGrab()
        {
            // ch:标志位设为false | en:Set flag bit false
            m_bGrabbing = false;
            m_hReceiveThread.Join();

            // ch:停止采集 | en:Stop Grabbing
            int nRet = m_MyCamera.MV_STA_Stop_NET();
            if (nRet != MvStereoApp.MV_STA_OK)
            {
                ShowErrorMsg("Stop Grabbing Fail!", nRet);
            }


        }
        //软触发
        public void SoftTrigger()
        {
            int nRet = m_MyCamera.MV_STA_SetCommandValue_NET("TriggerSoftware");
            if (MvStereoApp.MV_STA_OK != nRet)
            {
                ShowErrorMsg("Trigger Software Fail!", nRet);
            }
        }
        private static void cbPointCloudMosaicCallback_fun(IntPtr pData, int nDataSize, int nStep)
        {
            return;
        }
        public void ReceiveThreadProcess(object SelectedIndex)
        {
            int nRet = MvStereoApp.MV_STA_OK;

            m_pcDataBuf = new byte[m_MaxImageSize]; //初始化
            m_stImageInfo = new MvStereoApp.STC_DATA_IMAGE();

            STC_DataSet pDataSet = IntPtr.Zero;
            Int32 nResultCount = 0;
            UInt32 nMsgType = 0;

            STC_Object DataObj = IntPtr.Zero;

            while ((m_bGrabbing))
            {
                nRet = m_MyCamera.MV_STA_ReceiveDataTimeout_NET(ref pDataSet, 1000);
                if (nRet == MvStereoApp.MV_STA_OK)
                {
                    nResultCount = m_MyCamera.MV_STA_DataSetCount_NET(pDataSet);

                    Int32 index = 0;
                    for (index = 0; index < nResultCount; index++)
                    {

                        DataObj = m_MyCamera.MV_STA_DataSetAt_NET(pDataSet, index);
                        if (0 != nRet)
                        {
                            m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                            continue;
                        }


                        nMsgType = m_MyCamera.MV_STA_DataMsgType_NET(DataObj);
                        if (0 != nRet)
                        {
                            m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                            continue;
                        }

                        if (MvStereoApp.STC_DATA_MSG_TYPE_IMG_RAW == nMsgType)
                        {
                            GC.Collect();   //强制释放下资源；

                            MvStereoApp.STC_DATA_IMAGE stImg = new MvStereoApp.STC_DATA_IMAGE();
                            nRet = m_MyCamera.MV_STA_GetImage_NET(DataObj, ref stImg);
                            if (0 != nRet)
                            {
                                m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                                continue;
                            }


                            // keep it to local;   暂不考虑数据长度超过 m_MaxImageSize 的情况

                            //当前部分双目相机显示是Mono12 实际是 Mono16
                            //双目相机有MV-DB和MV-DS两种


                            MvStereoApp.MV_STEREOCAM_NET_INFO device = (MvStereoApp.MV_STEREOCAM_NET_INFO)Marshal.PtrToStructure(m_stDeviceList.pDeviceInfo[(int)SelectedIndex], typeof(MvStereoApp.MV_STEREOCAM_NET_INFO));
                            String sShuangMu1 = "MV-DB";
                            String sShuangMu2 = "MV-DS";
                            String sModeNameTmp = System.Text.Encoding.Default.GetString(device.chModelName);
                            if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono12 == stImg.enPixelType &&
                          ((sShuangMu1.Substring(0, 5) == sModeNameTmp.Substring(0, 5)) || (sShuangMu2.Substring(0, 5) == sModeNameTmp.Substring(0, 5))))
                            {
                                stImg.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16;
                            }






                            {
                                Monitor.Enter(Lock);
                                m_stImageInfo.nWidth = stImg.nWidth;
                                m_stImageInfo.nHeight = stImg.nHeight;
                                m_stImageInfo.enPixelType = stImg.enPixelType;
                                m_stImageInfo.nFrameLen = stImg.nFrameLen;
                                m_stImageInfo.nFrameNum = stImg.nFrameNum;
                                Marshal.Copy(stImg.pData, m_pcDataBuf, 0, (int)stImg.nFrameLen);
                                Monitor.Exit(Lock);
                            }
                            eventProcessImage(m_stImageInfo);
                            if (stImg.enPixelType == MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32)
                            {
                                //格式转换、
                                MvStereoApp.STC_NORMALIZE_IMAGE pstImage = new MvStereoApp.STC_NORMALIZE_IMAGE();
                                pstImage.enDstPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16;
                                pstImage.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32;
                                pstImage.nHeight = stImg.nHeight;
                                pstImage.nLen = stImg.nFrameLen;
                                //public uint nNormalizedLen;
                                pstImage.nWidth = stImg.nWidth;
                                pstImage.pData = stImg.pData;
                                //public IntPtr pNormalizedData;
                                nRet = m_MyCamera.MV_STA_NormalizedPointCloudImage_NET(ref pstImage);
                                if (0 != nRet)
                                {
                                    ShowErrorMsg("MV_STA_NormalizedPointCloudImage_NET Failed ", nRet);
                                    return;
                                }
                                nRet = m_MyCamera.MV_STA_PointCloudMosaicUpdateImage_NET(pstImage.pNormalizedData, pstImage.nWidth, (int)pstImage.nNormalizedLen);
                            }

                            //pictureBox1.Invoke(new MethodInvoker(delegate
                            //{
                            //    Display(stImg);

                            //}));


                        }
                        else if (MvStereoApp.STC_3D_Profile_Count == nMsgType)
                        {

                            IntPtr pnProfileCount = new IntPtr(0);
                            nRet = m_MyCamera.MV_STA_GetProfileCount_NET(DataObj, ref pnProfileCount);
                            if (0 != nRet)
                            {
                                m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                                continue;
                            }


                        }
                        else if (MvStereoApp.STC_3D_Profile_LinePntNum == nMsgType)
                        {
                            //need support
                            IntPtr pnProfileLineNum = new IntPtr(0);
                            nRet = m_MyCamera.MV_STA_GetProfileLinePntNum_NET(DataObj, ref pnProfileLineNum);
                            if (0 != nRet)
                            {
                                m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                                continue;
                            }
                        }
                        else if (MvStereoApp.STC_3D_Profile_Intensity == nMsgType)
                        {
                            // 亮度图
                            GC.Collect();   //强制释放下资源；

                            MvStereoApp.STC_PROFILE_INTENSITY stProfileIntensity = new MvStereoApp.STC_PROFILE_INTENSITY();
                            nRet = m_MyCamera.MV_STA_GetProfileIntensity_NET(DataObj, ref stProfileIntensity);
                            if (0 != nRet)
                            {
                                m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                                continue;
                            }

                            // 亮度图默认Mono8；如果需要此处可以将Mono8转为Halcon的格式
                            // Mono8ImageToHalconProc(stProfileIntensity, m_stImageInfo.nWidth, m_stImageInfo.nHeight);

                        }
                        else
                        {
                            // need support
                        }

                    }

                    m_MyCamera.MV_STA_DestroyData_NET(pDataSet);
                }
                else
                {
                    Thread.Sleep(1);
                    // get no data
                }
            }
        }
        public void Display(MvStereoApp.STC_DATA_IMAGE pstDisplayImage, PictureBox pictureBox1)
        {

            int nRet = MvStereoApp.MV_STA_OK;

            if (pstDisplayImage.enPixelType == MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono8)
            {
                Marshal.Copy(pstDisplayImage.pData, m_BufForDriver, 0, (int)pstDisplayImage.nFrameLen);

                var handle = GCHandle.Alloc(m_BufForDriver, GCHandleType.Pinned);
                IntPtr pImage = handle.AddrOfPinnedObject();
                bmp = new Bitmap(pstDisplayImage.nWidth, pstDisplayImage.nHeight, pstDisplayImage.nWidth, PixelFormat.Format8bppIndexed, pImage);
                ColorPalette cp = bmp.Palette;
                for (int i = 0; i < 256; i++)
                {
                    cp.Entries[i] = Color.FromArgb(i, i, i);
                }
                bmp.Palette = cp;


                pictureBox1.Image = (Image)bmp;

                if (handle.IsAllocated)
                {
                    handle.Free();
                }
            }
            else if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16 == pstDisplayImage.enPixelType)
            {
                // 高帧率轮廓仪，一次会接收到多帧图像，需要分割
                UInt32 nTotalFrameNum = pstDisplayImage.nHeight;
                for (UInt32 nCurFrameIndex = 0; nCurFrameIndex < nTotalFrameNum; nCurFrameIndex++)
                {
                    MvStereoApp.STC_DATA_IMAGE stDataTmp = new MvStereoApp.STC_DATA_IMAGE();

                    stDataTmp.nWidth = pstDisplayImage.nWidth;
                    stDataTmp.nHeight = 1;
                    stDataTmp.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16;
                    stDataTmp.nFrameLen = pstDisplayImage.nFrameLen / nTotalFrameNum;
                    stDataTmp.pData = new IntPtr(pstDisplayImage.pData.ToInt64() + nCurFrameIndex * stDataTmp.nFrameLen);
                    stDataTmp.nFrameNum = pstDisplayImage.nFrameNum;

                    IntPtr hWnd = pictureBox1.Handle;

                    nRet = m_MyCamera.MV_STA_DisplayStandardPointCloud_NET(ref stDataTmp, hWnd, pictureBox1.Width, pictureBox1.Height);
                    if (0 != nRet)
                    {
                        return;
                    }
                }
            }
            else if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f_Planar == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_AC32 == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32 == pstDisplayImage.enPixelType))
            {
                // 点云图，转换为标准图，渲染

                if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f_Planar == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_AC32 == pstDisplayImage.enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32 == pstDisplayImage.enPixelType))
                {
                    // 点云图，转换为标准图，渲染
                    MvStereoApp.STC_NORMALIZE_IMAGE stImage = new MvStereoApp.STC_NORMALIZE_IMAGE();
                    stImage.nWidth = pstDisplayImage.nWidth;
                    stImage.nHeight = pstDisplayImage.nHeight;
                    stImage.pData = pstDisplayImage.pData;
                    stImage.nLen = pstDisplayImage.nFrameLen;
                    stImage.enPixelType = pstDisplayImage.enPixelType;

                    stImage.enDstPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32;

                    nRet = m_MyCamera.MV_STA_NormalizedPointCloudImage_NET(ref stImage);
                    if (0 != nRet)
                    {
                        return;
                    }

                    MvStereoApp.STC_DATA_IMAGE stDataTmp = new MvStereoApp.STC_DATA_IMAGE();

                    stDataTmp.nWidth = pstDisplayImage.nWidth;
                    stDataTmp.nHeight = pstDisplayImage.nHeight;
                    stDataTmp.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32;

                    stDataTmp.pData = stImage.pNormalizedData;
                    stDataTmp.nFrameLen = stImage.nNormalizedLen;
                    stDataTmp.nFrameNum = pstDisplayImage.nFrameNum;

                    IntPtr hWnd = pictureBox1.Handle;

                    nRet = m_MyCamera.MV_STA_DisplayStandardPointCloud_NET(ref stDataTmp, hWnd, pictureBox1.Width, pictureBox1.Height);
                    if (0 != nRet)
                    {
                        return;
                    }
                }
                else
                {
                    IntPtr hWnd = pictureBox1.Handle;

                    m_MyCamera.MV_STA_DisplayStandardPointCloud_NET(ref pstDisplayImage, hWnd, pictureBox1.Width, pictureBox1.Height);
                }

            }

            else if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16 == pstDisplayImage.enPixelType) ||
                 (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C32 == pstDisplayImage.enPixelType) ||
                (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C16 == pstDisplayImage.enPixelType))
            {
                MvStereoApp.STC_DEPTH_MAP_DISPLAY_EX stDisplayExInfo = new MvStereoApp.STC_DEPTH_MAP_DISPLAY_EX();

                if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C32 == pstDisplayImage.enPixelType ||
                    (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C16 == pstDisplayImage.enPixelType))
                {

                    // 深度图，转换为标准图，渲染
                    MvStereoApp.STC_NORMALIZE_IMAGE stImage = new MvStereoApp.STC_NORMALIZE_IMAGE();

                    stImage.nWidth = pstDisplayImage.nWidth;
                    stImage.nHeight = pstDisplayImage.nHeight;
                    stImage.pData = pstDisplayImage.pData;
                    stImage.nLen = pstDisplayImage.nFrameLen;
                    stImage.enPixelType = pstDisplayImage.enPixelType;

                    stImage.enDstPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16;

                    nRet = m_MyCamera.MV_STA_NormalizedDepthImage_NET(ref stImage);
                    if (0 != nRet)
                    {
                        return;
                    }

                    MvStereoApp.STC_DATA_IMAGE stDataTmp = new MvStereoApp.STC_DATA_IMAGE();
                    stDataTmp.nWidth = pstDisplayImage.nWidth;
                    stDataTmp.nHeight = pstDisplayImage.nHeight;
                    stDataTmp.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16;

                    stDataTmp.pData = stImage.pNormalizedData;
                    stDataTmp.nFrameLen = stImage.nNormalizedLen;
                    stDataTmp.nFrameNum = pstDisplayImage.nFrameNum;

                    IntPtr hWnd = pictureBox1.Handle;

                    nRet = m_MyCamera.MV_STA_DisplayStandardDepthMap_NET(ref stDataTmp, ref stDisplayExInfo, hWnd);
                    if (0 != nRet)
                    {
                        return;
                    }
                }
                else
                {
                    IntPtr hWnd = pictureBox1.Handle;
                    nRet = m_MyCamera.MV_STA_DisplayStandardDepthMap_NET(ref pstDisplayImage, ref stDisplayExInfo, hWnd);
                    if (0 != nRet)
                    {
                        return;
                    }
                }
            }
            else if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Rgbd_C16 == pstDisplayImage.enPixelType)
            {
                IntPtr hWnd = pictureBox1.Handle;
                MvStereoApp.STC_DEPTH_MAP_DISPLAY_EX stDisplayExInfo = new MvStereoApp.STC_DEPTH_MAP_DISPLAY_EX();
                nRet = m_MyCamera.MV_STA_DisplayRgbdMap_NET(ref pstDisplayImage, ref stDisplayExInfo, hWnd);
                if (0 != nRet)
                {
                    return;
                }
            }


        }
        public void PointCloudImage_halcon(MvStereoApp.STC_DATA_IMAGE pstDisplayImage, out HTuple ObjectModel3D)
        {

            bool bPointCloudImage = false;
            int nPointSize = 0;
            HTuple hObjectModel3D = null;
            bPointCloudImageExInfo(pstDisplayImage.enPixelType, ref bPointCloudImage, ref nPointSize);
            if (true == bPointCloudImage)
            {

                // 点云图处理流程  （高帧率相机有多可能一次输出多帧，需要注意； 其他相机每次输出一帧点云图）
                int nRealImageNum = pstDisplayImage.nHeight;
                byte[] targetDataBuf = new byte[m_pcDataBuf.Length];
                Array.Copy(m_pcDataBuf, targetDataBuf, m_pcDataBuf.Length);

                GCHandle hTotalImageBuf = GCHandle.Alloc(targetDataBuf, GCHandleType.Pinned);
                IntPtr pStartImageBuf = hTotalImageBuf.AddrOfPinnedObject();
                for (int nIndex = 0; nIndex < nRealImageNum; nIndex++)
                {
                    MvStereoApp.STC_DATA_IMAGE stImageInfoTmp = new MvStereoApp.STC_DATA_IMAGE();
                    stImageInfoTmp.enPixelType = pstDisplayImage.enPixelType;
                    stImageInfoTmp.nDevTimeStampHigh = pstDisplayImage.nDevTimeStampHigh;
                    stImageInfoTmp.nDevTimeStampLow = pstDisplayImage.nDevTimeStampLow;

                    stImageInfoTmp.nFrameNum = pstDisplayImage.nFrameNum;
                    stImageInfoTmp.nReserved0 = pstDisplayImage.nReserved0;
                    stImageInfoTmp.nWidth = pstDisplayImage.nWidth;
                    stImageInfoTmp.nHeight = 1;   //  逐帧进行处理

                    stImageInfoTmp.nFrameLen = (uint)(nPointSize * stImageInfoTmp.nWidth);   //实际长度 = 点数 * 每个点的长度
                    stImageInfoTmp.pData = new IntPtr(pStartImageBuf.ToInt64() + nIndex * stImageInfoTmp.nFrameLen);



                    PointCloudImageProc(stImageInfoTmp, nIndex, out hObjectModel3D);

                    // HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hv_height, hv_width);

                    // HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);


                }

                if (hTotalImageBuf.IsAllocated)
                {
                    hTotalImageBuf.Free();
                }

            }
            ObjectModel3D = hObjectModel3D;
            //if (true == bDepthImage(pstDisplayImage.enPixelType))
            //{
            //    // 深度图处理
            //    DepthImageProc();

            //}

        }
        public void DepthImage_halcon(MvStereoApp.STC_DATA_IMAGE pstDisplayImage, out HObject hImage)
        {
            HObject ho_Image;
            HOperatorSet.GenEmptyObj(out ho_Image);
            if (true == bDepthImage(pstDisplayImage.enPixelType))
            {
                // 深度图处理

                DepthImageProc(pstDisplayImage, out ho_Image);

            }
            hImage = ho_Image;
        }


        Boolean bDepthImage(MvStereoApp.MvStereoAppGvspPixelType enPixelType)
        {
            if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C16 == enPixelType) ||
                (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16 == enPixelType) ||
                (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C32 == enPixelType))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public void bPointCloudImageExInfo(MvStereoApp.MvStereoAppGvspPixelType enPixelType, ref bool bPointCloudImage, ref int nPointSize)
        {
            if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16 == enPixelType)
            {
                bPointCloudImage = true;
                nPointSize = 3 * 2;
            }
            else if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32 == enPixelType) ||
                     (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f == enPixelType))
            {
                bPointCloudImage = true;
                nPointSize = 3 * 4;
            }
            else
            {
                bPointCloudImage = false;
            }

            return;
        }
        // 将深度图转换HObject格式
        public void DepthImageProc(MvStereoApp.STC_DATA_IMAGE stImage, out HObject hImage)
        {
            HObject ho_Image = null;
            switch (stImage.enPixelType)
            {
                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Mono16:
                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C16:
                    {
                        GCHandle hBuf = GCHandle.Alloc(m_pcDataBuf, GCHandleType.Pinned);
                        IntPtr ptr = hBuf.AddrOfPinnedObject();
                        HOperatorSet.GenImage1(out ho_Image, "int2", stImage.nWidth, stImage.nHeight, ptr.ToInt64());

                        if (hBuf.IsAllocated)
                        {
                            hBuf.Free();
                        }
                    }
                    break;
                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_C32:
                    {
                        GCHandle hBuf = GCHandle.Alloc(m_pcDataBuf, GCHandleType.Pinned);
                        IntPtr ptr = hBuf.AddrOfPinnedObject();
                        HOperatorSet.GenImage1(out ho_Image, "int4", stImage.nWidth, stImage.nHeight, ptr.ToInt64());

                        if (hBuf.IsAllocated)
                        {
                            hBuf.Free();
                        }
                    }
                    break;
                default:
                    break;
            }
            hImage = ho_Image;
            //if (null != ho_Image)
            //{
            //    //保存深度图
            //    string strtiffName = "./Halcon_Image_";
            //    strtiffName += stImage.nFrameNum;
            //    strtiffName += ".tiff";
            //    HOperatorSet.WriteImage(ho_Image, "tiff", 0, strtiffName);
            //}

            //return;
        }

        // 将点云图转换HTuple格式
        public void PointCloudImageProc(MvStereoApp.STC_DATA_IMAGE stImage, int nsubFrameNum, out HTuple hObjectModel3D)
        {
            //HTuple hObjectModel3D = null;
            HTuple hv_X = new HTuple();
            HTuple hv_Y = new HTuple();
            HTuple hv_Z = new HTuple();
            Int32 j = 0;

            switch (stImage.enPixelType)
            {
                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16:
                    {
                        short[] ConvertData = new short[stImage.nFrameLen / 2];
                        Marshal.Copy(stImage.pData, ConvertData, 0, (int)stImage.nFrameLen / 2);
                        for (j = 0; j < stImage.nWidth; j++)
                        {
                            hv_X[j] = ConvertData[j * 3];
                            hv_Y[j] = ConvertData[j * 3 + 1];
                            hv_Z[j] = ConvertData[j * 3 + 2];
                        }
                    }
                    break;

                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32:
                    {
                        Int32[] ConvertData = new Int32[stImage.nFrameLen / 4];
                        Marshal.Copy(stImage.pData, ConvertData, 0, (int)stImage.nFrameLen / 4);
                        for (j = 0; j < stImage.nWidth; j++)
                        {
                            hv_X[j] = ConvertData[j * 3];
                            hv_Y[j] = ConvertData[j * 3 + 1];
                            hv_Z[j] = ConvertData[j * 3 + 2];
                        }
                    }
                    break;

                case MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f:
                    {
                        float[] ConvertData = new float[stImage.nFrameLen / 4];
                        Marshal.Copy(stImage.pData, ConvertData, 0, (int)stImage.nFrameLen / 4);
                        for (j = 0; j < stImage.nWidth; j++)
                        {
                            hv_X[j] = ConvertData[j * 3];
                            hv_Y[j] = ConvertData[j * 3 + 1];
                            hv_Z[j] = ConvertData[j * 3 + 2];
                        }
                    }
                    break;
                default:
                    break;
            }

            HOperatorSet.GenObjectModel3dFromPoints(hv_X, hv_Y, hv_Z, out hObjectModel3D);

            //if (null != hObjectModel3D)
            //{
            //    // 保存点云图
            //    string strPlyName = "./Halcon_Image_";
            //    strPlyName += stImageInfo.nFrameNum;
            //    strPlyName += "_";
            //    strPlyName += nsubFrameNum;
            //    strPlyName += ".ply";
            //    HOperatorSet.WriteObjectModel3d(hObjectModel3D, "ply", strPlyName, "invert_normals", "false");


            //    string strObjName = "./Halcon_Image_";
            //    strObjName += stImageInfo.nFrameNum;
            //    strObjName += "_";
            //    strObjName += nsubFrameNum;
            //    strObjName += ".ply";
            //    HOperatorSet.WriteObjectModel3d(hObjectModel3D, "obj", strObjName, "invert_normals", "false");
            //}
        }

        //保存为PLY格式
        public void SavePly(HTuple ObjectModel3D, string strFileName)
        {
            HOperatorSet.WriteObjectModel3d(ObjectModel3D, "ply", strFileName, "invert_normals", "false");
        }
        //public void SavePly(MvStereoApp.STC_DATA_IMAGE pstDisplayImage, string strFileName)
        //{
        //    Int32 nRet = MvStereoApp.MV_STA_OK;

        //    if (!m_bGrabbing)
        //    {
        //        // ShowErrorMsg("no start work!", 0);
        //        return;
        //    }

        //    if (0 == pstDisplayImage.nFrameLen)
        //    {
        //        // ShowErrorMsg("no data!", 0);
        //        return;
        //    }

        //    if (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16 == pstDisplayImage.enPixelType)
        //    {
        //        // 直接save

        //        MvStereoApp.STC_IMG_SAVE_ABC16_TO_PLY_PARAM stPlyImage = new MvStereoApp.STC_IMG_SAVE_ABC16_TO_PLY_PARAM();
        //        stPlyImage.nLinePntNum = pstDisplayImage.nFrameLen / (sizeof(short) * 3);
        //        stPlyImage.nLineNum = 1;

        //        byte[] targetDataBuf = new byte[m_pcDataBuf.Length];
        //        Array.Copy(m_pcDataBuf, targetDataBuf, m_pcDataBuf.Length);
        //        GCHandle hTotalImageBuf = GCHandle.Alloc(targetDataBuf, GCHandleType.Pinned);
        //        stPlyImage.pSrcData = hTotalImageBuf.AddrOfPinnedObject();

        //        stPlyImage.nSrcDataLen = pstDisplayImage.nFrameLen;
        //        stPlyImage.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC16;


        //        UInt32 nLenTmp = (stPlyImage.nLinePntNum * stPlyImage.nLineNum * (16 * 3 + 4)) + 2048;

        //        byte[] pcPlyDataBuf = new byte[nLenTmp];

        //        stPlyImage.pDstBuf = Marshal.UnsafeAddrOfPinnedArrayElement(pcPlyDataBuf, 0);
        //        stPlyImage.nDstBufSize = nLenTmp;

        //        nRet = m_MyCamera.MV_STA_SavePly_NET(ref stPlyImage);
        //        if (MvStereoApp.MV_STA_OK != nRet)
        //        {
        //            // ShowErrorMsg("MV_STA_SavePly failed !", nRet);
        //            return;
        //        }

        //        byte[] ys = new byte[stPlyImage.nDstBufLen];
        //        Marshal.Copy(stPlyImage.pDstBuf, ys, 0, (int)stPlyImage.nDstBufLen);

        //       // string strFileName = filepath + "\\" + "Image_";
        //        strFileName += pstDisplayImage.nFrameNum;
        //        strFileName += ".ply";

        //        FileStream file = new FileStream(strFileName, FileMode.Create, FileAccess.Write);
        //        file.Write(ys, 0, (int)stPlyImage.nDstBufLen);
        //        file.Close();

        //        //ShowErrorMsg("Success save ply file!", nRet);
        //    }
        //    else if ((MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f == pstDisplayImage.enPixelType) ||
        //               (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32f_Planar == pstDisplayImage.enPixelType) ||
        //        (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_AC32 == pstDisplayImage.enPixelType) ||
        //        (MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32 == pstDisplayImage.enPixelType))
        //    {

        //        // 点云图，转换为标准图
        //        MvStereoApp.STC_NORMALIZE_IMAGE stImage = new MvStereoApp.STC_NORMALIZE_IMAGE();
        //        stImage.nWidth = pstDisplayImage.nWidth;
        //        stImage.nHeight = pstDisplayImage.nHeight;
        //        stImage.enPixelType = pstDisplayImage.enPixelType;
        //        stImage.nLen = pstDisplayImage.nFrameLen;


        //        {
        //            Monitor.Enter(Lock);
        //            stImage.pData = Marshal.UnsafeAddrOfPinnedArrayElement(m_pcDataBuf, 0);
        //            stImage.enDstPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32;
        //            nRet = m_MyCamera.MV_STA_NormalizedPointCloudImage_NET(ref stImage);
        //            Monitor.Exit(Lock);
        //        }

        //        if (MvStereoApp.MV_STA_OK != nRet)
        //        {
        //            //ShowErrorMsg("MV_STA_NormalizedDepthImage failed !", nRet);
        //            return;
        //        }

        //        // save it 
        //        MvStereoApp.STC_IMG_SAVE_ABC16_TO_PLY_PARAM stPlyImage = new MvStereoApp.STC_IMG_SAVE_ABC16_TO_PLY_PARAM();

        //        stPlyImage.nLinePntNum = stImage.nNormalizedLen / (sizeof(int) * 3);
        //        stPlyImage.nLineNum = 1;

        //        stPlyImage.pSrcData = stImage.pNormalizedData;
        //        stPlyImage.nSrcDataLen = stImage.nNormalizedLen;
        //        stPlyImage.enPixelType = MvStereoApp.MvStereoAppGvspPixelType.STC_PixelType_Gvsp_Coord3D_ABC32;

        //        UInt32 nLenTmp = (stPlyImage.nLinePntNum * stPlyImage.nLineNum * (16 * 3 + 4)) + 2048;
        //        byte[] pcPlyDataBuf = new byte[nLenTmp];

        //        stPlyImage.pDstBuf = Marshal.UnsafeAddrOfPinnedArrayElement(pcPlyDataBuf, 0);
        //        stPlyImage.nDstBufSize = nLenTmp;

        //        nRet = m_MyCamera.MV_STA_SavePly_NET(ref stPlyImage);
        //        if (MvStereoApp.MV_STA_OK != nRet)
        //        {
        //            //ShowErrorMsg("MV_STA_SavePly failed !", nRet);
        //            return;
        //        }

        //        byte[] ys = new byte[stPlyImage.nDstBufLen];
        //        Marshal.Copy(stPlyImage.pDstBuf, ys, 0, (int)stPlyImage.nDstBufLen);

        //      //  string strFileName = filepath + "\\" + "Image_";
        //        strFileName += pstDisplayImage.nFrameNum;
        //        strFileName += ".ply";

        //        FileStream file = new FileStream(strFileName, FileMode.Create, FileAccess.Write);
        //        file.Write(ys, 0, (int)stPlyImage.nDstBufLen);
        //        file.Close();

        //        // ShowErrorMsg("Success save ply file!", nRet);
        //    }
        //    else
        //    {
        //        // ShowErrorMsg("Not support save ply file!", nRet);
        //        return;
        //    }
        //}


        //错误消息显示
        private void ShowErrorMsg(string csMessage, int nErrorNum)
        {
            string errorMsg;
            if (nErrorNum == 0)
            {
                errorMsg = csMessage;
            }
            else
            {
                errorMsg = csMessage + ": Error =" + String.Format("{0:X}", nErrorNum);
            }

            switch (nErrorNum)
            {
                case MvStereoApp.MV_STA_E_HANDLE: errorMsg += " Error or invalid handle "; break;
                case MvStereoApp.MV_STA_E_SUPPORT: errorMsg += " Not supported function "; break;
                case MvStereoApp.MV_STA_E_BUFOVER: errorMsg += " Cache is full "; break;
                case MvStereoApp.MV_STA_E_CALLORDER: errorMsg += " Function calling order error "; break;
                case MvStereoApp.MV_STA_E_PARAMETER: errorMsg += " Incorrect parameter "; break;
                case MvStereoApp.MV_STA_E_RESOURCE: errorMsg += " Applying resource failed "; break;
                case MvStereoApp.MV_STA_E_NODATA: errorMsg += " No data "; break;
                case MvStereoApp.MV_STA_E_PRECONDITION: errorMsg += " Precondition error, or running environment changed "; break;
                case MvStereoApp.MV_STA_E_VERSION: errorMsg += " Version mismatches "; break;
                case MvStereoApp.MV_STA_E_NOENOUGH_BUF: errorMsg += " Insufficient memory "; break;
                case MvStereoApp.MV_STA_E_ABNORMAL_IMAGE: errorMsg += " error image "; break;
                case MvStereoApp.MV_STA_E_LOAD_LIBRARY: errorMsg += " load dll  error "; break;
                case MvStereoApp.MV_STA_E_UNKNOW: errorMsg += " Unknown error "; break;
            }

            MessageBox.Show(errorMsg, "PROMPT");
        }


    }
}
