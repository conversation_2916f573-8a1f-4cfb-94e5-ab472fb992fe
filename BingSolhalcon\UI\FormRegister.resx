﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="label1.Text" xml:space="preserve">
    <value>机器码</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtHardware.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>关闭</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="&gt;&gt;btnReg_Click.Name" xml:space="preserve">
    <value>btnReg_Click</value>
  </data>
  <data name="&gt;&gt;txtLicence.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtHardware.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 32</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtHardware.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>注册码</value>
  </data>
  <data name="&gt;&gt;btnClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>10, 20</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="btnReg_Click.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtHardware.Size" type="System.Drawing.Size, System.Drawing">
    <value>603, 30</value>
  </data>
  <data name="txtHardware.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;btnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 31</value>
  </data>
  <data name="btnReg_Click.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 178</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtLicence.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 98</value>
  </data>
  <data name="&gt;&gt;txtLicence.Name" xml:space="preserve">
    <value>txtLicence</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 31</value>
  </data>
  <data name="&gt;&gt;txtHardware.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 68</value>
  </data>
  <data name="&gt;&gt;txtHardware.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;btnClose.Name" xml:space="preserve">
    <value>btnClose</value>
  </data>
  <data name="&gt;&gt;txtLicence.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtLicence.Size" type="System.Drawing.Size, System.Drawing">
    <value>603, 30</value>
  </data>
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="btnClose.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnClose.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnReg_Click.Text" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="btnReg_Click.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="label2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="btnClose.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;btnReg_Click.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtHardware.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtLicence.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 37</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>FormRegister</value>
  </data>
  <data name="&gt;&gt;btnClose.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnReg_Click.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 103</value>
  </data>
  <data name="&gt;&gt;btnReg_Click.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtHardware.Name" xml:space="preserve">
    <value>txtHardware</value>
  </data>
  <data name="btnReg_Click.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 68</value>
  </data>
  <data name="&gt;&gt;btnReg_Click.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtLicence.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="&gt;&gt;txtLicence.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>849, 345</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormRegister</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 8, 6, 8</value>
  </data>
  <data name="txtLicence.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10pt</value>
  </data>
  <data name="btnClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 178</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>