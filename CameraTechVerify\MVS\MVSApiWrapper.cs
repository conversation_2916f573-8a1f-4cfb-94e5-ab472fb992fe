using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using CameraTechVerify.Models;
using static CameraTechVerify.MVS.MVSStructures;

namespace CameraTechVerify.MVS
{
    /// <summary>
    /// MVS 4.5.1 SDK API 包装类
    /// 提供对真实 MVS SDK 的统一访问接口
    /// </summary>
    public class MVSApiWrapper
    {
        private object _camera;
        private Type _cameraType;
        private bool _isInitialized = false;

        // 反射获取的方法
        private System.Reflection.MethodInfo _enumDevicesMethod;
        private System.Reflection.MethodInfo _createDeviceMethod;
        private System.Reflection.MethodInfo _openDeviceMethod;
        private System.Reflection.MethodInfo _closeDeviceMethod;
        private System.Reflection.MethodInfo _destroyDeviceMethod;
        private System.Reflection.MethodInfo _startGrabbingMethod;
        private System.Reflection.MethodInfo _stopGrabbingMethod;
        private System.Reflection.MethodInfo _getOneFrameMethod;
        private System.Reflection.MethodInfo _setFloatValueMethod;
        private System.Reflection.MethodInfo _getFloatValueMethod;
        private System.Reflection.MethodInfo _setIntValueMethod;
        private System.Reflection.MethodInfo _getIntValueMethod;
        private System.Reflection.MethodInfo _setBoolValueMethod;
        private System.Reflection.MethodInfo _getBoolValueMethod;
        private System.Reflection.MethodInfo _setEnumValueMethod;
        private System.Reflection.MethodInfo _getEnumValueMethod;
        private System.Reflection.MethodInfo _triggerSoftwareMethod;
        private System.Reflection.MethodInfo _registerImageCallbackMethod;
        private System.Reflection.MethodInfo _forceIpMethod;

        public bool IsInitialized => _isInitialized;
        public object Camera => _camera;

        /// <summary>
        /// 初始化 MVS API
        /// </summary>
        public bool Initialize(Type cameraType)
        {
            try
            {
                _cameraType = cameraType;
                InitializeApiMethods();
                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MVS API 初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 初始化 API 方法反射
        /// </summary>
        private void InitializeApiMethods()
        {
            var bindingFlags = System.Reflection.BindingFlags.Public | 
                              System.Reflection.BindingFlags.Instance | 
                              System.Reflection.BindingFlags.Static;

            // 设备枚举（静态方法）
            _enumDevicesMethod = _cameraType.GetMethod("MV_CC_EnumDevices_NET", bindingFlags);

            // 设备管理
            _createDeviceMethod = _cameraType.GetMethod("MV_CC_CreateDevice_NET", bindingFlags);
            _openDeviceMethod = _cameraType.GetMethod("MV_CC_OpenDevice_NET", bindingFlags);
            _closeDeviceMethod = _cameraType.GetMethod("MV_CC_CloseDevice_NET", bindingFlags);
            _destroyDeviceMethod = _cameraType.GetMethod("MV_CC_DestroyDevice_NET", bindingFlags);

            // 图像采集
            _startGrabbingMethod = _cameraType.GetMethod("MV_CC_StartGrabbing_NET", bindingFlags);
            _stopGrabbingMethod = _cameraType.GetMethod("MV_CC_StopGrabbing_NET", bindingFlags);
            _getOneFrameMethod = _cameraType.GetMethod("MV_CC_GetOneFrameTimeout_NET", bindingFlags);
            _registerImageCallbackMethod = _cameraType.GetMethod("MV_CC_RegisterImageCallBackEx_NET", bindingFlags);

            // 参数控制
            _setFloatValueMethod = _cameraType.GetMethod("MV_CC_SetFloatValue_NET", bindingFlags);
            _getFloatValueMethod = _cameraType.GetMethod("MV_CC_GetFloatValue_NET", bindingFlags);
            _setIntValueMethod = _cameraType.GetMethod("MV_CC_SetIntValue_NET", bindingFlags);
            _getIntValueMethod = _cameraType.GetMethod("MV_CC_GetIntValue_NET", bindingFlags);
            _setBoolValueMethod = _cameraType.GetMethod("MV_CC_SetBoolValue_NET", bindingFlags);
            _getBoolValueMethod = _cameraType.GetMethod("MV_CC_GetBoolValue_NET", bindingFlags);
            _setEnumValueMethod = _cameraType.GetMethod("MV_CC_SetEnumValue_NET", bindingFlags);
            _getEnumValueMethod = _cameraType.GetMethod("MV_CC_GetEnumValue_NET", bindingFlags);

            // 触发控制
            _triggerSoftwareMethod = _cameraType.GetMethod("MV_CC_TriggerSoftware_NET", bindingFlags);

            // 网络功能
            _forceIpMethod = _cameraType.GetMethod("MV_GIGE_ForceIpEx_NET", bindingFlags);
        }

        /// <summary>
        /// 枚举设备
        /// </summary>
        public List<CameraDeviceInfo> EnumerateDevices(uint deviceType = MV_GIGE_DEVICE | MV_USB_DEVICE)
        {
            var devices = new List<CameraDeviceInfo>();

            try
            {
                if (_enumDevicesMethod == null)
                {
                    throw new InvalidOperationException("设备枚举方法未找到");
                }

                // 创建设备信息列表结构
                var deviceList = new MV_CC_DEVICE_INFO_LIST();
                deviceList.nDeviceNum = 0;
                deviceList.pDeviceInfo = new IntPtr[MV_MAX_DEVICE_NUM];

                // 调用枚举设备方法
                var result = (uint)_enumDevicesMethod.Invoke(null, new object[] { deviceType, deviceList });

                if (result == MV_OK)
                {
                    // 解析设备信息
                    for (int i = 0; i < deviceList.nDeviceNum; i++)
                    {
                        if (deviceList.pDeviceInfo[i] != IntPtr.Zero)
                        {
                            var deviceInfo = Marshal.PtrToStructure<MV_CC_DEVICE_INFO>(deviceList.pDeviceInfo[i]);
                            var cameraDevice = ConvertToDeviceInfo(deviceInfo, i);
                            devices.Add(cameraDevice);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举设备失败: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// 创建设备
        /// </summary>
        public bool CreateDevice(ref MV_CC_DEVICE_INFO deviceInfo)
        {
            try
            {
                if (_createDeviceMethod == null)
                    return false;

                _camera = Activator.CreateInstance(_cameraType);
                var result = (uint)_createDeviceMethod.Invoke(_camera, new object[] { deviceInfo });
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 打开设备
        /// </summary>
        public bool OpenDevice()
        {
            try
            {
                if (_camera == null || _openDeviceMethod == null)
                    return false;

                var result = (uint)_openDeviceMethod.Invoke(_camera, null);
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"打开设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭设备
        /// </summary>
        public bool CloseDevice()
        {
            try
            {
                if (_camera == null || _closeDeviceMethod == null)
                    return false;

                var result = (uint)_closeDeviceMethod.Invoke(_camera, null);
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"关闭设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 销毁设备
        /// </summary>
        public bool DestroyDevice()
        {
            try
            {
                if (_camera == null || _destroyDeviceMethod == null)
                    return false;

                var result = (uint)_destroyDeviceMethod.Invoke(_camera, null);
                _camera = null;
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"销毁设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 开始采集
        /// </summary>
        public bool StartGrabbing()
        {
            try
            {
                if (_camera == null || _startGrabbingMethod == null)
                    return false;

                var result = (uint)_startGrabbingMethod.Invoke(_camera, null);
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"开始采集失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        public bool StopGrabbing()
        {
            try
            {
                if (_camera == null || _stopGrabbingMethod == null)
                    return false;

                var result = (uint)_stopGrabbingMethod.Invoke(_camera, null);
                return result == MV_OK;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止采集失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取一帧图像
        /// </summary>
        public Bitmap GetOneFrame(uint timeoutMs = 1000)
        {
            try
            {
                if (_camera == null || _getOneFrameMethod == null)
                    return null;

                // 分配图像缓冲区
                uint bufferSize = 1920 * 1080 * 3; // 假设最大分辨率
                IntPtr pData = Marshal.AllocHGlobal((int)bufferSize);

                try
                {
                    var frameInfo = new MV_FRAME_OUT_INFO_EX();
                    var result = (uint)_getOneFrameMethod.Invoke(_camera, 
                        new object[] { pData, bufferSize, frameInfo, timeoutMs });

                    if (result == MV_OK)
                    {
                        return ConvertToBitmap(pData, frameInfo);
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(pData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取图像失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 转换设备信息
        /// </summary>
        private CameraDeviceInfo ConvertToDeviceInfo(MV_CC_DEVICE_INFO deviceInfo, int index)
        {
            var info = new CameraDeviceInfo
            {
                DeviceIndex = index,
                ManufacturerName = "Hikvision"
            };

            if (deviceInfo.nTLayerType == MV_GIGE_DEVICE)
            {
                var gigeInfo = deviceInfo.SpecialInfo;
                info.DeviceType = DeviceType.GigE;
                info.ModelName = gigeInfo.chModelName ?? "Unknown";
                info.SerialNumber = gigeInfo.chSerialNumber ?? "Unknown";
                info.UserDefinedName = gigeInfo.chUserDefinedName ?? "";
                info.DeviceVersion = gigeInfo.chDeviceVersion ?? "";
                
                // 转换 IP 地址
                uint ip = gigeInfo.nCurrentIp;
                info.IpAddress = $"{ip & 0xFF}.{(ip >> 8) & 0xFF}.{(ip >> 16) & 0xFF}.{(ip >> 24) & 0xFF}";
                
                info.ConnectionType = "GigE";
                info.IsAccessible = true;
                info.IsOnline = true;
            }
            else if (deviceInfo.nTLayerType == MV_USB_DEVICE)
            {
                info.DeviceType = DeviceType.USB3;
                info.ConnectionType = "USB3";
                info.IsAccessible = true;
            }

            info.DeviceName = info.DisplayName;
            return info;
        }

        /// <summary>
        /// 转换为 Bitmap
        /// </summary>
        private Bitmap ConvertToBitmap(IntPtr pData, MV_FRAME_OUT_INFO_EX frameInfo)
        {
            try
            {
                int width = frameInfo.nWidth;
                int height = frameInfo.nHeight;
                
                // 根据像素格式转换
                System.Drawing.Imaging.PixelFormat pixelFormat = System.Drawing.Imaging.PixelFormat.Format24bppRgb;

                if (frameInfo.nPixelType == (uint)MvGvspPixelType.PixelType_Gvsp_Mono8)
                {
                    pixelFormat = System.Drawing.Imaging.PixelFormat.Format8bppIndexed;
                }
                else if (frameInfo.nPixelType == (uint)MvGvspPixelType.PixelType_Gvsp_RGB8_Packed)
                {
                    pixelFormat = System.Drawing.Imaging.PixelFormat.Format24bppRgb;
                }

                var bitmap = new Bitmap(width, height, pixelFormat);
                var bmpData = bitmap.LockBits(new Rectangle(0, 0, width, height), 
                    ImageLockMode.WriteOnly, pixelFormat);

                // 复制图像数据
                int stride = bmpData.Stride;
                int imageSize = stride * height;
                
                unsafe
                {
                    byte* src = (byte*)pData.ToPointer();
                    byte* dst = (byte*)bmpData.Scan0.ToPointer();
                    
                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width * 3; x++)
                        {
                            dst[y * stride + x] = src[y * width * 3 + x];
                        }
                    }
                }

                bitmap.UnlockBits(bmpData);
                return bitmap;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换图像失败: {ex.Message}");
                return null;
            }
        }
    }
}
