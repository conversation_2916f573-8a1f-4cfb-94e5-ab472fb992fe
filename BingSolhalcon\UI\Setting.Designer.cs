﻿namespace BingSolhalcon.UI
{
    partial class Setting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Setting));
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.page1 = new DevExpress.XtraTab.XtraTabPage();
            this.Edit_HatFocal = new DevExpress.XtraEditors.TextEdit();
            this.lblHatFocal = new System.Windows.Forms.Label();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.label1 = new System.Windows.Forms.Label();
            this.Edit_ReduceRow_C3 = new DevExpress.XtraEditors.TextEdit();
            this.Edit_ReduceRadius_C3 = new DevExpress.XtraEditors.TextEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.Edit_ReduceCol_C3 = new DevExpress.XtraEditors.TextEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl11 = new System.Windows.Forms.Label();
            this.Edit_ReduceRow = new DevExpress.XtraEditors.TextEdit();
            this.Edit_ReduceRadius = new DevExpress.XtraEditors.TextEdit();
            this.labelControl10 = new System.Windows.Forms.Label();
            this.labelControl9 = new System.Windows.Forms.Label();
            this.Edit_ReduceCol = new DevExpress.XtraEditors.TextEdit();
            this.cbo_decimalnum = new DevExpress.XtraEditors.ComboBoxEdit();
            this.Edit_HoleScale = new DevExpress.XtraEditors.TextEdit();
            this.lblHoleScale = new System.Windows.Forms.Label();
            this.lblDecimalnum = new System.Windows.Forms.Label();
            this.Edit_markZ = new DevExpress.XtraEditors.TextEdit();
            this.lblMarkZ = new System.Windows.Forms.Label();
            this.Edit_ThicknessZ = new DevExpress.XtraEditors.TextEdit();
            this.lblThicknessZ = new System.Windows.Forms.Label();
            this.Edit_hatZ = new DevExpress.XtraEditors.TextEdit();
            this.lblHatZ = new System.Windows.Forms.Label();
            this.Edit_centreZ = new DevExpress.XtraEditors.TextEdit();
            this.lblCentreZ = new System.Windows.Forms.Label();
            this.Edit_deepX = new DevExpress.XtraEditors.TextEdit();
            this.lblDeepX = new System.Windows.Forms.Label();
            this.Edit_deepZ = new DevExpress.XtraEditors.TextEdit();
            this.lblDeepZ = new System.Windows.Forms.Label();
            this.page2 = new DevExpress.XtraTab.XtraTabPage();
            this.Edit_wheelParam = new DevExpress.XtraEditors.TextEdit();
            this.label6 = new System.Windows.Forms.Label();
            this.cbox_sendNGPosition = new System.Windows.Forms.CheckBox();
            this.Edit_defExposureTimeHat = new DevExpress.XtraEditors.TextEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.Edit_defExposureTimeCen = new DevExpress.XtraEditors.TextEdit();
            this.label4 = new System.Windows.Forms.Label();
            this.cbox_posDegReverse = new System.Windows.Forms.CheckBox();
            this.grpCamera2Type = new DevExpress.XtraEditors.GroupControl();
            this.rbtnCamera2_CameraLink = new System.Windows.Forms.RadioButton();
            this.rbtnCamera2_GigE = new System.Windows.Forms.RadioButton();
            this.grpMEGAPHASE = new DevExpress.XtraEditors.GroupControl();
            this.rbtnMEGAPHASE_No = new System.Windows.Forms.RadioButton();
            this.rbtnMEGAPHASE_Yes = new System.Windows.Forms.RadioButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_HatFocal.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRow_C3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRadius_C3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceCol_C3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRow.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRadius.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceCol.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbo_decimalnum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_HoleScale.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_markZ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ThicknessZ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_hatZ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_centreZ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_deepX.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_deepZ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_wheelParam.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_defExposureTimeHat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_defExposureTimeCen.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCamera2Type)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpMEGAPHASE)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("xtraTabControl1.Appearance.BackColor")));
            this.xtraTabControl1.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("xtraTabControl1.Appearance.ForeColor")));
            this.xtraTabControl1.Appearance.Options.UseBackColor = true;
            this.xtraTabControl1.Appearance.Options.UseForeColor = true;
            this.xtraTabControl1.LookAndFeel.SkinMaskColor = System.Drawing.Color.Transparent;
            this.xtraTabControl1.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.Transparent;
            this.xtraTabControl1.LookAndFeel.SkinName = "Office 2010 Blue";
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.page1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.page1,
            this.page2});
            // 
            // page1
            // 
            this.page1.Appearance.PageClient.BackColor = ((System.Drawing.Color)(resources.GetObject("page1.Appearance.PageClient.BackColor")));
            this.page1.Appearance.PageClient.Options.UseBackColor = true;
            this.page1.Controls.Add(this.Edit_HatFocal);
            this.page1.Controls.Add(this.lblHatFocal);
            this.page1.Controls.Add(this.groupControl2);
            this.page1.Controls.Add(this.groupControl1);
            this.page1.Controls.Add(this.cbo_decimalnum);
            this.page1.Controls.Add(this.Edit_HoleScale);
            this.page1.Controls.Add(this.lblHoleScale);
            this.page1.Controls.Add(this.lblDecimalnum);
            this.page1.Controls.Add(this.Edit_markZ);
            this.page1.Controls.Add(this.lblMarkZ);
            this.page1.Controls.Add(this.Edit_ThicknessZ);
            this.page1.Controls.Add(this.lblThicknessZ);
            this.page1.Controls.Add(this.Edit_hatZ);
            this.page1.Controls.Add(this.lblHatZ);
            this.page1.Controls.Add(this.Edit_centreZ);
            this.page1.Controls.Add(this.lblCentreZ);
            this.page1.Controls.Add(this.Edit_deepX);
            this.page1.Controls.Add(this.lblDeepX);
            this.page1.Controls.Add(this.Edit_deepZ);
            this.page1.Controls.Add(this.lblDeepZ);
            resources.ApplyResources(this.page1, "page1");
            this.page1.Name = "page1";
            // 
            // Edit_HatFocal
            // 
            resources.ApplyResources(this.Edit_HatFocal, "Edit_HatFocal");
            this.Edit_HatFocal.Name = "Edit_HatFocal";
            // 
            // lblHatFocal
            // 
            resources.ApplyResources(this.lblHatFocal, "lblHatFocal");
            this.lblHatFocal.Name = "lblHatFocal";
            // 
            // groupControl2
            // 
            resources.ApplyResources(this.groupControl2, "groupControl2");
            this.groupControl2.Controls.Add(this.label1);
            this.groupControl2.Controls.Add(this.Edit_ReduceRow_C3);
            this.groupControl2.Controls.Add(this.Edit_ReduceRadius_C3);
            this.groupControl2.Controls.Add(this.label2);
            this.groupControl2.Controls.Add(this.label3);
            this.groupControl2.Controls.Add(this.Edit_ReduceCol_C3);
            this.groupControl2.Name = "groupControl2";
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // Edit_ReduceRow_C3
            // 
            resources.ApplyResources(this.Edit_ReduceRow_C3, "Edit_ReduceRow_C3");
            this.Edit_ReduceRow_C3.Name = "Edit_ReduceRow_C3";
            // 
            // Edit_ReduceRadius_C3
            // 
            resources.ApplyResources(this.Edit_ReduceRadius_C3, "Edit_ReduceRadius_C3");
            this.Edit_ReduceRadius_C3.Name = "Edit_ReduceRadius_C3";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // Edit_ReduceCol_C3
            // 
            resources.ApplyResources(this.Edit_ReduceCol_C3, "Edit_ReduceCol_C3");
            this.Edit_ReduceCol_C3.Name = "Edit_ReduceCol_C3";
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.Controls.Add(this.labelControl11);
            this.groupControl1.Controls.Add(this.Edit_ReduceRow);
            this.groupControl1.Controls.Add(this.Edit_ReduceRadius);
            this.groupControl1.Controls.Add(this.labelControl10);
            this.groupControl1.Controls.Add(this.labelControl9);
            this.groupControl1.Controls.Add(this.Edit_ReduceCol);
            this.groupControl1.Name = "groupControl1";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // Edit_ReduceRow
            // 
            resources.ApplyResources(this.Edit_ReduceRow, "Edit_ReduceRow");
            this.Edit_ReduceRow.Name = "Edit_ReduceRow";
            // 
            // Edit_ReduceRadius
            // 
            resources.ApplyResources(this.Edit_ReduceRadius, "Edit_ReduceRadius");
            this.Edit_ReduceRadius.Name = "Edit_ReduceRadius";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // Edit_ReduceCol
            // 
            resources.ApplyResources(this.Edit_ReduceCol, "Edit_ReduceCol");
            this.Edit_ReduceCol.Name = "Edit_ReduceCol";
            // 
            // cbo_decimalnum
            // 
            resources.ApplyResources(this.cbo_decimalnum, "cbo_decimalnum");
            this.cbo_decimalnum.Name = "cbo_decimalnum";
            // 
            // 
            // 
            this.cbo_decimalnum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cbo_decimalnum.Properties.Buttons"))))});
            this.cbo_decimalnum.Properties.Items.AddRange(new object[] {
            resources.GetString("cbo_decimalnum.Properties.Items"),
            resources.GetString("cbo_decimalnum.Properties.Items1"),
            resources.GetString("cbo_decimalnum.Properties.Items2")});
            // 
            // Edit_HoleScale
            // 
            resources.ApplyResources(this.Edit_HoleScale, "Edit_HoleScale");
            this.Edit_HoleScale.Name = "Edit_HoleScale";
            // 
            // lblHoleScale
            // 
            resources.ApplyResources(this.lblHoleScale, "lblHoleScale");
            this.lblHoleScale.Name = "lblHoleScale";
            // 
            // lblDecimalnum
            // 
            resources.ApplyResources(this.lblDecimalnum, "lblDecimalnum");
            this.lblDecimalnum.Name = "lblDecimalnum";
            // 
            // Edit_markZ
            // 
            resources.ApplyResources(this.Edit_markZ, "Edit_markZ");
            this.Edit_markZ.Name = "Edit_markZ";
            // 
            // lblMarkZ
            // 
            resources.ApplyResources(this.lblMarkZ, "lblMarkZ");
            this.lblMarkZ.Name = "lblMarkZ";
            // 
            // Edit_ThicknessZ
            // 
            resources.ApplyResources(this.Edit_ThicknessZ, "Edit_ThicknessZ");
            this.Edit_ThicknessZ.Name = "Edit_ThicknessZ";
            // 
            // lblThicknessZ
            // 
            resources.ApplyResources(this.lblThicknessZ, "lblThicknessZ");
            this.lblThicknessZ.Name = "lblThicknessZ";
            // 
            // Edit_hatZ
            // 
            resources.ApplyResources(this.Edit_hatZ, "Edit_hatZ");
            this.Edit_hatZ.Name = "Edit_hatZ";
            // 
            // lblHatZ
            // 
            resources.ApplyResources(this.lblHatZ, "lblHatZ");
            this.lblHatZ.Name = "lblHatZ";
            // 
            // Edit_centreZ
            // 
            resources.ApplyResources(this.Edit_centreZ, "Edit_centreZ");
            this.Edit_centreZ.Name = "Edit_centreZ";
            // 
            // lblCentreZ
            // 
            resources.ApplyResources(this.lblCentreZ, "lblCentreZ");
            this.lblCentreZ.Name = "lblCentreZ";
            // 
            // Edit_deepX
            // 
            resources.ApplyResources(this.Edit_deepX, "Edit_deepX");
            this.Edit_deepX.Name = "Edit_deepX";
            // 
            // lblDeepX
            // 
            resources.ApplyResources(this.lblDeepX, "lblDeepX");
            this.lblDeepX.Name = "lblDeepX";
            // 
            // Edit_deepZ
            // 
            resources.ApplyResources(this.Edit_deepZ, "Edit_deepZ");
            this.Edit_deepZ.Name = "Edit_deepZ";
            // 
            // lblDeepZ
            // 
            resources.ApplyResources(this.lblDeepZ, "lblDeepZ");
            this.lblDeepZ.Name = "lblDeepZ";
            // 
            // page2
            // 
            this.page2.Controls.Add(this.Edit_wheelParam);
            this.page2.Controls.Add(this.label6);
            this.page2.Controls.Add(this.cbox_sendNGPosition);
            this.page2.Controls.Add(this.Edit_defExposureTimeHat);
            this.page2.Controls.Add(this.label5);
            this.page2.Controls.Add(this.Edit_defExposureTimeCen);
            this.page2.Controls.Add(this.label4);
            this.page2.Controls.Add(this.cbox_posDegReverse);
            this.page2.Controls.Add(this.grpCamera2Type);
            this.page2.Controls.Add(this.grpMEGAPHASE);
            resources.ApplyResources(this.page2, "page2");
            this.page2.Name = "page2";
            // 
            // Edit_wheelParam
            // 
            resources.ApplyResources(this.Edit_wheelParam, "Edit_wheelParam");
            this.Edit_wheelParam.Name = "Edit_wheelParam";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // cbox_sendNGPosition
            // 
            resources.ApplyResources(this.cbox_sendNGPosition, "cbox_sendNGPosition");
            this.cbox_sendNGPosition.Name = "cbox_sendNGPosition";
            this.cbox_sendNGPosition.UseVisualStyleBackColor = true;
            // 
            // Edit_defExposureTimeHat
            // 
            resources.ApplyResources(this.Edit_defExposureTimeHat, "Edit_defExposureTimeHat");
            this.Edit_defExposureTimeHat.Name = "Edit_defExposureTimeHat";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // Edit_defExposureTimeCen
            // 
            resources.ApplyResources(this.Edit_defExposureTimeCen, "Edit_defExposureTimeCen");
            this.Edit_defExposureTimeCen.Name = "Edit_defExposureTimeCen";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // cbox_posDegReverse
            // 
            resources.ApplyResources(this.cbox_posDegReverse, "cbox_posDegReverse");
            this.cbox_posDegReverse.Name = "cbox_posDegReverse";
            this.cbox_posDegReverse.UseVisualStyleBackColor = true;
            // 
            // grpCamera2Type
            // 
            this.grpCamera2Type.Controls.Add(this.rbtnCamera2_CameraLink);
            this.grpCamera2Type.Controls.Add(this.rbtnCamera2_GigE);
            resources.ApplyResources(this.grpCamera2Type, "grpCamera2Type");
            this.grpCamera2Type.Name = "grpCamera2Type";
            // 
            // rbtnCamera2_CameraLink
            // 
            resources.ApplyResources(this.rbtnCamera2_CameraLink, "rbtnCamera2_CameraLink");
            this.rbtnCamera2_CameraLink.Name = "rbtnCamera2_CameraLink";
            this.rbtnCamera2_CameraLink.UseVisualStyleBackColor = true;
            // 
            // rbtnCamera2_GigE
            // 
            resources.ApplyResources(this.rbtnCamera2_GigE, "rbtnCamera2_GigE");
            this.rbtnCamera2_GigE.Checked = true;
            this.rbtnCamera2_GigE.Name = "rbtnCamera2_GigE";
            this.rbtnCamera2_GigE.TabStop = true;
            this.rbtnCamera2_GigE.UseVisualStyleBackColor = true;
            // 
            // grpMEGAPHASE
            // 
            this.grpMEGAPHASE.Controls.Add(this.rbtnMEGAPHASE_No);
            this.grpMEGAPHASE.Controls.Add(this.rbtnMEGAPHASE_Yes);
            resources.ApplyResources(this.grpMEGAPHASE, "grpMEGAPHASE");
            this.grpMEGAPHASE.Name = "grpMEGAPHASE";
            // 
            // rbtnMEGAPHASE_No
            // 
            resources.ApplyResources(this.rbtnMEGAPHASE_No, "rbtnMEGAPHASE_No");
            this.rbtnMEGAPHASE_No.Checked = true;
            this.rbtnMEGAPHASE_No.Name = "rbtnMEGAPHASE_No";
            this.rbtnMEGAPHASE_No.TabStop = true;
            this.rbtnMEGAPHASE_No.UseVisualStyleBackColor = true;
            // 
            // rbtnMEGAPHASE_Yes
            // 
            resources.ApplyResources(this.rbtnMEGAPHASE_Yes, "rbtnMEGAPHASE_Yes");
            this.rbtnMEGAPHASE_Yes.Name = "rbtnMEGAPHASE_Yes";
            this.rbtnMEGAPHASE_Yes.UseVisualStyleBackColor = true;
            // 
            // btnSave
            // 
            resources.ApplyResources(this.btnSave, "btnSave");
            this.btnSave.Name = "btnSave";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            resources.ApplyResources(this.btnCancel, "btnCancel");
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // Setting
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.xtraTabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.LookAndFeel.SkinName = "Office 2010 Blue";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Name = "Setting";
            this.Load += new System.EventHandler(this.Setting_Load);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Edit_HatFocal.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRow_C3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRadius_C3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceCol_C3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRow.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceRadius.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ReduceCol.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbo_decimalnum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_HoleScale.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_markZ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_ThicknessZ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_hatZ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_centreZ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_deepX.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_deepZ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_wheelParam.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_defExposureTimeHat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Edit_defExposureTimeCen.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCamera2Type)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpMEGAPHASE)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage page2;
        private DevExpress.XtraTab.XtraTabPage page1;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.GroupControl grpCamera2Type;
        private System.Windows.Forms.RadioButton rbtnCamera2_CameraLink;
        private System.Windows.Forms.RadioButton rbtnCamera2_GigE;
        private DevExpress.XtraEditors.GroupControl grpMEGAPHASE;
        private System.Windows.Forms.RadioButton rbtnMEGAPHASE_No;
        private System.Windows.Forms.RadioButton rbtnMEGAPHASE_Yes;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceRadius;
        private System.Windows.Forms.Label labelControl9;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceCol;
        private System.Windows.Forms.Label labelControl10;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceRow;
        private System.Windows.Forms.Label labelControl11;
        private DevExpress.XtraEditors.TextEdit Edit_HoleScale;
        private System.Windows.Forms.Label lblHoleScale;
        private System.Windows.Forms.Label lblDecimalnum;
        private DevExpress.XtraEditors.TextEdit Edit_markZ;
        private System.Windows.Forms.Label lblMarkZ;
        private DevExpress.XtraEditors.TextEdit Edit_ThicknessZ;
        private System.Windows.Forms.Label lblThicknessZ;
        private DevExpress.XtraEditors.TextEdit Edit_hatZ;
        private System.Windows.Forms.Label lblHatZ;
        private DevExpress.XtraEditors.TextEdit Edit_centreZ;
        private System.Windows.Forms.Label lblCentreZ;
        private DevExpress.XtraEditors.TextEdit Edit_deepX;
        private System.Windows.Forms.Label lblDeepX;
        private DevExpress.XtraEditors.TextEdit Edit_deepZ;
        private System.Windows.Forms.Label lblDeepZ;
        private DevExpress.XtraEditors.ComboBoxEdit cbo_decimalnum;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceRow_C3;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceRadius_C3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.TextEdit Edit_ReduceCol_C3;
        private DevExpress.XtraEditors.TextEdit Edit_HatFocal;
        private System.Windows.Forms.Label lblHatFocal;
        private System.Windows.Forms.CheckBox cbox_posDegReverse;
        private DevExpress.XtraEditors.TextEdit Edit_defExposureTimeHat;
        private System.Windows.Forms.Label label5;
        private DevExpress.XtraEditors.TextEdit Edit_defExposureTimeCen;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.CheckBox cbox_sendNGPosition;
        private DevExpress.XtraEditors.TextEdit Edit_wheelParam;
        private System.Windows.Forms.Label label6;
    }
}