﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Data;
using System.Threading.Tasks;
using GemBox.ExcelLite;
using System.Data.SqlClient;
using System.Threading;

namespace BingSolhalcon
{
    public partial class DataBase : Form
    {
        SQlFun m_sqlfun;

        public delegate void delegateGetDatabasename(ref string databasename,ref string numberofdecimal);
        ComponentResourceManager res = new ComponentResourceManager(typeof(DataBase)); //自定义资源字段

        public event delegateGetDatabasename evenGetDatabasename;
        string m_databasename,numberofdecimal, constr;



        private Dictionary<string, string[]> HeaderLangMap = new Dictionary<string, string[]> {
            { "序号", new string[]{ "id", "id", "id" } },
            { "轮毂型号", new string[]{ "Hub model", "Modèle de moyeu", "Modelo Hub" } },
            { "帽槽深度检测值1", new string[]{ "Cap groove depth detection value 1", "Valeur de détection de profondeur de fente de bouchon 1", "Valor de detección de profundidad de la ranura del sombrero 1" } },
            { "帽槽深度检测值2", new string[]{ "Cap groove depth detection value 2", "Valeur de détection de profondeur de fente de bouchon 2", "Valor de detección de profundidad de la ranura del sombrero 2" } },
            { "帽槽深度偏差", new string[]{ "Depth deviation of cap groove", "Déviation de la profondeur de la fente du chapeau", "Desviación de la profundidad de la ranura del sombrero" } },
            { "帽槽深度检测结果", new string[]{ "Cap groove depth inspection results", "Résultats de la détection de profondeur de rainure de chapeau", "Resultados de la prueba de profundidad de la ranura del sombrero" } },
            { "中心孔检测值", new string[]{ "Center hole detection value", "Valeur de détection du trou central", "Valor de detección del agujero central" } },
            { "中心孔偏差", new string[]{ "Center hole deviation", "Déviation du trou central", "Desviación del agujero central" } },
            { "中心孔检测结果", new string[]{ "Center hole inspection results", "Résultats de la détection du trou central", "Resultados de la prueba del agujero central" } },
            { "螺栓孔1检测值", new string[]{ "Detection value of bolt hole 1", "Valeur de détection du trou de boulon 1", "Valor de detección del agujero del perno 1" } },
            { "螺栓孔1偏差", new string[]{ "Deviation of bolt hole 1", "Trou de boulon 1 déviation", "Desviación del agujero del perno 1" } },
            { "螺栓孔1检测结果", new string[]{ "Inspection results of bolt hole 1", "Résultats de la détection de trou de boulon 1", "Resultados de la prueba del agujero del perno 1" } },
            { "螺栓孔2检测值", new string[]{ "Detection value of bolt hole 2", "Valeur de détection du trou de boulon 2", "Valor de detección del agujero del perno 2" } },
            { "螺栓孔2偏差", new string[]{ "Deviation of bolt hole 2", "Trou de boulon 2 déviations", "Desviación del agujero del perno 2" } },
            { "螺栓孔2检测结果", new string[]{ "Inspection Results of Bolt Hole 2", "Résultats de la détection de trou de boulon 2", "Resultados de la prueba del agujero del perno 2" } },
            { "螺栓孔3检测值", new string[]{ "Bolt hole 3 detection value", "Valeur de détection du trou de boulon 3", "Valor de detección del agujero del perno 3" } },
            { "螺栓孔3偏差", new string[]{ "Deviation of bolt hole 3", "Trou de boulon 3 déviations", "Desviación del agujero del perno 3" } },
            { "螺栓孔3检测结果", new string[]{ "Inspection Results of Bolt Hole 3", "Résultats de la détection de trou de boulon 3", "Resultados de la prueba del agujero del perno 3" } },
            { "螺栓孔4检测值", new string[]{ "Bolt hole 4 detection value", "Valeur de détection du trou de boulon 4", "Valor de detección del agujero del perno 4" } },
            { "螺栓孔4偏差", new string[]{ "Deviation of bolt hole 4", "Trou de boulon 4 déviations", "Desviación del agujero del perno 4" } },
            { "螺栓孔4检测结果", new string[]{ "Inspection Results of Bolt Hole 4", "Résultats de détection de trou de boulon 4", "Resultados de la prueba del agujero del perno 4" } },
            { "螺栓孔5检测值", new string[]{ "Bolt hole 5 detection value", "Valeur de détection du trou de boulon 5", "Valor de detección del agujero del perno 5" } },
            { "螺栓孔5偏差", new string[]{ "Deviation of bolt hole 5", "Trous de boulon 5 déviations", "Desviación del agujero del perno 5" } },
            { "螺栓孔5检测结果", new string[]{ "Inspection Results of Bolt Hole 5", "Résultats de la détection de trou de boulon 5", "Resultados de la prueba del agujero del perno 5" } },
            { "螺栓孔6检测值", new string[]{ "Detection value of bolt hole 6", "Valeur de détection du trou de boulon 6", "Valor de detección del agujero del perno 6" } },
            { "螺栓孔6偏差", new string[]{ "Deviation of bolt hole 6", "Trous de boulon 6 déviations", "Desviación del agujero del perno 6" } },
            { "螺栓孔6检测结果", new string[]{ "Inspection Results of Bolt Hole 6", "Résultats de la détection de trou de boulon 6", "Resultados de la prueba del agujero del perno 6" } },
            { "螺栓孔7检测值", new string[]{ "Bolt hole 7 detection value", "Valeur de détection du trou de boulon 7", "Valor de detección del agujero del perno 7" } },
            { "螺栓孔7偏差", new string[]{ "Deviation of bolt hole 7", "Trous de boulon 7 déviations", "Desviación del agujero del perno 7" } },
            { "螺栓孔7检测结果", new string[]{ "Inspection results of bolt hole 7", "Résultats de la détection de trou de boulon 7", "Resultados de la prueba del agujero del perno 7" } },
            { "螺栓孔8检测值", new string[]{ "Bolt hole 8 detection value", "Valeur de détection du trou de boulon 8", "Valor de detección del agujero del perno 8" } },
            { "螺栓孔8偏差", new string[]{ "Deviation of bolt hole 8", "Trous de boulon 8 déviations", "Desviación del agujero del perno 8" } },
            { "螺栓孔8检测结果", new string[]{ "Inspection results of bolt hole 8", "Résultats de la détection de trou de boulon 8", "Resultados de la prueba del agujero del perno 8" } },
            { "螺栓孔1直径", new string[]{ "Bolt hole 1 diameter", "Trou de boulon 1 diamètre", "Diámetro del agujero del perno 1" } },
            { "螺栓孔1直径偏差", new string[]{ "Diameter deviation of bolt hole 1", "Trou de boulon 1 déviation du diamètre", "Desviación del diámetro del agujero del perno 1" } },
            { "螺栓孔1直径检测结果", new string[]{ "Diameter Inspection Results of Bolt Hole 1", "Résultats de la détection de diamètre de trou de boulon 1", "Resultados de la prueba de diámetro del agujero del perno 1" } },
            { "螺栓孔2直径", new string[]{ "Bolt hole 2 diameter", "Trou de boulon 2 diamètre", "Diámetro del agujero del perno 2" } },
            { "螺栓孔2直径偏差", new string[]{ "Diameter deviation of bolt hole 2", "Déviation du diamètre du trou de boulon 2", "Desviación del diámetro del agujero del perno 2" } },
            { "螺栓孔2直径检测结果", new string[]{ "Inspection Results of Bolt Hole 2 Diameter", "Résultats de la détection du diamètre du trou de boulon 2", "Resultados de la prueba de diámetro del agujero del perno 2" } },
            { "螺栓孔3直径", new string[]{ "Bolt hole 3 diameter", "Trou de boulon 3 diamètre", "Diámetro del agujero del perno 3" } },
            { "螺栓孔3直径偏差", new string[]{ "Diameter deviation of bolt hole 3", "Trou de boulon 3 déviation du diamètre", "Desviación del diámetro del agujero del perno 3" } },
            { "螺栓孔3直径检测结果", new string[]{ "Inspection Results of Bolt Hole 3 Diameter", "Résultats de la détection du diamètre du trou de boulon 3", "Resultados de la prueba de diámetro del agujero del perno 3" } },
            { "螺栓孔4直径", new string[]{ "Bolt hole 4 diameter", "Trou de boulon 4 diamètre", "Diámetro del agujero del perno 4" } },
            { "螺栓孔4直径偏差", new string[]{ "Diameter deviation of bolt hole 4", "Déviation du diamètre du trou de boulon 4", "Desviación del diámetro del agujero del perno 4" } },
            { "螺栓孔4直径检测结果", new string[]{ "Inspection Results of Bolt Hole 4 Diameter", "Résultats de la détection du diamètre du trou de boulon 4", "Resultados de la prueba de diámetro del agujero del perno 4" } },
            { "螺栓孔5直径", new string[]{ "Bolt hole 5 diameter", "Trou de boulon 5 diamètre", "Diámetro del agujero del perno 5" } },
            { "螺栓孔5直径偏差", new string[]{ "Diameter deviation of bolt hole 5", "Déviation du diamètre du trou de boulon 5", "Desviación del diámetro del agujero del perno 5" } },
            { "螺栓孔5直径检测结果", new string[]{ "Inspection Results of Bolt Hole 5 Diameter", "Résultats de la détection de diamètre de trou de boulon 5", "Resultados de la prueba de diámetro del agujero del perno 5" } },
            { "螺栓孔6直径", new string[]{ "Bolt hole 6 diameter", "Trou de boulon 6 diamètre", "Diámetro del agujero del perno 6" } },
            { "螺栓孔6直径偏差", new string[]{ "Diameter deviation of bolt hole 6", "Déviation du diamètre du trou de boulon 6", "Desviación del diámetro del agujero del perno 6" } },
            { "螺栓孔6直径检测结果", new string[]{ "Inspection Results of Bolt Hole 6 Diameter", "Résultats de la détection du diamètre du trou de boulon 6", "Resultados de la prueba de diámetro del agujero del perno 6" } },
            { "螺栓孔7直径", new string[]{ "Bolt hole 7 diameter", "Trou de boulon 7 diamètre", "Diámetro del agujero del perno 7" } },
            { "螺栓孔7直径偏差", new string[]{ "Diameter deviation of bolt hole 7", "Déviation du diamètre du trou de boulon 7", "Desviación del diámetro del agujero del perno 7" } },
            { "螺栓孔7直径检测结果", new string[]{ "Inspection Results of Bolt Hole 7 Diameter", "Résultats de la détection de diamètre de trou de boulon 7", "Resultados de la prueba de diámetro del agujero del perno 7" } },
            { "螺栓孔8直径", new string[]{ "Bolt hole 8 diameter", "Trou de boulon 8 diamètre", "Diámetro del agujero del perno 8" } },
            { "螺栓孔8直径偏差", new string[]{ "Diameter deviation of bolt hole 8", "Trou de boulon 8 déviation du diamètre", "Desviación del diámetro del agujero del perno 8" } },
            { "螺栓孔8直径检测结果", new string[]{ "Inspection Results of Bolt Hole 8 Diameter", "Résultats de la détection de diamètre de trou de boulon 8", "Resultados de la prueba de diámetro del agujero del perno 8" } },
            { "螺栓孔1厚度", new string[]{ "Bolt hole 1 thickness", "Trou de boulon 1 épaisseur", "Espesor del agujero del perno 1" } },
            { "螺栓孔1厚度偏差", new string[]{ "Thickness deviation of bolt hole 1", "Trou de boulon 1 déviation d'épaisseur", "Desviación del espesor del agujero del perno 1" } },
            { "螺栓孔1厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 1", "Résultats de la détection d'épaisseur de trou de boulon 1", "Resultados de la prueba de espesor del agujero del perno 1" } },
            { "螺栓孔2厚度", new string[]{ "Bolt hole 2 thickness", "Trou de boulon 2 épaisseur", "Espesor del agujero del perno 2" } },
            { "螺栓孔2厚度偏差", new string[]{ "Thickness deviation of bolt hole 2", "Trou de boulon 2 déviation d'épaisseur", "Desviación del espesor del agujero del perno 2" } },
            { "螺栓孔2厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 2", "Résultats de la détection de l'épaisseur du trou de boulon 2", "Resultados de la prueba de espesor del agujero del perno 2" } },
            { "螺栓孔3厚度", new string[]{ "Bolt hole 3 thickness", "Trou de boulon 3 épaisseur", "Espesor del agujero del perno 3" } },
            { "螺栓孔3厚度偏差", new string[]{ "Thickness deviation of bolt hole 3", "Trou de boulon 3 déviation d'épaisseur", "Desviación del espesor del agujero del perno 3" } },
            { "螺栓孔3厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 3", "Résultats de la détection d'épaisseur de trou de boulon 3", "Resultados de la prueba de espesor del agujero del perno 3" } },
            { "螺栓孔4厚度", new string[]{ "Bolt hole 4 thickness", "Épaisseur du trou de boulon 4", "Espesor del agujero del perno 4" } },
            { "螺栓孔4厚度偏差", new string[]{ "Thickness deviation of bolt hole 4", "Trou de boulon 4 déviation d'épaisseur", "Desviación del espesor del agujero del perno 4" } },
            { "螺栓孔4厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 4", "Résultats de la détection de l'épaisseur du trou de boulon 4", "Resultados de la prueba de espesor del agujero del perno 4" } },
            { "螺栓孔5厚度", new string[]{ "Bolt hole 5 thickness", "Trou de boulon 5 épaisseur", "Espesor del agujero del perno 5" } },
            { "螺栓孔5厚度偏差", new string[]{ "Thickness deviation of bolt hole 5", "Trou de boulon 5 déviation d'épaisseur", "Desviación del espesor del agujero del perno 5" } },
            { "螺栓孔5厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 5", "Résultats de la détection d'épaisseur de trou de boulon 5", "Resultados de la prueba de espesor del agujero del perno 5" } },
            { "螺栓孔6厚度", new string[]{ "Bolt hole 6 thickness", "Trou de boulon 6 épaisseur", "Espesor del agujero del perno 6" } },
            { "螺栓孔6厚度偏差", new string[]{ "Thickness deviation of bolt hole 6", "Trou de boulon 6 déviation d'épaisseur", "Desviación del espesor del agujero del perno 6" } },
            { "螺栓孔6厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 6", "Résultats de la détection d'épaisseur de trou de boulon 6", "Resultados de la prueba de espesor del agujero del perno 6" } },
            { "螺栓孔7厚度", new string[]{ "Bolt hole 7 thickness", "Trou de boulon 7 épaisseur", "Espesor del agujero del perno 7" } },
            { "螺栓孔7厚度偏差", new string[]{ "Thickness deviation of bolt hole 7", "Trou de boulon 7 déviation d'épaisseur", "Desviación del espesor del agujero del perno 7" } },
            { "螺栓孔7厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 7", "Résultats de la détection d'épaisseur de trou de boulon 7", "Resultados de la prueba de espesor del agujero del perno 7" } },
            { "螺栓孔8厚度", new string[]{ "Bolt hole 8 thickness", "Trou de boulon 8 épaisseur", "Espesor del agujero del perno 8" } },
            { "螺栓孔8厚度偏差", new string[]{ "Thickness deviation of bolt hole 8", "Trou de boulon 8 déviation d'épaisseur", "Desviación del espesor del agujero del perno 8" } },
            { "螺栓孔8厚度检测结果", new string[]{ "Thickness Testing Results of Bolt Hole 8", "Résultats de la détection d'épaisseur de trou de boulon 8", "Resultados de la prueba de espesor del agujero del perno 8" } },
            { "帽止口检测值", new string[]{ "Cap seam detection value", "Valeur de détection de bouchon", "Valor de detección del tapón" } },
            { "帽止口偏差", new string[]{ "Cap stop deviation", "Déviation de bouchon", "Desviación del tapón" } },
            { "帽止口检测结果", new string[]{ "Test results of cap seam", "Résultats de la détection de bouchon", "Resultados de la prueba del tapón" } },
            { "最后结果", new string[]{ "Final result", "Résultats finaux", "Resultados finales" } },
            { "时间", new string[]{ "Time", "Le temps", "Tiempo" } }
        };



        public DataBase()
        {
           
            InitializeComponent();
           // dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
           
            btn_connectdatabase.BackColor = Color.Red;
            m_sqlfun = new SQlFun();
            
        }

        public string m_database = "MyDB";
        private void FormLoad(object sender, EventArgs e)
        {
            //new Thread(() => {
            //    Thread.Sleep(3000);
            //    //加载默认语言
            //    MultiLanguage multilanguage = new MultiLanguage();
            //    multilanguage.LoadDefaultLanguage(this, typeof(DataBase));
            //}).Start();


            evenGetDatabasename(ref m_databasename,ref numberofdecimal);
             constr = @"Server=" + m_databasename + ";Database=" + m_database + ";Trusted_Connection=SSPI";
           // m_sqlfun.connection(constr);
            btn_readdatabase.Enabled = false;
            btn_check.Enabled = false;
            btn_queryWeight.Enabled = false;
            btn_deletedatabase.Enabled = false;
        }

        private void Form1_Resize(object sender, EventArgs e)
        {
        }

        private void btn_connectdatabase_Click(object sender, EventArgs e)
        {
            Task t_task = Task.Factory.StartNew(() =>
            {
                m_sqlfun.connection(constr);
                if (m_sqlfun.Sql_open())
                {
                    btn_connectdatabase.BackColor = Color.Green;
                    btn_connectdatabase.Text = res.GetString("success");
                    btn_readdatabase.Enabled = true;
                    btn_check.Enabled = true;
                    btn_queryWeight.Enabled = true;

                    btn_deletedatabase.Enabled = false;
                }
                else
                {
 
                     MessageBoxEX.Show(res.GetString("dbnotCon"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    btn_connectdatabase.BackColor = Color.Red;
                }
                m_sqlfun.conn.Close();
            });
               
        }

        private void btn_readdatabase_Click(object sender, EventArgs e)
        {
            Task t_task = Task.Factory.StartNew(() =>
            {
                m_sqlfun.connection(constr);
                m_sqlfun.Sql_open();
                if (m_sqlfun.conn.State == ConnectionState.Open)
                {
                    dataGridView1.Invoke(new MethodInvoker(delegate
                    {
                        // m_sqlfun.disp_datagridview("轮型识别1", dataGridView1, this.Width);
                        m_sqlfun.disp3days_datagridview("检测结果", dataGridView1, this.Width);
                        UpdateHeaderLanguage();
                    }));
                }
                m_sqlfun.conn.Close();

            });
        }


        private void UpdateHeaderLanguage()
        {
            int langIndex = -1;
            switch(Properties.Settings.Default.DefaultLanguage)
            {
                case "en":
                    langIndex = 0;
                    break;
                case "fr":
                    langIndex = 1;
                    break;
                case "es":
                    langIndex = 2;
                    break;
            }

            for (int i = 0; i < dataGridView1.Columns.Count; ++i)
            {
                if(langIndex >= 0 && HeaderLangMap.ContainsKey(dataGridView1.Columns[i].HeaderText))
                {
                    if (langIndex == 1)
                    {
                        dataGridView1.Columns[i].HeaderText = HeaderLangMap[dataGridView1.Columns[i].HeaderText][langIndex] + "\n" + dataGridView1.Columns[i].HeaderText;
                    }
                    else
                    {
                        dataGridView1.Columns[i].HeaderText = HeaderLangMap[dataGridView1.Columns[i].HeaderText][langIndex];
                    }
                }
            }
        }



        private void btn_deletedatabase_Click(object sender, EventArgs e)
        {
            m_sqlfun.connection(constr);
            m_sqlfun.Sql_open();
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                if(dataGridView1.CurrentRow==null)
                {
                    MessageBoxEX.Show(res.GetString("selectDelet"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                }
                else
                {
                    string str = dataGridView1.CurrentRow.Index.ToString();

                    int currentindex = int.Parse(str);

                    int index = int.Parse(dataGridView1.Rows[currentindex].Cells[0].Value.ToString());

                    m_sqlfun.Sql_deleterow(index);
                    m_sqlfun.disp_datagridview("轮型识别1", dataGridView1, this.Width);
                }
                    
               
            }
            m_sqlfun.conn.Close();
        }

        private void btn_check_Click(object sender, EventArgs e)
        {

            Task t_task = Task.Factory.StartNew(() =>
            {
                m_sqlfun.connection(constr);
                m_sqlfun.Sql_open();
                string str1 = combine(dateTimePicker2, cbox_timeH1, cbox_timeM1, cbox_timeS1);

                string str2 = combine(dateTimePicker3, cbox_timeH2, cbox_timeM2, cbox_timeS2);


                if (m_sqlfun.conn.State == ConnectionState.Open)
                {
                    dataGridView1.Invoke(new MethodInvoker(delegate
                    {
                        string sql = "select * from 检测结果 where 时间 between '" + str1 + "' and '" + str2;
                        if (textBox_wheeltype.Text.Equals(""))
                        {
                            sql += "' order by 序号";
                        }
                        else
                        {
                            sql +=  "' and 轮毂型号='" + textBox_wheeltype.Text + "' order by 序号";
                        }

                        var sda = new SqlDataAdapter();
                        var cmd = new SqlCommand(sql, m_sqlfun.conn);
                        sda.SelectCommand = cmd;
                        var dTable = new DataTable();
                        sda.Fill(dTable);
                        dataGridView1.DataSource = dTable;
                    }));

                }
                m_sqlfun.conn.Close();
                UpdateHeaderLanguage();
            });

        }

        private void btn_queryWeight_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(constr))
                {
                    conn.Open();
                    string startTime = combine(dateTimePicker2, cbox_timeH1, cbox_timeM1, cbox_timeS1);

                    string endTime = combine(dateTimePicker3, cbox_timeH2, cbox_timeM2, cbox_timeS2);
                    

                    if (conn.State != ConnectionState.Open)
                    {
                        MessageBox.Show("数据库连接失败");
                        return;
                    }
                    var sql = "select ";
                    sql += " row_number() over(order by[轮毂型号]) as 序号, ";
                    sql += " [轮毂型号], ";
                    sql += " count([轮毂型号]) as 轮毂数量, ";
                    sql += " sum(帽槽深度检测值1) as 轮毂重量, ";
                    sql += " avg(帽槽深度检测值1) as 平均重量 ";
                    sql += " from [dbo].[检测结果] where 时间 between '" + startTime + "' and '" + endTime + "' ";
                    if(textBox_wheeltype.Text != "")
                    {
                        sql += " and [轮毂型号] like '%" + textBox_wheeltype.Text + "%' ";
                    }
                    sql += " group by [轮毂型号]";


                    var sda = new SqlDataAdapter();
                    var cmd = new SqlCommand(sql, conn);
                    sda.SelectCommand = cmd;
                    var table = new DataTable();
                    sda.Fill(table);
                    dataGridView1.DataSource = table;
                }
            }
            catch
            {

            }
        }


        private string combine(DateTimePicker datetimepic, ComboBox combobox1, ComboBox combobox2, ComboBox combobox3)
        {
            string strcombox1;
            string strcombox2;
            string strcombox3;
            if (int.Parse(combobox1.Text) < 10)
                strcombox1 = "0" + combobox1.Text;
            else
                strcombox1 = combobox1.Text;
            if (int.Parse(combobox2.Text) < 10)
                strcombox2 = "0" + combobox2.Text;
            else
                strcombox2 = combobox2.Text;
            if (int.Parse(combobox3.Text) < 10)
                strcombox3 = "0" + combobox3.Text;
            else
                strcombox3 = combobox3.Text;


            string timestring = datetimepic.Value.ToString("yyyy-MM-dd");
            return timestring + " " + strcombox1 + ":" + strcombox2 + ":" + strcombox3;

        }

        private void Form_Closed(object sender, FormClosedEventArgs e)
        {
            m_sqlfun.connection(constr);
            if (m_sqlfun.Sql_open())
                m_sqlfun.conn.Close();
        }

        private void btn_toexcel_Click(object sender, EventArgs e)
        {
            m_sqlfun.connection(constr);
            m_sqlfun.Sql_open();
            if (dataGridView1.Rows.Count > 0)
            
            m_sqlfun.Export_DataGridViewtoExcel(dataGridView1);
            else
                 MessageBoxEX.Show(res.GetString("tableNull"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            m_sqlfun.conn.Close();
        }

     
    }
}
