﻿#pragma checksum "..\..\..\..\Views\AlgorithmVersionView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4A73B6927CF72119B8DC82E2FAAF9A268BA67453"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HalconAlgorithmManager.Views {
    
    
    /// <summary>
    /// AlgorithmVersionView
    /// </summary>
    public partial class AlgorithmVersionView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 64 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VersionCountText;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox VersionListBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedVersionText;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedStatusText;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedDateText;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedAuthorText;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SelectedDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ChangeLogListBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox FileChangeListBox;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestStatusText;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestCaseText;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestPassRateText;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\Views\AlgorithmVersionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PerformanceText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VisionAlgorithmManager;component/views/algorithmversionview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewVersion_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 33 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PublishVersion_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 36 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RollbackVersion_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.VersionCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.FilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.VersionListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 81 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            this.VersionListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.VersionListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SelectedVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.SelectedStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.SelectedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SelectedAuthorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SelectedDescriptionText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ChangeLogListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 13:
            this.FileChangeListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 14:
            this.TestStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TestCaseText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TestPassRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.PerformanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            
            #line 269 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewTestReport_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 278 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyVersion_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 281 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateTag_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 284 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MergeVersion_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 287 "..\..\..\..\Views\AlgorithmVersionView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportVersion_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

