﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="TestSDKControllerDemo.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="HalconDotNet" Version="19.11.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="MvCameraControl.Net">
      <HintPath>D:\MVS\Development\DotNet\win64\MvCameraControl.Net.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Implementations\" />
  </ItemGroup>

</Project>
