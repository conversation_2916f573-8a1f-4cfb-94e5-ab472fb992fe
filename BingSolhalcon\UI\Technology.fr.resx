﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="DevExpress.Utils.v14.1" name="DevExpress.Utils.v14.1, Version=14.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="imageCollection1.ImageStream" type="DevExpress.Utils.ImageCollectionStreamer, DevExpress.Utils.v14.1" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFpEZXZFeHByZXNzLlV0aWxzLnYxNC4xLCBWZXJzaW9uPTE0LjEu
        NC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI4OGQxNzU0ZDcwMGU0OWEMAwAAAFFT
        eXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRv
        a2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAAChEZXZFeHByZXNzLlV0aWxzLkltYWdlQ29sbGVjdGlvblN0
        cmVhbWVyAgAAAAlJbWFnZVNpemUERGF0YQQHE1N5c3RlbS5EcmF3aW5nLlNpemUDAAAAAgIAAAAF/P//
        /xNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAwAAACAAAAAgAAAACQUAAAAP
        BQAAAHIfAAAClQcAAIlQTkcNChoKAAAADUlIRFIAAAAgAAAAIAgGAAAAc3p69AAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAc3SURBVFhH7Zf7T1vnGccTth82aZ22Sd2lAZuLWbbuD1hV
        bcq29IdJ1aRprZQ2kaZsTdqtUkTaVa3WLa0mrSRZsrQEVkbWhRADAQJdkkbEaQLhZogxGDDYGIyDwTYG
        fL8e3+C753k5BpJwiaatP+0rvZyDfc75PPfzesf/9YBylErlF871jHvl/z9b5ebmfrFYqfzuOe34siMk
        obLLVCd/9dlo9+7dj6mUyqf+2WeB1S9hloxILi2hps+cqLwzlC9f9r+TSqX6cpFC8XS93oqFWArBRAbx
        9DJSmSUkU2ksZZbhD0dRftswfUqje4tu2bly539JCoXiq4V5hT9oG3cgllpGeglIpTOQEglEoxJC4RgC
        wQj8gQgCoQgi9Fmavl8vx2IAdy2zyx2jtkxd94hU0zEcL73aE5ERW4tq4GsFeQU/HHV4kCavUymKQDxB
        oPgKPBAW8CDBw5EYonGJrqHIUJq2ksvpgIzYWpSCx1X5+Xum5n3iwXEpIUAMZM95hSgF0VgcsSyc0rKd
        nE7noxlALfjNIqXyR4vBMIU8vgr3r8JjIuwSGZbOZLC0PVuk0T77iBEozs3dRQb82EcGhAgeoCPDV0NO
        RnExMvxRxMGRKJXOeQ9eU7c+JWM21c6ioqI8NiAo4BFou0fwysFStDS0ob5Gg0s1N5GQkmLFyBhWwB8S
        xwfFnjM8kszAS1ErqWmtkDmbamdhYaGiUKH4STwmCQPqCBqJxqgQJSSoEzjkQX8YVsss5pyLGOwfR3/v
        mIxcU5ouZHg4kUZASlFLJ/FmrWbbNOzMz89XCgO4+KjY9j93DCW/+SvO//0aLKZp3L6hEwBt5zAcdjf6
        KEJ2m0t8tkRQz4If7jmvgHd2GeGJxAXcFU6gVW/a3oBvKxQFZMBeKZEU7dfXYxQPZlnMdnFkSfGkfAbc
        m3KKYzKZojaNwOMLibDX1d5C66cDBJcwHZSgs9zb3oBihYKSoNjLD8u2mN8bFPnO5nxi3C48HzFMwu3y
        iMhYLTNovdaL/c+/g7NnmtCrG0dDUycqqz7BuMuPUYcP3SbbtgbkqPLyitiAJbnKjQQZ0JnRR+HsvjMk
        PuPwh4NRcZ5IkNfksY+MfPuNDxElz4OU8wn7AubIczt5PumNYWwxiu5JF179x1WVzNpQOUW5RaqCgoJn
        xNNlcRQMestq/jPy6OXU8HmC0tVLqZqeWUD/oBXv/OEjgidw8nQjXnrpJH7x3B8x4AxA5wrjUNW/WmTW
        hsqhLiheb8DHje3obBuEgaqdNXDXROE3UhRGxLmu14QEFdyRl0+LF9fHV7V4maClJy7B7A7i9qANhvkI
        wUPocgRxrLFtyzSsGKBYM4Bbbb3Y26y4NhnOYQ9IaXjiKRH25us6aI12DLsCaB+dRUuXCdeH7Lht96NB
        P7GNAbvuN8BktOFuz6jItXnUJlY4FIWFCrHt1gBOvqfG+xTqw78+gX3PH8OpM5fx7LNvwrQYwa8O/QU/
        +/nbeOanv8Pr715Ay4gDap1lawNEDawzoP1TPYYGLBiiGjAOTWKaWi5DNVFbrUGMPPdFJHRqTWjvGcMM
        bV6s/jjMniiGF6Lonwujm8LeZg+g9Z4fTUYXLgzY8GJlc7HMe0hyFxTulflC2bFrpmjM0vCZm/NBc6Mf
        Icr58dJaXL+px3n1LRhs85vCr1h9aLR4UDfuwYHyxosy7yGtDiKZfZ94HCVpvrPnDOecz0eSmKFWmwqw
        5zGCRwS8xxlC+0wQNwh+1erF5Ukvas2LqDYtoqT25qZpWB3FvNFgdbUbxJELLkV/GB6k+e4luJtajfeM
        a3D2PCQ8X4P7cHnCI+AXCH5udAEn74xtbgC/DdkA3laxsiNXS63353ercfjgceyj94M7sgYfJ/jIA3DN
        dADXpnxokuHsOcMrRuZR2r65ATt25+U9wa/jXqryrFLrwu4lgx6GR6AnuJbDTjlnzxnOYa8ze1A9tiDg
        fyP4B0NulPba8MKZ+u/JyPuV3ZKd1axMPQGnfWGIwu6Tc+4IJWDLwmnE6innAj4TIM9X4M0TXtRTwVWP
        LaJqdB4VwwQfduOUwY3jAy7s+6B+UEbeL9qSfYV3xb+nieXzB+Cac8Nmn8XE9AxMUzMYmrSjf2IaWrMN
        HWM23DJO0ZCx4orBiiYaMpcGp3CRtvTn9fdQ1W9DhX4a5QOzeH/QidMEP0Hw9/QuHGnq2DgNTz7+5JeK
        8/O//9ZHLR1/amrD62oNjp6/Yn2t6nLP0cqG1qMVtc0lZ9W1JeXqpiNl6uZXyy5+8tsyteaVsrquw2W1
        ukPl9aaD5fUzvyxr8B8oa8jsL2/Ei2cb8UJZA61LOPBhMw5UtuDwhRsbG7Bnz57Pq3apcnkickfwHpF+
        K3yLN6v0jvgGjeqv8+Jz/owXf8+1w9eKe+mFzvdyS/PrnYcbP4+u/Q7/7ONrCPW5FeLG4l87OfLxPxHf
        t37xs7KLwXyUtWPHvwHYaxM8xZ4cpQAAAABJRU5ErkJggkMIAACJUE5HDQoaCgAAAA1JSERSAAAAIAAA
        ACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAH5UlEQVRYR92X
        e0zV5xnHtdZsS5Z1U0FAJ+lmsi1rtmXZ4pJmTVaX1bb2jy5u6bJkmVFr1XoHb3iZVsVCRbwjIuVYEFQQ
        EPAAAnK/OhFRvIPcOXAO5/zO/cpnz++cY7fGy3Rd98e+yff87u/3+z7v877vc8b9X2Jbcc+khAZvd1wd
        7KyCvLLGecFHXwnGbyr1VO8QoT01sLceEhrgEznGyvV2uR9TDsF3vzzey3Id/GsevH8ell+AtSWwsQy2
        XYIz16HgdkB4dzWoprZUwIaLfgPjAy08Hyb8OhV+q4G30uHdLAlntQ4YY7kW9Ha4a4Co0oCJ1kH4tBUq
        HwQMqeKFYkjFjJdmfCfY5rMjNDT0e6+egNfTYMgKNocHp8uLy+3F4/X5o2AQEwcbfH4TyZcDYo+DwWCo
        CDb77Jg0adK3fpUC5fVDDEl3DUYHgwYHw0YnBsWJ1ebC55N4jIHX6w1KPRnBZp8LL9RcV2joMNPRZeFu
        j4X7fVa6Bmx0D9ro0dnpG7F/bmJMfsw2B139ejHko6NzwC/scnv8R2lvYqDZZ0Bs8s2T2obhu5V/13tr
        r5mob1doFCNNN8203LZw+Y6Z1nsWPysvG+kUUbPVIcPk8oupMJptfjMqVUyfNH1asPmnIyWvu/VEQX+b
        pnjw1umyPs7XGylsVDjfMEphk5ELLUZKryhUtIl4u0qz9Bj6h00oYsLpCvRYhRoRFYEI/Zth2Hbkdu3O
        lM66XWndDSeLh0kTphYNkFFuJLPSxOlqhTM1CplVo3I+SnadkdxGEwXNZvIaxWSzQoEY1LaY/BF5ODTq
        8SGDUo8ip1x3Y+GOWyUJmf3szeonMXuQo4UGDuUPceyCkSMX9CSXGEi5aOBkpYJGDGkuGeVc5Sjpqqla
        MSg8W2siuUgnOTNM660eTBaHP0IeCUxISMg3g5JfwMSkU/eI1fSw5XgPuzJ0fJQ+QNzZYRLyR0nIM5FY
        oBCfP0JikVwX6kkqs5BcbuF4hZlDxXqOXTSSJAa3fqZjYVwPS+L7yS4fluHw0acz+cXdbrjRfmt3UPOf
        UBeJWa8f8da0Wog+3M2m1EG2ZYywJV3Hrhwjm9OH2JWrsOPcKB/lqjQQV6jI0cimLDNr0kysTjWyKmWU
        BYm9LDs4xPy4bpYmDPhFXZKXD+l0Pn4YxkeERPxs7oIaCuoVPjjQy+qUEVYcH2JlqvTocCdrM6xEZVpZ
        fcrMutM2VmUoRGVZWabRszrdRHSGhTUnxUiakYUH+1m0v5/58T3M39ONQ/LwIe2ycInehIDsv2DmzJlf
        Cw8JfyNaerAjU8eKE0Osz1JYk2FgWdoA67OdrDlrZXmW9Drf7WdUjpX1uQ5WyXsfphtYkjYiRs2sPan4
        I/Jh0giL9vWxZN+gX9hmCzA8PHxKUPaLUB+88mam8WKrjagMPdFnTGw+bycqe5RNBR5iiryszbexSeth
        XaGTDUUuthT72Cz31+eLkTNmVmUqYljPyrRRVomJFcekA4eGWbx3AKss5yr1eudAUPIRjA8LC/vRrL80
        U3nHw/pzCn8rcbO11Mk22dF2yIazUzaZ3ZXquYedFT42au1C9fkYm7VeYgrc/oisPqWwNt3M4qQhViSJ
        iQPDHMoxYDbL2iAM6j0WE6dOnfqbdRoDh2vtIm7n4xqfhNxAvGyvu8sdJNZCohQb8VUe4qt9Qth1yce6
        IjsxxWKiyMPGXCfRkitLToyw9JgMxf4B3pekVBT8jIyM/HpQ71FEvhT57Vl/+LRO2+ElrsrOJ8LdJXoO
        q8WGnO+rspHcBMcaxzjaKMVHlVPec7Orws22UjcxJV6i8+xsOOeURLWxWmNmRfKoDMUIKz++htEIGk32
        W0G5x0MWjO+/tuE6Lf1jHKq1kVRvQ9MiH7b4SGvxktrsIqXJxYlmD4frHCSJkQNqVCQaO6UO2FI65s+b
        ddkO/wxamSozZNNn9PZaGB3Fz6DUEzFBZsWrm0s9FN92kdZk4awUG9lXfeReg3PtkCPHM21w6gqkSS2Q
        HDQRp1ZDUobFaH1syHMRnWUnM7OUvj6b1AVqbaAmot/A06skddn8xRuLNxbfHRNBOxdEtOQGlN0KsOSm
        VDxyfU6MZIo51YQaCTVHYiVRt0riLjl6jatXu9HpvBRrq5jx3Rna8Mmhf5Y8+7FIvBBQegpkas54+0gv
        9w1ioklH9V2ouR9g1T2ouAPFYiRPzGVKJFJlmNRc2Vpo5M2330OTpqHsYhnz5s0jPDT03eCW/Fz14Qsy
        NX+5p9pNc6dCczdc7oWWPmjqgbpO0LbbSMi9yn7tPRK0PewtG5EQ62W8eyXsfbwc+fLS8CnhP58+ffo3
        gm0+H9QPJWRz6x54uD4kY998i7wrdzjf1kVxh47KLgfaDis5bVYWfLDWLzo4OMicOXMICwmbI7XlVGnm
        P6qKP4fkQ9iy9PvePpOHK3oLbUYb1xQ39VKiVXc7xISbzs5OTCaTX1zC/Scx/Yq6xAeb+NIYPy0k5Kfp
        Unz02T0ibqFdDLTonCxYtExWN7Mkmo7Zs2crEaGhsyMiIiar3wQ+/S8huGH9rm3Yx3Wjldoeg6xqCg8e
        PKCgoICpU6b+MWJyxA/k1WcvPp8X6oaVkH1Zt2FjDF1dXX6+M/edjrApYa+pK2jwta8W4ZPDf7h9+3Zi
        Y2PVJPu9+gdGbr8YePq/wYvTQkN/ok6tJ9V4z4Zx4/4B+ko+tCY4CBsAAAAASUVORK5CYIKODwAAiVBO
        Rw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACHDwAA
        jA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUA
        AEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZEsRAU
        VLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTsAIAB
        HmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4JUuQ
        LrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR3xKx
        RoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQdli7d
        1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtFehn4
        uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGXwzIS
        F/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNFhImm
        jMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH554SqT
        i3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJVgMW
        SASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB5CEV
        SBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyCqbAS
        rAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiEj6xH
        ipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I1kfb
        oL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9rB3W
        H8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhGfDf+
        On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFpB+kQ
        6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJyeuS
        M1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJCYVE2
        UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQlnyfYl
        rUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48vvacI
        K+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0CvpvfRZ
        VUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15LWytc
        a6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AAbWBt
        wDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0zllmN
        2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHWztYb
        rE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5sxybH
        SSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6eO7y
        HPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPwYyAm
        MCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmRXVHY
        qLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNmWS6s
        vaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wlxqae
        5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2dDYv
        uz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8V5vD
        N3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33zaEb9j
        oNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2vTqy+
        XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqbPhzi
        HRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/0Pyh
        tp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h/HhP
        TM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavrXTes
        b3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxSfNTw
        s+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+tfa5
        zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/6H7o
        /ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAABL9JREFUWEfFld9Pm1Ucxk9/
        v4UC3ki3oia7GN55C7hxIzdq4MZ/YYleekMg0cQfUIpzLpkubjjDzHT+YnGLHTKYkglBJAjLDCrqcKRA
        VwrDKiIiDL4+z2n70pe1MDY2m3zo+74953me8z3f86JE5H8l68P7SdaH9xPLTXVXdVXd5bpw42ijBK8G
        JTgelKbJJmm63iSheEhCN0LS/HuzNCc2Ab9zHMdzHudTh3r1I/Xhmks1VZmelgB7z+wNl3WVib/DL0aX
        IXm9eZI/mC++731SMFogBWMFUjheuCUcx/Gcx/nU8Xf7pfzrcik9XxrO9LQEcL3nkuJzxeI57xHvl17J
        60OAIQQYQYBfYH4tu+EtYBzHcx7nU4d6/k6/uD9xwypXgHdd4j7jFqPdEG+3Vzxhj3jOAQQyLhhiXNwa
        zqN5wa8I8AMCDCPANwiA59R1n95GgPbr7Xi8vc/szVmpiFZIwVWE+BEBLiNA/x0GWFtb2xY0r/yjUoom
        iu6wAicQoM1t9sDq6uptM3NzxjTP1QPUdX+wWYAWBECTeD7HnuMUrKys3BbTy9NSmUiZowktp2AAAXAK
        qEdd96lNAjiPOcX1oUs8ZxGgw5Dl5eUtif0bs5gXjiHAz6nVf5cvJR0l4r2E8kOPuq6TLljlCnAUAd5f
        34alpaVNif4Tlf2J/etl58ppjr33DfukpLNEhmeG18sPXfZZpqclgOOIQ5wnneL+CAGQdnFxUROcDZrX
        aSb/npR9N/YljdFw+uix7Fh52nwoPqTHpldPXedxJ6xyBTiMACdSVfjULQsLCxoepcaZRvM+8ldEKqZx
        1LBaHjW9Yhpzz1n2CyUyGBs0x+u9hx51WeVMT0sA+0G7OI4hRCtCnHbJ/Py8xug09HFqiDfI+J/jUj5V
        Lr4rySPGcPob3c6GC3wRkIHogDmX8GhTj7qscqanNUAIAd50iKMluRWJREKjTwXKyNXt+W2P5H+bPFo8
        33zJ6G90eiAckP7JfnNeGm0OPeo6Dm0SwNZgE/thhHgLAVqcMjc3p2H5uIf6lYzjRNhYfLlocL377G7p
        i/SZczKhOfWoa2+2wypXgJdsYju4HmJ2dlajewINpE8Hg6AiOgzebEbYkF1tu6T3Wq85fiN65TSHrq3R
        BqscAdSLSmxN6yHi8biGjclVMAjfE3xZMQz3luY9Yz3m2GxwW7U5dG0vbxbgBSXqVYQIYuBrNonFYhrH
        UazgePKE6AbFv23XKYBAB3oOmONyYX8D5tCjLhdp8bTcMMArqRAoVTQa1TC9/UhyWxxvp8JgT53vIMxn
        LqkfrTfHZsMWgjn0qEsPi6flJh0gxdTUlEZvC0W4Cpbx9ST2QwhGPrZL7UitOX4jqiFDdzsBJiYmdoRM
        zW0FaP2pVSKRyF1BjUzNWwKkPjbNhgCqDXx1l1AjU5MeST/zYwdO4FZ1+BFdapmwk1CbHkk/+uogblAE
        AupZ1a2eTw3MJnA3UJPaz6mL8HoAeIADqHwQAI+px1WtqlaD6hkMvBfUqH71BJap1EPAB1gJ5QUPglJQ
        BqrAk+DpHeQpQF3qPwr8IA/oCvCPAbgNDMJqPAwe2UGoVwKKAX3ol+4D3Qi8YBA+dN1DqE+fVAMq9R91
        G98ZRwgEawAAAABJRU5ErkJgggs=
</value>
  </data>
  <data name="Btn_ImportExc.Caption" xml:space="preserve">
    <value>Importation de processus</value>
  </data>
  <data name="Btn_ExportExc.Caption" xml:space="preserve">
    <value>Processus actuel</value>
  </data>
  <data name="Btn_LoadTechnology.Caption" xml:space="preserve">
    <value>Téléchargement de processus</value>
  </data>
  <data name="ribbonPage1.Text" xml:space="preserve">
    <value>Formule de processus</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Données de formule de processus</value>
  </data>
</root>