<Window x:Class="HalconAlgorithmManager.Dialogs.NewAlgorithmDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="新建算法档案" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="#FF2D2D30"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF1E1E1E" BorderBrush="#FF007ACC" BorderThickness="0,0,0,2" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="➕" FontSize="24" Foreground="#FF007ACC" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="新建算法档案" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="Create New Algorithm Profile" FontSize="12" Foreground="LightGray" Margin="0,2,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主内容区 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="30,20">
            <StackPanel>
                
                <!-- 基本信息 -->
                <GroupBox Header="📋 基本信息" Foreground="White" Margin="0,10">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="算法编号:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="AlgorithmIdTextBox" Height="30" Margin="10,10,0,10"
                                Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                FontSize="12" Text="A0006" ToolTip="算法唯一编号，如：A0001"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="算法名称:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="AlgorithmNameTextBox" Height="30" Margin="10,10,0,10"
                                Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                FontSize="12" Text="" ToolTip="算法描述性名称"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="算法分类:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" x:Name="CategoryComboBox" Height="30" Margin="10,10,0,10"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="12">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="轮毂检测算法" IsSelected="True"/>
                            <ComboBoxItem Content="质量检测算法"/>
                            <ComboBoxItem Content="模板匹配算法"/>
                            <ComboBoxItem Content="其他算法"/>
                        </ComboBox>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="检测引擎:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                        <ComboBox Grid.Row="3" Grid.Column="1" x:Name="EngineComboBox" Height="30" Margin="10,10,0,10"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="12">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="Halcon 23.05" IsSelected="True"/>
                            <ComboBoxItem Content="OpenCV 4.8"/>
                            <ComboBoxItem Content="VisionPro 9.0"/>
                            <ComboBoxItem Content="自定义引擎"/>
                        </ComboBox>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="算法类型:" Foreground="LightGray" FontSize="14" VerticalAlignment="Center" Margin="0,10"/>
                        <ComboBox Grid.Row="4" Grid.Column="1" x:Name="AlgorithmTypeComboBox" Height="30" Margin="10,10,0,10"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="12">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="尺寸测量" IsSelected="True"/>
                            <ComboBoxItem Content="缺陷检测"/>
                            <ComboBoxItem Content="位置定位"/>
                            <ComboBoxItem Content="模板匹配"/>
                            <ComboBoxItem Content="颜色识别"/>
                            <ComboBoxItem Content="字符识别"/>
                            <ComboBoxItem Content="计数统计"/>
                        </ComboBox>
                    </Grid>
                </GroupBox>

                <!-- 详细描述 -->
                <GroupBox Header="📝 详细描述" Foreground="White" Margin="0,10">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="算法描述:" Foreground="LightGray" FontSize="14" Margin="0,0,0,10"/>
                        <TextBox Grid.Row="1" x:Name="DescriptionTextBox" Height="100" 
                                Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1"
                                FontSize="12" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                Text="请输入算法的详细描述，包括检测目标、应用场景、技术特点等信息..."/>
                    </Grid>
                </GroupBox>

                <!-- 技术参数 -->
                <GroupBox Header="⚙️ 技术参数" Foreground="White" Margin="0,10">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="测量精度:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" x:Name="PrecisionComboBox" Height="25" Margin="10,8,10,8"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="0.01mm" IsSelected="True"/>
                            <ComboBoxItem Content="0.1mm"/>
                            <ComboBoxItem Content="1mm"/>
                        </ComboBox>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="处理速度:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <ComboBox Grid.Row="0" Grid.Column="3" x:Name="SpeedComboBox" Height="25" Margin="10,8,0,8"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="高速 (小于100ms)" IsSelected="True"/>
                            <ComboBoxItem Content="中速 (100-500ms)"/>
                            <ComboBoxItem Content="精确 (大于500ms)"/>
                        </ComboBox>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="图像尺寸:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" x:Name="ImageSizeComboBox" Height="25" Margin="10,8,10,8"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="2048x1536" IsSelected="True"/>
                            <ComboBoxItem Content="1920x1080"/>
                            <ComboBoxItem Content="1280x720"/>
                            <ComboBoxItem Content="自定义"/>
                        </ComboBox>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="颜色模式:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <ComboBox Grid.Row="1" Grid.Column="3" x:Name="ColorModeComboBox" Height="25" Margin="10,8,0,8"
                                 Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" FontSize="11">
                            <ComboBox.Resources>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="Background" Value="#FF3F3F46"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Style>
                            </ComboBox.Resources>
                            <ComboBoxItem Content="灰度图像" IsSelected="True"/>
                            <ComboBoxItem Content="彩色图像"/>
                            <ComboBoxItem Content="多光谱"/>
                        </ComboBox>

                        <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Content="支持实时处理" IsChecked="True" 
                                 Foreground="White" FontSize="12" Margin="0,15,0,5"/>
                        <CheckBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Content="支持批量处理" IsChecked="False" 
                                 Foreground="White" FontSize="12" Margin="0,15,0,5"/>
                    </Grid>
                </GroupBox>

                <!-- 版本信息 -->
                <GroupBox Header="📋 版本信息" Foreground="White" Margin="0,10">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="初始版本:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="VersionTextBox" Text="v1.0.0" Height="25" Margin="10,8,10,8"
                                Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1" FontSize="11"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="创建者:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <TextBox Grid.Row="0" Grid.Column="3" x:Name="CreatorTextBox" Text="系统管理员" Height="25" Margin="10,8,0,8"
                                Background="#FF3F3F46" Foreground="White" BorderBrush="#FF007ACC" BorderThickness="1" FontSize="11"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="创建时间:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CreateTimeText" Text="2024-01-25 15:30:00" 
                                  Foreground="White" FontSize="11" VerticalAlignment="Center" Margin="10,8,10,8"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="状态:" Foreground="LightGray" FontSize="12" VerticalAlignment="Center" Margin="0,8"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="草稿" Foreground="Orange" FontSize="11" FontWeight="Bold" 
                                  VerticalAlignment="Center" Margin="10,8,0,8"/>
                    </Grid>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <Border Grid.Row="2" Background="#FF3F3F46" BorderBrush="#FF5A5A5A" BorderThickness="0,1,0,0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="✅ 创建算法" Width="100" Height="35" Margin="10,0"
                       Background="#FF28A745" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Create_Click"/>
                <Button Content="❌ 取消" Width="80" Height="35" Margin="10,0"
                       Background="#FF6C757D" Foreground="White" BorderThickness="0" FontSize="12"
                       Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
