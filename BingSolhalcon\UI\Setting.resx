﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="xtraTabControl1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xtraTabControl1.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="xtraTabControl1.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 12</value>
  </data>
  <metadata name="page1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>808, 93</value>
  </metadata>
  <data name="page1.Appearance.PageClient.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Tomato</value>
  </data>
  <metadata name="Edit_HatFocal.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>676, 93</value>
  </metadata>
  <data name="Edit_HatFocal.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 293</value>
  </data>
  <data name="Edit_HatFocal.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Edit_HatFocal.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;Edit_HatFocal.Name" xml:space="preserve">
    <value>Edit_HatFocal</value>
  </data>
  <data name="&gt;&gt;Edit_HatFocal.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_HatFocal.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_HatFocal.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lblHatFocal.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblHatFocal.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 296</value>
  </data>
  <data name="lblHatFocal.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblHatFocal.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="lblHatFocal.Text" xml:space="preserve">
    <value>帽止口焦距</value>
  </data>
  <data name="lblHatFocal.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblHatFocal.Name" xml:space="preserve">
    <value>lblHatFocal</value>
  </data>
  <data name="&gt;&gt;lblHatFocal.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHatFocal.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblHatFocal.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="groupControl2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>535, 93</value>
  </metadata>
  <data name="groupControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 31</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>ReduceRow</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="Edit_ReduceRow_C3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>365, 93</value>
  </metadata>
  <data name="Edit_ReduceRow_C3.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 28</value>
  </data>
  <data name="Edit_ReduceRow_C3.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceRow_C3.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow_C3.Name" xml:space="preserve">
    <value>Edit_ReduceRow_C3</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow_C3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow_C3.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow_C3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="Edit_ReduceRadius_C3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>181, 93</value>
  </metadata>
  <data name="Edit_ReduceRadius_C3.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 92</value>
  </data>
  <data name="Edit_ReduceRadius_C3.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceRadius_C3.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius_C3.Name" xml:space="preserve">
    <value>Edit_ReduceRadius_C3</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius_C3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius_C3.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius_C3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 63</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>ReduceCol</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 95</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>ReduceRadius</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="Edit_ReduceCol_C3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 93</value>
  </metadata>
  <data name="Edit_ReduceCol_C3.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 60</value>
  </data>
  <data name="Edit_ReduceCol_C3.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceCol_C3.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol_C3.Name" xml:space="preserve">
    <value>Edit_ReduceCol_C3</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol_C3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol_C3.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol_C3.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 161</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>227, 122</value>
  </data>
  <data name="groupControl2.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>C3</value>
  </data>
  <metadata name="groupControl1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1224, 55</value>
  </metadata>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 31</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>ReduceRow</value>
  </data>
  <data name="labelControl11.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="Edit_ReduceRow.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1074, 55</value>
  </metadata>
  <data name="Edit_ReduceRow.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 28</value>
  </data>
  <data name="Edit_ReduceRow.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceRow.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow.Name" xml:space="preserve">
    <value>Edit_ReduceRow</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRow.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="Edit_ReduceRadius.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>910, 55</value>
  </metadata>
  <data name="Edit_ReduceRadius.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 92</value>
  </data>
  <data name="Edit_ReduceRadius.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceRadius.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius.Name" xml:space="preserve">
    <value>Edit_ReduceRadius</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceRadius.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 63</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>ReduceCol</value>
  </data>
  <data name="labelControl10.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 95</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 14</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>ReduceRadius</value>
  </data>
  <data name="labelControl9.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="Edit_ReduceCol.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>766, 55</value>
  </metadata>
  <data name="Edit_ReduceCol.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 60</value>
  </data>
  <data name="Edit_ReduceCol.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ReduceCol.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol.Name" xml:space="preserve">
    <value>Edit_ReduceCol</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;Edit_ReduceCol.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 30</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>227, 122</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>C2</value>
  </data>
  <metadata name="cbo_decimalnum.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>614, 55</value>
  </metadata>
  <data name="cbo_decimalnum.EditValue" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbo_decimalnum.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 228</value>
  </data>
  <assembly alias="DevExpress.Utils.v17.2" name="DevExpress.Utils.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="cbo_decimalnum.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v17.2">
    <value>Combo</value>
  </data>
  <data name="cbo_decimalnum.Properties.Items" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbo_decimalnum.Properties.Items1" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbo_decimalnum.Properties.Items2" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbo_decimalnum.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="cbo_decimalnum.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;cbo_decimalnum.Name" xml:space="preserve">
    <value>cbo_decimalnum</value>
  </data>
  <data name="&gt;&gt;cbo_decimalnum.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ComboBoxEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cbo_decimalnum.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;cbo_decimalnum.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="Edit_HoleScale.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>900, 93</value>
  </metadata>
  <data name="Edit_HoleScale.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 261</value>
  </data>
  <data name="Edit_HoleScale.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_HoleScale.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;Edit_HoleScale.Name" xml:space="preserve">
    <value>Edit_HoleScale</value>
  </data>
  <data name="&gt;&gt;Edit_HoleScale.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_HoleScale.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_HoleScale.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lblHoleScale.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 264</value>
  </data>
  <data name="lblHoleScale.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblHoleScale.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="lblHoleScale.Text" xml:space="preserve">
    <value>中心孔比例</value>
  </data>
  <data name="lblHoleScale.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblHoleScale.Name" xml:space="preserve">
    <value>lblHoleScale</value>
  </data>
  <data name="&gt;&gt;lblHoleScale.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHoleScale.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblHoleScale.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblDecimalnum.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 231</value>
  </data>
  <data name="lblDecimalnum.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblDecimalnum.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="lblDecimalnum.Text" xml:space="preserve">
    <value>有效数据位</value>
  </data>
  <data name="lblDecimalnum.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblDecimalnum.Name" xml:space="preserve">
    <value>lblDecimalnum</value>
  </data>
  <data name="&gt;&gt;lblDecimalnum.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDecimalnum.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblDecimalnum.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <metadata name="Edit_markZ.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>495, 55</value>
  </metadata>
  <data name="Edit_markZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 195</value>
  </data>
  <data name="Edit_markZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_markZ.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;Edit_markZ.Name" xml:space="preserve">
    <value>Edit_markZ</value>
  </data>
  <data name="&gt;&gt;Edit_markZ.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_markZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_markZ.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lblMarkZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 198</value>
  </data>
  <data name="lblMarkZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblMarkZ.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="lblMarkZ.Text" xml:space="preserve">
    <value>标记Z轴</value>
  </data>
  <data name="lblMarkZ.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblMarkZ.Name" xml:space="preserve">
    <value>lblMarkZ</value>
  </data>
  <data name="&gt;&gt;lblMarkZ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblMarkZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblMarkZ.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="Edit_ThicknessZ.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>249, 55</value>
  </metadata>
  <data name="Edit_ThicknessZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 162</value>
  </data>
  <data name="Edit_ThicknessZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_ThicknessZ.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;Edit_ThicknessZ.Name" xml:space="preserve">
    <value>Edit_ThicknessZ</value>
  </data>
  <data name="&gt;&gt;Edit_ThicknessZ.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_ThicknessZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_ThicknessZ.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="lblThicknessZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 165</value>
  </data>
  <data name="lblThicknessZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblThicknessZ.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lblThicknessZ.Text" xml:space="preserve">
    <value>厚度Z轴</value>
  </data>
  <data name="lblThicknessZ.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblThicknessZ.Name" xml:space="preserve">
    <value>lblThicknessZ</value>
  </data>
  <data name="&gt;&gt;lblThicknessZ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblThicknessZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblThicknessZ.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <metadata name="Edit_hatZ.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>142, 55</value>
  </metadata>
  <data name="Edit_hatZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 129</value>
  </data>
  <data name="Edit_hatZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_hatZ.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;Edit_hatZ.Name" xml:space="preserve">
    <value>Edit_hatZ</value>
  </data>
  <data name="&gt;&gt;Edit_hatZ.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_hatZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_hatZ.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lblHatZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 132</value>
  </data>
  <data name="lblHatZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblHatZ.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="lblHatZ.Text" xml:space="preserve">
    <value>帽止口Z轴</value>
  </data>
  <data name="lblHatZ.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblHatZ.Name" xml:space="preserve">
    <value>lblHatZ</value>
  </data>
  <data name="&gt;&gt;lblHatZ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHatZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblHatZ.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <metadata name="Edit_centreZ.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 55</value>
  </metadata>
  <data name="Edit_centreZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 96</value>
  </data>
  <data name="Edit_centreZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_centreZ.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;Edit_centreZ.Name" xml:space="preserve">
    <value>Edit_centreZ</value>
  </data>
  <data name="&gt;&gt;Edit_centreZ.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_centreZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_centreZ.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lblCentreZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 99</value>
  </data>
  <data name="lblCentreZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblCentreZ.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="lblCentreZ.Text" xml:space="preserve">
    <value>中心孔Z轴</value>
  </data>
  <data name="lblCentreZ.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblCentreZ.Name" xml:space="preserve">
    <value>lblCentreZ</value>
  </data>
  <data name="&gt;&gt;lblCentreZ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblCentreZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblCentreZ.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <metadata name="Edit_deepX.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1243, 17</value>
  </metadata>
  <data name="Edit_deepX.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 63</value>
  </data>
  <data name="Edit_deepX.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_deepX.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;Edit_deepX.Name" xml:space="preserve">
    <value>Edit_deepX</value>
  </data>
  <data name="&gt;&gt;Edit_deepX.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_deepX.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_deepX.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="lblDeepX.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 66</value>
  </data>
  <data name="lblDeepX.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblDeepX.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblDeepX.Text" xml:space="preserve">
    <value>深度X轴</value>
  </data>
  <data name="lblDeepX.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblDeepX.Name" xml:space="preserve">
    <value>lblDeepX</value>
  </data>
  <data name="&gt;&gt;lblDeepX.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDeepX.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblDeepX.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <metadata name="Edit_deepZ.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1124, 17</value>
  </metadata>
  <data name="Edit_deepZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 30</value>
  </data>
  <data name="Edit_deepZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_deepZ.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;Edit_deepZ.Name" xml:space="preserve">
    <value>Edit_deepZ</value>
  </data>
  <data name="&gt;&gt;Edit_deepZ.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_deepZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;Edit_deepZ.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="lblDeepZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>-27, 33</value>
  </data>
  <data name="lblDeepZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="lblDeepZ.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblDeepZ.Text" xml:space="preserve">
    <value>深度Z轴</value>
  </data>
  <data name="lblDeepZ.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;lblDeepZ.Name" xml:space="preserve">
    <value>lblDeepZ</value>
  </data>
  <data name="&gt;&gt;lblDeepZ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDeepZ.Parent" xml:space="preserve">
    <value>page1</value>
  </data>
  <data name="&gt;&gt;lblDeepZ.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="page1.Enabled" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="page1.Size" type="System.Drawing.Size, System.Drawing">
    <value>533, 342</value>
  </data>
  <data name="page1.Text" xml:space="preserve">
    <value>参数配置</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>539, 371</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <metadata name="page2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1032, 17</value>
  </metadata>
  <metadata name="Edit_wheelParam.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>880, 17</value>
  </metadata>
  <data name="Edit_wheelParam.Location" type="System.Drawing.Point, System.Drawing">
    <value>389, 124</value>
  </data>
  <data name="Edit_wheelParam.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_wheelParam.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="&gt;&gt;Edit_wheelParam.Name" xml:space="preserve">
    <value>Edit_wheelParam</value>
  </data>
  <data name="&gt;&gt;Edit_wheelParam.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_wheelParam.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;Edit_wheelParam.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 127</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>轮毂参数表</value>
  </data>
  <data name="label6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbox_sendNGPosition.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbox_sendNGPosition.Location" type="System.Drawing.Point, System.Drawing">
    <value>370, 40</value>
  </data>
  <data name="cbox_sendNGPosition.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 18</value>
  </data>
  <data name="cbox_sendNGPosition.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="cbox_sendNGPosition.Text" xml:space="preserve">
    <value>发送NG位置</value>
  </data>
  <data name="&gt;&gt;cbox_sendNGPosition.Name" xml:space="preserve">
    <value>cbox_sendNGPosition</value>
  </data>
  <data name="&gt;&gt;cbox_sendNGPosition.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_sendNGPosition.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;cbox_sendNGPosition.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="Edit_defExposureTimeHat.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>677, 17</value>
  </metadata>
  <data name="Edit_defExposureTimeHat.Location" type="System.Drawing.Point, System.Drawing">
    <value>389, 91</value>
  </data>
  <data name="Edit_defExposureTimeHat.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_defExposureTimeHat.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeHat.Name" xml:space="preserve">
    <value>Edit_defExposureTimeHat</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeHat.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeHat.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeHat.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 94</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>帽止口默认曝光时长</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="Edit_defExposureTimeCen.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>472, 17</value>
  </metadata>
  <data name="Edit_defExposureTimeCen.Location" type="System.Drawing.Point, System.Drawing">
    <value>389, 65</value>
  </data>
  <data name="Edit_defExposureTimeCen.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="Edit_defExposureTimeCen.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeCen.Name" xml:space="preserve">
    <value>Edit_defExposureTimeCen</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeCen.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeCen.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;Edit_defExposureTimeCen.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 68</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 14</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>中心孔默认曝光时长</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbox_posDegReverse.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbox_posDegReverse.Location" type="System.Drawing.Point, System.Drawing">
    <value>370, 16</value>
  </data>
  <data name="cbox_posDegReverse.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 18</value>
  </data>
  <data name="cbox_posDegReverse.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="cbox_posDegReverse.Text" xml:space="preserve">
    <value>位置度顺序翻转</value>
  </data>
  <data name="&gt;&gt;cbox_posDegReverse.Name" xml:space="preserve">
    <value>cbox_posDegReverse</value>
  </data>
  <data name="&gt;&gt;cbox_posDegReverse.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbox_posDegReverse.Parent" xml:space="preserve">
    <value>page2</value>
  </data>
  <data name="&gt;&gt;cbox_posDegReverse.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <metadata name="grpCamera2Type.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>316, 17</value>
  </metadata>
  <data name="rbtnCamera2_CameraLink.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbtnCamera2_CameraLink.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 33</value>
  </data>
  <data name="rbtnCamera2_CameraLink.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 18</value>
  </data>
  <data name="rbtnCamera2_CameraLink.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rbtnCamera2_CameraLink.Text" xml:space="preserve">
    <value>CameraLink</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_CameraLink.Name" xml:space="preserve">
    <value>rbtnCamera2_CameraLink</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_CameraLink.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_CameraLink.Parent" xml:space="preserve">
    <value>grpCamera2Type</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_CameraLink.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbtnCamera2_GigE.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbtnCamera2_GigE.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 33</value>
  </data>
  <data name="rbtnCamera2_GigE.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 18</value>
  </data>
  <data name="rbtnCamera2_GigE.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rbtnCamera2_GigE.Text" xml:space="preserve">
    <value>GigE</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_GigE.Name" xml:space="preserve">
    <value>rbtnCamera2_GigE</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_GigE.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_GigE.Parent" xml:space="preserve">
    <value>grpCamera2Type</value>
  </data>
  <data name="&gt;&gt;rbtnCamera2_GigE.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grpCamera2Type.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 94</value>
  </data>
  <data name="grpCamera2Type.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 60</value>
  </data>
  <data name="grpCamera2Type.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="grpCamera2Type.Text" xml:space="preserve">
    <value>Camera2类型</value>
  </data>
  <metadata name="grpMEGAPHASE.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>166, 17</value>
  </metadata>
  <data name="rbtnMEGAPHASE_No.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbtnMEGAPHASE_No.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 33</value>
  </data>
  <data name="rbtnMEGAPHASE_No.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 18</value>
  </data>
  <data name="rbtnMEGAPHASE_No.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rbtnMEGAPHASE_No.Text" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_No.Name" xml:space="preserve">
    <value>rbtnMEGAPHASE_No</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_No.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_No.Parent" xml:space="preserve">
    <value>grpMEGAPHASE</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_No.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbtnMEGAPHASE_Yes.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbtnMEGAPHASE_Yes.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 33</value>
  </data>
  <data name="rbtnMEGAPHASE_Yes.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 18</value>
  </data>
  <data name="rbtnMEGAPHASE_Yes.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rbtnMEGAPHASE_Yes.Text" xml:space="preserve">
    <value>有</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_Yes.Name" xml:space="preserve">
    <value>rbtnMEGAPHASE_Yes</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_Yes.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_Yes.Parent" xml:space="preserve">
    <value>grpMEGAPHASE</value>
  </data>
  <data name="&gt;&gt;rbtnMEGAPHASE_Yes.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grpMEGAPHASE.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 16</value>
  </data>
  <data name="grpMEGAPHASE.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 60</value>
  </data>
  <data name="grpMEGAPHASE.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grpMEGAPHASE.Text" xml:space="preserve">
    <value>厚度检测工位</value>
  </data>
  <data name="page2.Enabled" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="page2.Size" type="System.Drawing.Size, System.Drawing">
    <value>533, 342</value>
  </data>
  <data name="page2.Text" xml:space="preserve">
    <value>系统配置</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="btnSave.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>394, 55</value>
  </metadata>
  <data name="btnSave.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnSave.Location" type="System.Drawing.Point, System.Drawing">
    <value>363, 388</value>
  </data>
  <data name="btnSave.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 27</value>
  </data>
  <data name="btnSave.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnSave.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="&gt;&gt;btnSave.Name" xml:space="preserve">
    <value>btnSave</value>
  </data>
  <data name="&gt;&gt;btnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnSave.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnSave.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="btnCancel.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1039, 93</value>
  </metadata>
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 388</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 27</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 14</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>563, 427</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>Setting</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v17.2, Version=17.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
</root>