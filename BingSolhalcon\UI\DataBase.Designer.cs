﻿namespace BingSolhalcon
{
    partial class DataBase
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private string  filepath = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
          
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DataBase));
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.btn_check = new System.Windows.Forms.Button();
            this.btn_readdatabase = new System.Windows.Forms.Button();
            this.btn_toexcel = new System.Windows.Forms.Button();
            this.btn_deletedatabase = new System.Windows.Forms.Button();
            this.btn_queryWeight = new System.Windows.Forms.Button();
            this.btn_connectdatabase = new System.Windows.Forms.Button();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.dateTimePicker3 = new System.Windows.Forms.DateTimePicker();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.dateTimePicker2 = new System.Windows.Forms.DateTimePicker();
            this.label7 = new System.Windows.Forms.Label();
            this.textBox_wheeltype = new System.Windows.Forms.TextBox();
            this.cbox_timeH1 = new System.Windows.Forms.ComboBox();
            this.cbox_timeM1 = new System.Windows.Forms.ComboBox();
            this.cbox_timeS1 = new System.Windows.Forms.ComboBox();
            this.cbox_timeS2 = new System.Windows.Forms.ComboBox();
            this.cbox_timeM2 = new System.Windows.Forms.ComboBox();
            this.cbox_timeH2 = new System.Windows.Forms.ComboBox();
            this.label_H1 = new System.Windows.Forms.Label();
            this.label_M1 = new System.Windows.Forms.Label();
            this.label_S1 = new System.Windows.Forms.Label();
            this.label_M2 = new System.Windows.Forms.Label();
            this.label_H2 = new System.Windows.Forms.Label();
            this.label_S2 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.groupBox4.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // dataGridView1
            // 
            resources.ApplyResources(this.dataGridView1, "dataGridView1");
            this.dataGridView1.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowTemplate.Height = 23;
            // 
            // btn_check
            // 
            resources.ApplyResources(this.btn_check, "btn_check");
            this.btn_check.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_check.FlatAppearance.BorderSize = 0;
            this.btn_check.ForeColor = System.Drawing.Color.Black;
            this.btn_check.Name = "btn_check";
            this.btn_check.UseVisualStyleBackColor = false;
            this.btn_check.Click += new System.EventHandler(this.btn_check_Click);
            // 
            // btn_readdatabase
            // 
            resources.ApplyResources(this.btn_readdatabase, "btn_readdatabase");
            this.btn_readdatabase.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_readdatabase.FlatAppearance.BorderSize = 0;
            this.btn_readdatabase.ForeColor = System.Drawing.Color.Black;
            this.btn_readdatabase.Name = "btn_readdatabase";
            this.btn_readdatabase.UseVisualStyleBackColor = false;
            this.btn_readdatabase.Click += new System.EventHandler(this.btn_readdatabase_Click);
            // 
            // btn_toexcel
            // 
            resources.ApplyResources(this.btn_toexcel, "btn_toexcel");
            this.btn_toexcel.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_toexcel.FlatAppearance.BorderSize = 0;
            this.btn_toexcel.ForeColor = System.Drawing.Color.Black;
            this.btn_toexcel.Name = "btn_toexcel";
            this.btn_toexcel.UseVisualStyleBackColor = false;
            this.btn_toexcel.Click += new System.EventHandler(this.btn_toexcel_Click);
            // 
            // btn_deletedatabase
            // 
            resources.ApplyResources(this.btn_deletedatabase, "btn_deletedatabase");
            this.btn_deletedatabase.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_deletedatabase.FlatAppearance.BorderSize = 0;
            this.btn_deletedatabase.ForeColor = System.Drawing.Color.Black;
            this.btn_deletedatabase.Name = "btn_deletedatabase";
            this.btn_deletedatabase.UseVisualStyleBackColor = false;
            this.btn_deletedatabase.Click += new System.EventHandler(this.btn_deletedatabase_Click);
            // 
            // btn_queryWeight
            // 
            resources.ApplyResources(this.btn_queryWeight, "btn_queryWeight");
            this.btn_queryWeight.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_queryWeight.FlatAppearance.BorderSize = 0;
            this.btn_queryWeight.ForeColor = System.Drawing.Color.Black;
            this.btn_queryWeight.Name = "btn_queryWeight";
            this.btn_queryWeight.UseVisualStyleBackColor = false;
            this.btn_queryWeight.Click += new System.EventHandler(this.btn_queryWeight_Click);
            // 
            // btn_connectdatabase
            // 
            resources.ApplyResources(this.btn_connectdatabase, "btn_connectdatabase");
            this.btn_connectdatabase.BackColor = System.Drawing.Color.PaleTurquoise;
            this.btn_connectdatabase.FlatAppearance.BorderSize = 0;
            this.btn_connectdatabase.ForeColor = System.Drawing.Color.Black;
            this.btn_connectdatabase.Name = "btn_connectdatabase";
            this.btn_connectdatabase.UseVisualStyleBackColor = false;
            this.btn_connectdatabase.Click += new System.EventHandler(this.btn_connectdatabase_Click);
            // 
            // groupBox4
            // 
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Controls.Add(this.dateTimePicker3);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            // 
            // dateTimePicker3
            // 
            resources.ApplyResources(this.dateTimePicker3, "dateTimePicker3");
            this.dateTimePicker3.Name = "dateTimePicker3";
            // 
            // groupBox3
            // 
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Controls.Add(this.dateTimePicker2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // dateTimePicker2
            // 
            resources.ApplyResources(this.dateTimePicker2, "dateTimePicker2");
            this.dateTimePicker2.Name = "dateTimePicker2";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // textBox_wheeltype
            // 
            resources.ApplyResources(this.textBox_wheeltype, "textBox_wheeltype");
            this.textBox_wheeltype.Name = "textBox_wheeltype";
            // 
            // cbox_timeH1
            // 
            resources.ApplyResources(this.cbox_timeH1, "cbox_timeH1");
            this.cbox_timeH1.FormattingEnabled = true;
            this.cbox_timeH1.Items.AddRange(new object[] {
            resources.GetString("cbox_timeH1.Items"),
            resources.GetString("cbox_timeH1.Items1"),
            resources.GetString("cbox_timeH1.Items2"),
            resources.GetString("cbox_timeH1.Items3"),
            resources.GetString("cbox_timeH1.Items4"),
            resources.GetString("cbox_timeH1.Items5"),
            resources.GetString("cbox_timeH1.Items6"),
            resources.GetString("cbox_timeH1.Items7"),
            resources.GetString("cbox_timeH1.Items8"),
            resources.GetString("cbox_timeH1.Items9"),
            resources.GetString("cbox_timeH1.Items10"),
            resources.GetString("cbox_timeH1.Items11"),
            resources.GetString("cbox_timeH1.Items12"),
            resources.GetString("cbox_timeH1.Items13"),
            resources.GetString("cbox_timeH1.Items14"),
            resources.GetString("cbox_timeH1.Items15"),
            resources.GetString("cbox_timeH1.Items16"),
            resources.GetString("cbox_timeH1.Items17"),
            resources.GetString("cbox_timeH1.Items18"),
            resources.GetString("cbox_timeH1.Items19"),
            resources.GetString("cbox_timeH1.Items20"),
            resources.GetString("cbox_timeH1.Items21"),
            resources.GetString("cbox_timeH1.Items22"),
            resources.GetString("cbox_timeH1.Items23")});
            this.cbox_timeH1.Name = "cbox_timeH1";
            // 
            // cbox_timeM1
            // 
            resources.ApplyResources(this.cbox_timeM1, "cbox_timeM1");
            this.cbox_timeM1.FormattingEnabled = true;
            this.cbox_timeM1.Items.AddRange(new object[] {
            resources.GetString("cbox_timeM1.Items"),
            resources.GetString("cbox_timeM1.Items1"),
            resources.GetString("cbox_timeM1.Items2"),
            resources.GetString("cbox_timeM1.Items3"),
            resources.GetString("cbox_timeM1.Items4"),
            resources.GetString("cbox_timeM1.Items5"),
            resources.GetString("cbox_timeM1.Items6"),
            resources.GetString("cbox_timeM1.Items7"),
            resources.GetString("cbox_timeM1.Items8"),
            resources.GetString("cbox_timeM1.Items9"),
            resources.GetString("cbox_timeM1.Items10"),
            resources.GetString("cbox_timeM1.Items11"),
            resources.GetString("cbox_timeM1.Items12"),
            resources.GetString("cbox_timeM1.Items13"),
            resources.GetString("cbox_timeM1.Items14"),
            resources.GetString("cbox_timeM1.Items15"),
            resources.GetString("cbox_timeM1.Items16"),
            resources.GetString("cbox_timeM1.Items17"),
            resources.GetString("cbox_timeM1.Items18"),
            resources.GetString("cbox_timeM1.Items19"),
            resources.GetString("cbox_timeM1.Items20"),
            resources.GetString("cbox_timeM1.Items21"),
            resources.GetString("cbox_timeM1.Items22"),
            resources.GetString("cbox_timeM1.Items23"),
            resources.GetString("cbox_timeM1.Items24"),
            resources.GetString("cbox_timeM1.Items25"),
            resources.GetString("cbox_timeM1.Items26"),
            resources.GetString("cbox_timeM1.Items27"),
            resources.GetString("cbox_timeM1.Items28"),
            resources.GetString("cbox_timeM1.Items29"),
            resources.GetString("cbox_timeM1.Items30"),
            resources.GetString("cbox_timeM1.Items31"),
            resources.GetString("cbox_timeM1.Items32"),
            resources.GetString("cbox_timeM1.Items33"),
            resources.GetString("cbox_timeM1.Items34"),
            resources.GetString("cbox_timeM1.Items35"),
            resources.GetString("cbox_timeM1.Items36"),
            resources.GetString("cbox_timeM1.Items37"),
            resources.GetString("cbox_timeM1.Items38"),
            resources.GetString("cbox_timeM1.Items39"),
            resources.GetString("cbox_timeM1.Items40"),
            resources.GetString("cbox_timeM1.Items41"),
            resources.GetString("cbox_timeM1.Items42"),
            resources.GetString("cbox_timeM1.Items43"),
            resources.GetString("cbox_timeM1.Items44"),
            resources.GetString("cbox_timeM1.Items45"),
            resources.GetString("cbox_timeM1.Items46"),
            resources.GetString("cbox_timeM1.Items47"),
            resources.GetString("cbox_timeM1.Items48"),
            resources.GetString("cbox_timeM1.Items49"),
            resources.GetString("cbox_timeM1.Items50"),
            resources.GetString("cbox_timeM1.Items51"),
            resources.GetString("cbox_timeM1.Items52"),
            resources.GetString("cbox_timeM1.Items53"),
            resources.GetString("cbox_timeM1.Items54"),
            resources.GetString("cbox_timeM1.Items55"),
            resources.GetString("cbox_timeM1.Items56"),
            resources.GetString("cbox_timeM1.Items57"),
            resources.GetString("cbox_timeM1.Items58"),
            resources.GetString("cbox_timeM1.Items59")});
            this.cbox_timeM1.Name = "cbox_timeM1";
            // 
            // cbox_timeS1
            // 
            resources.ApplyResources(this.cbox_timeS1, "cbox_timeS1");
            this.cbox_timeS1.FormattingEnabled = true;
            this.cbox_timeS1.Items.AddRange(new object[] {
            resources.GetString("cbox_timeS1.Items"),
            resources.GetString("cbox_timeS1.Items1"),
            resources.GetString("cbox_timeS1.Items2"),
            resources.GetString("cbox_timeS1.Items3"),
            resources.GetString("cbox_timeS1.Items4"),
            resources.GetString("cbox_timeS1.Items5"),
            resources.GetString("cbox_timeS1.Items6"),
            resources.GetString("cbox_timeS1.Items7"),
            resources.GetString("cbox_timeS1.Items8"),
            resources.GetString("cbox_timeS1.Items9"),
            resources.GetString("cbox_timeS1.Items10"),
            resources.GetString("cbox_timeS1.Items11"),
            resources.GetString("cbox_timeS1.Items12"),
            resources.GetString("cbox_timeS1.Items13"),
            resources.GetString("cbox_timeS1.Items14"),
            resources.GetString("cbox_timeS1.Items15"),
            resources.GetString("cbox_timeS1.Items16"),
            resources.GetString("cbox_timeS1.Items17"),
            resources.GetString("cbox_timeS1.Items18"),
            resources.GetString("cbox_timeS1.Items19"),
            resources.GetString("cbox_timeS1.Items20"),
            resources.GetString("cbox_timeS1.Items21"),
            resources.GetString("cbox_timeS1.Items22"),
            resources.GetString("cbox_timeS1.Items23"),
            resources.GetString("cbox_timeS1.Items24"),
            resources.GetString("cbox_timeS1.Items25"),
            resources.GetString("cbox_timeS1.Items26"),
            resources.GetString("cbox_timeS1.Items27"),
            resources.GetString("cbox_timeS1.Items28"),
            resources.GetString("cbox_timeS1.Items29"),
            resources.GetString("cbox_timeS1.Items30"),
            resources.GetString("cbox_timeS1.Items31"),
            resources.GetString("cbox_timeS1.Items32"),
            resources.GetString("cbox_timeS1.Items33"),
            resources.GetString("cbox_timeS1.Items34"),
            resources.GetString("cbox_timeS1.Items35"),
            resources.GetString("cbox_timeS1.Items36"),
            resources.GetString("cbox_timeS1.Items37"),
            resources.GetString("cbox_timeS1.Items38"),
            resources.GetString("cbox_timeS1.Items39"),
            resources.GetString("cbox_timeS1.Items40"),
            resources.GetString("cbox_timeS1.Items41"),
            resources.GetString("cbox_timeS1.Items42"),
            resources.GetString("cbox_timeS1.Items43"),
            resources.GetString("cbox_timeS1.Items44"),
            resources.GetString("cbox_timeS1.Items45"),
            resources.GetString("cbox_timeS1.Items46"),
            resources.GetString("cbox_timeS1.Items47"),
            resources.GetString("cbox_timeS1.Items48"),
            resources.GetString("cbox_timeS1.Items49"),
            resources.GetString("cbox_timeS1.Items50"),
            resources.GetString("cbox_timeS1.Items51"),
            resources.GetString("cbox_timeS1.Items52"),
            resources.GetString("cbox_timeS1.Items53"),
            resources.GetString("cbox_timeS1.Items54"),
            resources.GetString("cbox_timeS1.Items55"),
            resources.GetString("cbox_timeS1.Items56"),
            resources.GetString("cbox_timeS1.Items57"),
            resources.GetString("cbox_timeS1.Items58"),
            resources.GetString("cbox_timeS1.Items59")});
            this.cbox_timeS1.Name = "cbox_timeS1";
            // 
            // cbox_timeS2
            // 
            resources.ApplyResources(this.cbox_timeS2, "cbox_timeS2");
            this.cbox_timeS2.FormattingEnabled = true;
            this.cbox_timeS2.Items.AddRange(new object[] {
            resources.GetString("cbox_timeS2.Items"),
            resources.GetString("cbox_timeS2.Items1"),
            resources.GetString("cbox_timeS2.Items2"),
            resources.GetString("cbox_timeS2.Items3"),
            resources.GetString("cbox_timeS2.Items4"),
            resources.GetString("cbox_timeS2.Items5"),
            resources.GetString("cbox_timeS2.Items6"),
            resources.GetString("cbox_timeS2.Items7"),
            resources.GetString("cbox_timeS2.Items8"),
            resources.GetString("cbox_timeS2.Items9"),
            resources.GetString("cbox_timeS2.Items10"),
            resources.GetString("cbox_timeS2.Items11"),
            resources.GetString("cbox_timeS2.Items12"),
            resources.GetString("cbox_timeS2.Items13"),
            resources.GetString("cbox_timeS2.Items14"),
            resources.GetString("cbox_timeS2.Items15"),
            resources.GetString("cbox_timeS2.Items16"),
            resources.GetString("cbox_timeS2.Items17"),
            resources.GetString("cbox_timeS2.Items18"),
            resources.GetString("cbox_timeS2.Items19"),
            resources.GetString("cbox_timeS2.Items20"),
            resources.GetString("cbox_timeS2.Items21"),
            resources.GetString("cbox_timeS2.Items22"),
            resources.GetString("cbox_timeS2.Items23"),
            resources.GetString("cbox_timeS2.Items24"),
            resources.GetString("cbox_timeS2.Items25"),
            resources.GetString("cbox_timeS2.Items26"),
            resources.GetString("cbox_timeS2.Items27"),
            resources.GetString("cbox_timeS2.Items28"),
            resources.GetString("cbox_timeS2.Items29"),
            resources.GetString("cbox_timeS2.Items30"),
            resources.GetString("cbox_timeS2.Items31"),
            resources.GetString("cbox_timeS2.Items32"),
            resources.GetString("cbox_timeS2.Items33"),
            resources.GetString("cbox_timeS2.Items34"),
            resources.GetString("cbox_timeS2.Items35"),
            resources.GetString("cbox_timeS2.Items36"),
            resources.GetString("cbox_timeS2.Items37"),
            resources.GetString("cbox_timeS2.Items38"),
            resources.GetString("cbox_timeS2.Items39"),
            resources.GetString("cbox_timeS2.Items40"),
            resources.GetString("cbox_timeS2.Items41"),
            resources.GetString("cbox_timeS2.Items42"),
            resources.GetString("cbox_timeS2.Items43"),
            resources.GetString("cbox_timeS2.Items44"),
            resources.GetString("cbox_timeS2.Items45"),
            resources.GetString("cbox_timeS2.Items46"),
            resources.GetString("cbox_timeS2.Items47"),
            resources.GetString("cbox_timeS2.Items48"),
            resources.GetString("cbox_timeS2.Items49"),
            resources.GetString("cbox_timeS2.Items50"),
            resources.GetString("cbox_timeS2.Items51"),
            resources.GetString("cbox_timeS2.Items52"),
            resources.GetString("cbox_timeS2.Items53"),
            resources.GetString("cbox_timeS2.Items54"),
            resources.GetString("cbox_timeS2.Items55"),
            resources.GetString("cbox_timeS2.Items56"),
            resources.GetString("cbox_timeS2.Items57"),
            resources.GetString("cbox_timeS2.Items58"),
            resources.GetString("cbox_timeS2.Items59")});
            this.cbox_timeS2.Name = "cbox_timeS2";
            // 
            // cbox_timeM2
            // 
            resources.ApplyResources(this.cbox_timeM2, "cbox_timeM2");
            this.cbox_timeM2.FormattingEnabled = true;
            this.cbox_timeM2.Items.AddRange(new object[] {
            resources.GetString("cbox_timeM2.Items"),
            resources.GetString("cbox_timeM2.Items1"),
            resources.GetString("cbox_timeM2.Items2"),
            resources.GetString("cbox_timeM2.Items3"),
            resources.GetString("cbox_timeM2.Items4"),
            resources.GetString("cbox_timeM2.Items5"),
            resources.GetString("cbox_timeM2.Items6"),
            resources.GetString("cbox_timeM2.Items7"),
            resources.GetString("cbox_timeM2.Items8"),
            resources.GetString("cbox_timeM2.Items9"),
            resources.GetString("cbox_timeM2.Items10"),
            resources.GetString("cbox_timeM2.Items11"),
            resources.GetString("cbox_timeM2.Items12"),
            resources.GetString("cbox_timeM2.Items13"),
            resources.GetString("cbox_timeM2.Items14"),
            resources.GetString("cbox_timeM2.Items15"),
            resources.GetString("cbox_timeM2.Items16"),
            resources.GetString("cbox_timeM2.Items17"),
            resources.GetString("cbox_timeM2.Items18"),
            resources.GetString("cbox_timeM2.Items19"),
            resources.GetString("cbox_timeM2.Items20"),
            resources.GetString("cbox_timeM2.Items21"),
            resources.GetString("cbox_timeM2.Items22"),
            resources.GetString("cbox_timeM2.Items23"),
            resources.GetString("cbox_timeM2.Items24"),
            resources.GetString("cbox_timeM2.Items25"),
            resources.GetString("cbox_timeM2.Items26"),
            resources.GetString("cbox_timeM2.Items27"),
            resources.GetString("cbox_timeM2.Items28"),
            resources.GetString("cbox_timeM2.Items29"),
            resources.GetString("cbox_timeM2.Items30"),
            resources.GetString("cbox_timeM2.Items31"),
            resources.GetString("cbox_timeM2.Items32"),
            resources.GetString("cbox_timeM2.Items33"),
            resources.GetString("cbox_timeM2.Items34"),
            resources.GetString("cbox_timeM2.Items35"),
            resources.GetString("cbox_timeM2.Items36"),
            resources.GetString("cbox_timeM2.Items37"),
            resources.GetString("cbox_timeM2.Items38"),
            resources.GetString("cbox_timeM2.Items39"),
            resources.GetString("cbox_timeM2.Items40"),
            resources.GetString("cbox_timeM2.Items41"),
            resources.GetString("cbox_timeM2.Items42"),
            resources.GetString("cbox_timeM2.Items43"),
            resources.GetString("cbox_timeM2.Items44"),
            resources.GetString("cbox_timeM2.Items45"),
            resources.GetString("cbox_timeM2.Items46"),
            resources.GetString("cbox_timeM2.Items47"),
            resources.GetString("cbox_timeM2.Items48"),
            resources.GetString("cbox_timeM2.Items49"),
            resources.GetString("cbox_timeM2.Items50"),
            resources.GetString("cbox_timeM2.Items51"),
            resources.GetString("cbox_timeM2.Items52"),
            resources.GetString("cbox_timeM2.Items53"),
            resources.GetString("cbox_timeM2.Items54"),
            resources.GetString("cbox_timeM2.Items55"),
            resources.GetString("cbox_timeM2.Items56"),
            resources.GetString("cbox_timeM2.Items57"),
            resources.GetString("cbox_timeM2.Items58"),
            resources.GetString("cbox_timeM2.Items59")});
            this.cbox_timeM2.Name = "cbox_timeM2";
            // 
            // cbox_timeH2
            // 
            resources.ApplyResources(this.cbox_timeH2, "cbox_timeH2");
            this.cbox_timeH2.FormattingEnabled = true;
            this.cbox_timeH2.Items.AddRange(new object[] {
            resources.GetString("cbox_timeH2.Items"),
            resources.GetString("cbox_timeH2.Items1"),
            resources.GetString("cbox_timeH2.Items2"),
            resources.GetString("cbox_timeH2.Items3"),
            resources.GetString("cbox_timeH2.Items4"),
            resources.GetString("cbox_timeH2.Items5"),
            resources.GetString("cbox_timeH2.Items6"),
            resources.GetString("cbox_timeH2.Items7"),
            resources.GetString("cbox_timeH2.Items8"),
            resources.GetString("cbox_timeH2.Items9"),
            resources.GetString("cbox_timeH2.Items10"),
            resources.GetString("cbox_timeH2.Items11"),
            resources.GetString("cbox_timeH2.Items12"),
            resources.GetString("cbox_timeH2.Items13"),
            resources.GetString("cbox_timeH2.Items14"),
            resources.GetString("cbox_timeH2.Items15"),
            resources.GetString("cbox_timeH2.Items16"),
            resources.GetString("cbox_timeH2.Items17"),
            resources.GetString("cbox_timeH2.Items18"),
            resources.GetString("cbox_timeH2.Items19"),
            resources.GetString("cbox_timeH2.Items20"),
            resources.GetString("cbox_timeH2.Items21"),
            resources.GetString("cbox_timeH2.Items22"),
            resources.GetString("cbox_timeH2.Items23")});
            this.cbox_timeH2.Name = "cbox_timeH2";
            // 
            // label_H1
            // 
            resources.ApplyResources(this.label_H1, "label_H1");
            this.label_H1.Name = "label_H1";
            // 
            // label_M1
            // 
            resources.ApplyResources(this.label_M1, "label_M1");
            this.label_M1.Name = "label_M1";
            // 
            // label_S1
            // 
            resources.ApplyResources(this.label_S1, "label_S1");
            this.label_S1.Name = "label_S1";
            // 
            // label_M2
            // 
            resources.ApplyResources(this.label_M2, "label_M2");
            this.label_M2.Name = "label_M2";
            // 
            // label_H2
            // 
            resources.ApplyResources(this.label_H2, "label_H2");
            this.label_H2.Name = "label_H2";
            // 
            // label_S2
            // 
            resources.ApplyResources(this.label_S2, "label_S2");
            this.label_S2.Name = "label_S2";
            // 
            // DataBase
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.Controls.Add(this.label_S2);
            this.Controls.Add(this.label_H2);
            this.Controls.Add(this.label_M2);
            this.Controls.Add(this.label_S1);
            this.Controls.Add(this.label_M1);
            this.Controls.Add(this.label_H1);
            this.Controls.Add(this.cbox_timeS2);
            this.Controls.Add(this.cbox_timeM2);
            this.Controls.Add(this.cbox_timeH2);
            this.Controls.Add(this.cbox_timeS1);
            this.Controls.Add(this.cbox_timeM1);
            this.Controls.Add(this.cbox_timeH1);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.textBox_wheeltype);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btn_connectdatabase);
            this.Controls.Add(this.btn_check);
            this.Controls.Add(this.btn_queryWeight);
            this.Controls.Add(this.btn_readdatabase);
            this.Controls.Add(this.btn_toexcel);
            this.Controls.Add(this.btn_deletedatabase);
            this.Controls.Add(this.dataGridView1);
            this.Controls.Add(this.groupBox1);
            this.Name = "DataBase";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.Form_Closed);
            this.Load += new System.EventHandler(this.FormLoad);
            this.Resize += new System.EventHandler(this.Form1_Resize);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.Button btn_check;
        private System.Windows.Forms.Button btn_readdatabase;
        private System.Windows.Forms.Button btn_toexcel;
        private System.Windows.Forms.Button btn_deletedatabase;
        private System.Windows.Forms.Button btn_queryWeight;
        private System.Windows.Forms.Button btn_connectdatabase;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.DateTimePicker dateTimePicker3;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.DateTimePicker dateTimePicker2;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox textBox_wheeltype;
        private System.Windows.Forms.ComboBox cbox_timeH1;
        private System.Windows.Forms.ComboBox cbox_timeM1;
        private System.Windows.Forms.ComboBox cbox_timeS1;
        private System.Windows.Forms.ComboBox cbox_timeS2;
        private System.Windows.Forms.ComboBox cbox_timeM2;
        private System.Windows.Forms.ComboBox cbox_timeH2;
        private System.Windows.Forms.Label label_H1;
        private System.Windows.Forms.Label label_M1;
        private System.Windows.Forms.Label label_S1;
        private System.Windows.Forms.Label label_M2;
        private System.Windows.Forms.Label label_H2;
        private System.Windows.Forms.Label label_S2;
    }
}