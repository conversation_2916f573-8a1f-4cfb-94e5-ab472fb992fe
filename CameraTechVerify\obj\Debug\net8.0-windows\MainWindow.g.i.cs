﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "62F5AA8C320B420ED36F939C40D616D6D4DCDAE6"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using CameraTechVerify;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CameraTechVerify {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeText;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DeviceListBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectButton;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DeviceInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameText;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelNameText;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberText;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IpAddressText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionTypeText;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ManufacturerText;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageInfoText;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoFitCheckBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ZoomText;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CameraImage;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultImagePanel;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ImageOverlay;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageSizeText;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrameRateText;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrameCountText;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartGrabbingButton;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopGrabbingButton;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CaptureButton;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveImageButton;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExposureTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ExposureTimeSlider;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GainTextBox;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider GainSlider;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FrameRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FrameRateSlider;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TriggerModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SoftwareTriggerButton;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WidthTextBox;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HeightTextBox;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ROIXTextBox;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ROIYTextBox;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ROIWidthTextBox;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ROIHeightTextBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoExposureCheckBox;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoGainCheckBox;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoWhiteBalanceCheckBox;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider GammaSlider;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GammaValueText;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider BrightnessSlider;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrightnessValueText;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ContrastSlider;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContrastValueText;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentExposureText;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentGainText;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentFrameRateText;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageCountText;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LostFrameText;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CameraTechVerify;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DeviceListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 75 "..\..\..\MainWindow.xaml"
            this.DeviceListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DeviceListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 93 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshDevices_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ConnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\MainWindow.xaml"
            this.ConnectButton.Click += new System.Windows.RoutedEventHandler(this.ConnectCamera_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\MainWindow.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.DisconnectCamera_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DeviceInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.DeviceNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ModelNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SerialNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.IpAddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ConnectionTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ManufacturerText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ImageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.AutoFitCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.ZoomText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 18:
            this.CameraImage = ((System.Windows.Controls.Image)(target));
            return;
            case 19:
            this.DefaultImagePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 20:
            this.ImageOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.ImageSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FrameRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.FrameCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.StartGrabbingButton = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\MainWindow.xaml"
            this.StartGrabbingButton.Click += new System.Windows.RoutedEventHandler(this.StartGrabbing_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.StopGrabbingButton = ((System.Windows.Controls.Button)(target));
            
            #line 209 "..\..\..\MainWindow.xaml"
            this.StopGrabbingButton.Click += new System.Windows.RoutedEventHandler(this.StopGrabbing_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CaptureButton = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\MainWindow.xaml"
            this.CaptureButton.Click += new System.Windows.RoutedEventHandler(this.Capture_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SaveImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\MainWindow.xaml"
            this.SaveImageButton.Click += new System.Windows.RoutedEventHandler(this.SaveImage_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ExposureTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 236 "..\..\..\MainWindow.xaml"
            this.ExposureTimeTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ExposureTimeTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ExposureTimeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 239 "..\..\..\MainWindow.xaml"
            this.ExposureTimeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ExposureTimeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.GainTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 246 "..\..\..\MainWindow.xaml"
            this.GainTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.GainTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 31:
            this.GainSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 249 "..\..\..\MainWindow.xaml"
            this.GainSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.GainSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 32:
            this.FrameRateTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 256 "..\..\..\MainWindow.xaml"
            this.FrameRateTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.FrameRateTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.FrameRateSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 259 "..\..\..\MainWindow.xaml"
            this.FrameRateSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.FrameRateSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 34:
            this.TriggerModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 266 "..\..\..\MainWindow.xaml"
            this.TriggerModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TriggerModeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 35:
            this.SoftwareTriggerButton = ((System.Windows.Controls.Button)(target));
            
            #line 276 "..\..\..\MainWindow.xaml"
            this.SoftwareTriggerButton.Click += new System.Windows.RoutedEventHandler(this.SoftwareTrigger_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.WidthTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 288 "..\..\..\MainWindow.xaml"
            this.WidthTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ResolutionTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 37:
            this.HeightTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 294 "..\..\..\MainWindow.xaml"
            this.HeightTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ResolutionTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 38:
            this.ROIXTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 39:
            this.ROIYTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 40:
            this.ROIWidthTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 41:
            this.ROIHeightTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 42:
            
            #line 324 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetROI_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.AutoExposureCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 333 "..\..\..\MainWindow.xaml"
            this.AutoExposureCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            
            #line 333 "..\..\..\MainWindow.xaml"
            this.AutoExposureCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            return;
            case 44:
            this.AutoGainCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 335 "..\..\..\MainWindow.xaml"
            this.AutoGainCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            
            #line 335 "..\..\..\MainWindow.xaml"
            this.AutoGainCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            return;
            case 45:
            this.AutoWhiteBalanceCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 337 "..\..\..\MainWindow.xaml"
            this.AutoWhiteBalanceCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            
            #line 337 "..\..\..\MainWindow.xaml"
            this.AutoWhiteBalanceCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoFunction_Changed);
            
            #line default
            #line hidden
            return;
            case 46:
            this.GammaSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 47:
            this.GammaValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.BrightnessSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 49:
            this.BrightnessValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.ContrastSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 51:
            this.ContrastValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            
            #line 377 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadParameters_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            
            #line 380 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveParameters_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            
            #line 383 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetParameters_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            
            #line 386 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GetCurrentParameters_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.CurrentExposureText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 57:
            this.CurrentGainText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.CurrentFrameRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 59:
            this.ImageCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 60:
            this.LostFrameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 61:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

