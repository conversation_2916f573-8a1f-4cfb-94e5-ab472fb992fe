using System;
using System.Reflection;
using System.Diagnostics;

namespace CameraTechVerify.Test
{
    /// <summary>
    /// MVS SDK 加载测试
    /// </summary>
    public static class MVSLoadTest
    {
        public static void TestMVSLoading()
        {
            Debug.WriteLine("=== MVS SDK 加载测试开始 ===");
            Console.WriteLine("=== MVS SDK 加载测试开始 ===");

            try
            {
                // 1. 检查已加载的程序集
                Debug.WriteLine("1. 检查已加载的程序集...");
                Console.WriteLine("1. 检查已加载的程序集...");
                
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    var name = assembly.GetName().Name;
                    if (name != null && name.Contains("MvCamera"))
                    {
                        Debug.WriteLine($"   找到已加载的 MVS 程序集: {name}");
                        Console.WriteLine($"   找到已加载的 MVS 程序集: {name}");
                    }
                }

                // 2. 尝试强制加载 MVS 程序集
                Debug.WriteLine("2. 尝试强制加载 MVS 程序集...");
                Console.WriteLine("2. 尝试强制加载 MVS 程序集...");
                
                string dllPath = @"D:\MVS\Development\DotNet\win64\MvCameraControl.Net.dll";
                
                if (System.IO.File.Exists(dllPath))
                {
                    Debug.WriteLine($"   DLL 文件存在: {dllPath}");
                    Console.WriteLine($"   DLL 文件存在: {dllPath}");
                    
                    var assembly = Assembly.LoadFrom(dllPath);
                    Debug.WriteLine($"   ✓ 程序集加载成功: {assembly.FullName}");
                    Console.WriteLine($"   ✓ 程序集加载成功: {assembly.FullName}");

                    // 3. 查找相机类型
                    Debug.WriteLine("3. 查找相机类型...");
                    Console.WriteLine("3. 查找相机类型...");
                    
                    var types = assembly.GetTypes();
                    foreach (var type in types)
                    {
                        if (type.Name.Contains("Camera") || type.Name.Contains("MV"))
                        {
                            Debug.WriteLine($"   找到类型: {type.FullName}");
                            Console.WriteLine($"   找到类型: {type.FullName}");
                            
                            // 4. 查找方法
                            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance);
                            foreach (var method in methods)
                            {
                                if (method.Name.Contains("Open") || method.Name.Contains("Create") || method.Name.Contains("Enum"))
                                {
                                    Debug.WriteLine($"     方法: {method.Name}");
                                    Console.WriteLine($"     方法: {method.Name}");
                                }
                            }
                        }
                    }
                }
                else
                {
                    Debug.WriteLine($"   ✗ DLL 文件不存在: {dllPath}");
                    Console.WriteLine($"   ✗ DLL 文件不存在: {dllPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"MVS SDK 加载测试失败: {ex}");
                Console.WriteLine($"MVS SDK 加载测试失败: {ex}");
            }

            Debug.WriteLine("=== MVS SDK 加载测试结束 ===");
            Console.WriteLine("=== MVS SDK 加载测试结束 ===");
        }
    }
}
