using System;
using System.Windows;

namespace HalconAlgorithmManager.Dialogs
{
    public partial class NewAlgorithmDialog : Window
    {
        public string AlgorithmId { get; private set; }
        public string AlgorithmName { get; private set; }
        public string Category { get; private set; }
        public string Engine { get; private set; }
        public string AlgorithmType { get; private set; }
        public string Description { get; private set; }

        public NewAlgorithmDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            CreateTimeText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            // 自动生成算法编号
            GenerateAlgorithmId();
        }

        private void GenerateAlgorithmId()
        {
            // 根据分类生成编号前缀
            string prefix = "A";
            if (CategoryComboBox.SelectedItem != null)
            {
                var category = ((System.Windows.Controls.ComboBoxItem)CategoryComboBox.SelectedItem).Content.ToString();
                switch (category)
                {
                    case "轮毂检测算法":
                        prefix = "A";
                        break;
                    case "质量检测算法":
                        prefix = "B";
                        break;
                    case "模板匹配算法":
                        prefix = "C";
                        break;
                    default:
                        prefix = "X";
                        break;
                }
            }
            
            // 生成序号（这里简化为随机数，实际应该查询数据库）
            Random random = new Random();
            int number = random.Next(1000, 9999);
            
            AlgorithmIdTextBox.Text = $"{prefix}{number:D4}";
        }

        private void Create_Click(object sender, RoutedEventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(AlgorithmIdTextBox.Text))
            {
                MessageBox.Show("请输入算法编号！", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                AlgorithmIdTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(AlgorithmNameTextBox.Text))
            {
                MessageBox.Show("请输入算法名称！", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                AlgorithmNameTextBox.Focus();
                return;
            }

            // 检查编号格式
            if (!IsValidAlgorithmId(AlgorithmIdTextBox.Text))
            {
                MessageBox.Show("算法编号格式不正确！\n\n格式要求：字母+4位数字，如：A0001", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                AlgorithmIdTextBox.Focus();
                return;
            }

            // 保存数据
            AlgorithmId = AlgorithmIdTextBox.Text.Trim();
            AlgorithmName = AlgorithmNameTextBox.Text.Trim();
            Category = ((System.Windows.Controls.ComboBoxItem)CategoryComboBox.SelectedItem)?.Content?.ToString() ?? "";
            Engine = ((System.Windows.Controls.ComboBoxItem)EngineComboBox.SelectedItem)?.Content?.ToString() ?? "";
            AlgorithmType = ((System.Windows.Controls.ComboBoxItem)AlgorithmTypeComboBox.SelectedItem)?.Content?.ToString() ?? "";
            Description = DescriptionTextBox.Text.Trim();

            // 确认创建
            var result = MessageBox.Show(
                $"确定要创建以下算法档案吗？\n\n" +
                $"算法编号: {AlgorithmId}\n" +
                $"算法名称: {AlgorithmName}\n" +
                $"算法分类: {Category}\n" +
                $"检测引擎: {Engine}\n" +
                $"算法类型: {AlgorithmType}",
                "创建确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = true;
                Close();
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool IsValidAlgorithmId(string algorithmId)
        {
            if (string.IsNullOrWhiteSpace(algorithmId) || algorithmId.Length != 5)
                return false;

            // 第一个字符必须是字母
            if (!char.IsLetter(algorithmId[0]))
                return false;

            // 后四个字符必须是数字
            for (int i = 1; i < 5; i++)
            {
                if (!char.IsDigit(algorithmId[i]))
                    return false;
            }

            return true;
        }

        private void CategoryComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            // 当分类改变时重新生成编号
            GenerateAlgorithmId();
        }
    }
}
