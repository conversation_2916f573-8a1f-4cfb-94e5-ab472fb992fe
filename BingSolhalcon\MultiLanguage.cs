﻿using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BingSolhalcon
{
    /// <summary>
    /// 语言切换
    /// </summary>
    class MultiLanguage
    {
        //当前默认语言
        public static string DefaultLanguage = "zh";


        /// <summary>
        /// 修改默认语言
        /// </summary>
        /// <param name="lang">待设置默认语言</param>
        public void SetDefaultLanguage(string lang)
        {
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(lang);
            DefaultLanguage = lang;
            Properties.Settings.Default.DefaultLanguage = lang;
            Properties.Settings.Default.Save();
        }

        public void LoadDefaultLanguage(Form form, Type formType)
        {
            string language = Properties.Settings.Default.DefaultLanguage;
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(language);
            MultiLanguage multiLanguage = new MultiLanguage();
            //加载默认语言
            multiLanguage.LoadLanguage(form, formType);
        }

        /// <summary>
        /// 加载语言
        /// </summary>
        /// <param name="form">加载语言的窗口</param>
        /// <param name="formType">窗口的类型</param>
        public void LoadLanguage(Form form, Type formType)
        {
            if (form != null)
            {
                System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(formType);
                resources.ApplyResources(form, "$this");
                Loading(form, resources);
            }
        }

        /// <summary>
        /// 加载语言
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="resources">语言资源</param>
        private static void Loading(Control control, System.ComponentModel.ComponentResourceManager resources)
        {
            if (control is RibbonControl)
            {
                //将资源与控件对应
                resources.ApplyResources(control, control.Name);
                RibbonControl ms = (RibbonControl)control;
                if (ms.Pages.Count > 0)
                {
                    foreach (RibbonPage c in ms.Pages)
                    {
                        resources.ApplyResources(c, c.Name);
                    }
                }
                if (ms.Items.Count > 0)
                {
                    foreach (BarItem c in ms.Items)
                    {
                        //将资源与控件对应
                        Loading(c, resources);
                    }
                }
            }

            if (control is ComboBox)
            {
                //将资源与控件对应
                //登陆界面中的combox暂时处理
                resources.ApplyResources(control, control.Name);
                ComboBox cb = (ComboBox)control;
                string language = Properties.Settings.Default.DefaultLanguage;
                if (cb.FindForm().Name == "LogIn"&& language=="en")
                {
                    cb.Items.Clear();
                    cb.Items.Add("Operator");
                    cb.Items.Add("Technician");
                    cb.Items.Add("Expert");
                }
                if (cb.FindForm().Name == "LogIn" && language == "fr")
                {
                    cb.Items.Clear();
                    cb.Items.Add("Operator");
                    cb.Items.Add("Technician");
                    cb.Items.Add("Expert");
                }
                if (cb.FindForm().Name == "LogIn" && language == "es")
                {
                    cb.Items.Clear();
                    cb.Items.Add("operador");
                    cb.Items.Add("técnico");
                    cb.Items.Add("experto");
                }


            }


            foreach (Control c in control.Controls)
            {
                if(c is Label)
                    labelResize(c);

                resources.ApplyResources(c, c.Name);
                
                Loading(c, resources);
            }
        }

        private static void Loading(BarItem control, System.ComponentModel.ComponentResourceManager resources)
        {
            resources.ApplyResources(control, control.Name);
        }

        private static  FontStyle fontStyle = FontStyle.Regular;
        private static float fontSize = 0;
        private static int lblWidth = 0;

        public static void labelResize(Control control)
        {
            lblWidth = control.Width;
            fontSize = control.Font.Size;
            fontStyle = control.Font.Style;
            string content = control.Text.Trim();
            FontFamily ff = new FontFamily(control.Font.Name);
            control.Font = new Font(ff, fontSize, fontStyle, GraphicsUnit.World);
            float size = control.Font.Size;
            
            /*
             * 在测量字符串显示的宽度时，使用了自带的函数
             */
            Graphics gh = control.FindForm().CreateGraphics();
            SizeF sf = gh.MeasureString(content, control.Font);
            while (sf.Width > lblWidth)
            {
                size -= 0.001F;
                control.Font = new Font(ff, size, fontStyle, GraphicsUnit.World);
                sf = gh.MeasureString(content, control.Font);
            }
            control.Text = content;

        }

    }

    /// <summary>
    /// MessageBox内置语言切换，重写MessageBox类
    /// </summary>
    class MessageBoxEX
    {
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons, string[] buttonTitles)
        {
            MessageForm frm = new MessageForm(buttons, buttonTitles);
            frm.Show();
            frm.WatchForActivate = true;
            DialogResult result = MessageBox.Show(frm, text, caption, buttons);
            frm.Close();
            return result;
        }

        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons,
            MessageBoxIcon icon, MessageBoxDefaultButton defaultButton, string[] buttonTitles)
        {
            MessageForm frm = new MessageForm(buttons, buttonTitles);
            frm.Show();
            frm.WatchForActivate = true;
            DialogResult result = MessageBox.Show(frm, text, caption, buttons, icon, defaultButton);
            frm.Close();
            return result;
        }

        class MessageForm : Form
        {
            IntPtr _handle;
            MessageBoxButtons _buttons;
            string[] _buttonTitles = null;

            bool _watchForActivate = false;

            public bool WatchForActivate
            {
                get { return _watchForActivate; }
                set { _watchForActivate = value; }
            }

            public MessageForm(MessageBoxButtons buttons, string[] buttonTitles)
            {
                _buttons = buttons;
                _buttonTitles = buttonTitles;

                // Hide self form, and don't show self form in task bar.
                this.Text = "";
                this.StartPosition = FormStartPosition.CenterScreen;
                this.Location = new Point(-32000, -32000);
                this.ShowInTaskbar = false;
                this.TopMost = true;
            }

            protected override void OnShown(EventArgs e)
            {
                base.OnShown(e);
                // Hide self form, don't show self form even in task list.
                NativeWin32API.SetWindowPos(this.Handle, IntPtr.Zero, 0, 0, 0, 0, 659);
            }

            protected override void WndProc(ref System.Windows.Forms.Message m)
            {
                if (_watchForActivate && m.Msg == 0x0006)
                {
                    _watchForActivate = false;
                    _handle = m.LParam;
                    CheckMsgbox();
                }
                base.WndProc(ref m);
            }

            private void CheckMsgbox()
            {
                if (_buttonTitles == null || _buttonTitles.Length == 0)
                    return;

                // Button title index
                int buttonTitleIndex = 0;
                // Get the handle of control in current window.
                IntPtr h = NativeWin32API.GetWindow(_handle, GW_CHILD);

                // Set those custom titles to the three buttons(Default title are: Yes, No and Cancle).
                while (h != IntPtr.Zero)
                {
                    if (NativeWin32API.GetWindowClassName(h).Equals("Button"))
                    {
                        if (_buttonTitles.Length > buttonTitleIndex)
                        {
                            // Changes the text of the specified window's title bar (if it has one). 
                            // If the specified window is a control, the text of the control is changed. 
                            // However, SetWindowText cannot change the text of a control in another application.
                            NativeWin32API.SetWindowText(h, _buttonTitles[buttonTitleIndex]);

                            buttonTitleIndex++;
                        }
                    }

                    // Get the handle of next control in current window.
                    h = NativeWin32API.GetWindow(h, GW_HWNDNEXT);
                }
            }
        }


        public const int GW_CHILD = 5;
        public const int GW_HWNDNEXT = 2;

        public class NativeWin32API
        {
            [DllImport("user32.dll", CharSet = CharSet.Auto)]
            public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int x, int y, int Width, int Height, int flags);
            [DllImport("user32.dll")]
            public static extern IntPtr GetWindow(IntPtr hWnd, Int32 wCmd);
            [DllImport("user32.dll")]
            public static extern bool SetWindowText(IntPtr hWnd, string lpString);
            [DllImport("user32.dll")]
            public static extern int GetClassNameW(IntPtr hWnd, [MarshalAs(UnmanagedType.LPWStr)]StringBuilder lpString, int nMaxCount);

            public static string GetWindowClassName(IntPtr handle)
            {
                StringBuilder sb = new StringBuilder(256);

                // Retrieves the name of the class to which the specified window belongs
                GetClassNameW(handle, sb, sb.Capacity);
                return sb.ToString();
            }
        }

    }
    

    /// <summary>
    /// 将CSV文件转为资源文件，对CSV转datatable操作
    /// </summary>
    class CsvLib
    {
        /// <summary>
        /// 将Csv读入DataTable
        /// </summary>
        /// <param name="filePath">csv文件路径</param>
        /// <param name="n">表示第n行是字段title,第n+1行是记录开始</param>
        public static DataTable Csv2dt(string filePath, int n)
        {
            DataTable dt = new DataTable();
            String csvSplitBy = "(?<=^|,)(\"(?:[^\"]|\"\")*\"|[^,]*)";
            StreamReader reader = new StreamReader(filePath, System.Text.Encoding.Default, false);
            //StreamReader reader = new StreamReader(filePath, System.Text.Encoding.Unicode, false);如果出现乱码多换几种编码方式试一下
            int i = 0, m = 0;
            reader.Peek();
            while (reader.Peek() > 0)
            {
                m = m + 1;
                string str = reader.ReadLine();
                if (m >= n + 1)
                {
                    if (m == n + 1) //如果是字段行，则自动加入字段。
                    {
                        MatchCollection mcs = Regex.Matches(str, csvSplitBy);
                        foreach (Match mc in mcs)
                        {
                            dt.Columns.Add(mc.Value); //增加列标题
                        }
                    }
                    else
                    {
                        MatchCollection mcs = Regex.Matches(str, "(?<=^|,)(\"(?:[^\"]|\"\")*\"|[^,]*)");
                        i = 0;
                        System.Data.DataRow dr = dt.NewRow();
                        foreach (Match mc in mcs)
                        {
                            dr[i] = mc.Value;
                            i++;
                        }
                        dt.Rows.Add(dr);  //DataTable 增加一行     
                    }
                }
            }
            return dt;
        }
    }

    
    

}
