﻿
using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using HalconDotNet;
using System.Resources;
using BingSolhalcon.resources;
using System.Threading;
using System.Collections.Concurrent;

namespace BingSolhalcon.UI
{
    public partial class CircleCaliper : Form
    {
        List<string> listOnit = new List<string>();
        List<string> listNew = new List<string>();


        public delegate void delegateGetDatabasename(ref string databasename,ref string numberofdecimal);
        public event delegateGetDatabasename evenGetDatabasename;
        string m_databasename, numberofdecimal;
        string connection;
        string wheeltype;
        bool m_readimage = true;


        public Caliper m_caliper;
        private boltholeresult m_boltholeresult;
        double m_diameter;

        private float x;//定义当前窗体的宽度
        private float y;//定义当前窗体的高度
        private OpenFileDialog dialog;
        private hwindows.hwindows m_halconwindow;
        SQlFun m_sqlfun;//数据库类

        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx)); //自定义资源字段

        //中心孔测量模型变量
        HObject ho_Image, ho_ContCircle;
        HObject ho_ContoursAffinTrans = null;

        HTuple RowDown = new HTuple(), ColDown = new HTuple();
        HTuple hv_width = new HTuple(), hv_height = new HTuple();
        HTuple hv_Row3 = new HTuple();
        HTuple hv_Column3 = new HTuple(), hv_Radius3 = new HTuple();

        //螺栓孔测量模型变量
        HTuple hv_boltModelRow = new HTuple(), hv_boltModelColumn = new HTuple(), hv_boltModelRadius = new HTuple();
        private double m_holeScale;
        private int m_reduceRow;
        private int m_reduceCol;
        private int m_reduceRadius;
        private int m_reduceRow_C3;
        private int m_reduceCol_C3;
        private int m_reduceRadius_C3;

        // 是否正在绘制测量圆
        private bool mDrawing = false;

        public CircleCaliper()
        {
            InitializeComponent();

            m_caliper = new Caliper();
            dialog = new OpenFileDialog();
            m_sqlfun = new SQlFun();
            m_halconwindow = new hwindows.hwindows();

            HOperatorSet.GenEmptyObj(out ho_Image);
            HOperatorSet.GenEmptyObj(out ho_ContCircle);
            HOperatorSet.GenEmptyObj(out ho_ContoursAffinTrans);

        }
        private void Form1_Resize(object sender, EventArgs e)
        {
            //float newx = (this.Width) / x;
            //float newy = (this.Height) / y;
            //setControls(newx, newy, this);
        }
        private void setControls(float newx, float newy, Control cons)
        {
            //遍历窗体中的控件，重新设置控件的值
            foreach (Control con in cons.Controls)
            {
                //获取控件的Tag属性值，并分割后存储字符串数组
                if (con.Tag != null)
                {
                    string[] mytag = con.Tag.ToString().Split(new char[] { ';' });
                    //根据窗体缩放的比例确定控件的值
                    con.Width = Convert.ToInt32(System.Convert.ToSingle(mytag[0]) * newx);//宽度
                    con.Height = Convert.ToInt32(System.Convert.ToSingle(mytag[1]) * newy);//高度
                    con.Left = Convert.ToInt32(System.Convert.ToSingle(mytag[2]) * newx);//左边距
                    con.Top = Convert.ToInt32(System.Convert.ToSingle(mytag[3]) * newy);//顶边距
                    Single currentSize = System.Convert.ToSingle(mytag[4]) * newy;//字体大小
                    con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                    if (con.Controls.Count > 0)
                    {
                        setControls(newx, newy, con);
                    }
                }
            }
        }
        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ";" + con.Height + ";" + con.Left + ";" + con.Top + ";" + con.Font.Size;
                if (con.Controls.Count > 0)
                {
                    setTag(con);
                }
            }
        }

        private void FormLoad(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(CircleCaliper));
            

            //窗口缩放用
            //x = this.Width;
            //y = this.Height;
            // setTag(this);
            //字体设置
            WindowParaSet winparset = new WindowParaSet();
            winparset.set_display_font(hWindowControl1.HalconWindow, 12, "mono", "true", "false");
            
            //窗口缩放用
            btn_mtrsave.Enabled = false;
            evenGetDatabasename(ref m_databasename,ref numberofdecimal);
            connection = @"Server=" + m_databasename + ";Database=MyDB;Trusted_Connection=SSPI";

            groupBox2.Enabled = true;
            groupBox3.Enabled = false;
            groupBox5.Enabled = false;

            //导入存在的注册图片

            string str = Directory.GetCurrentDirectory() + "\\model\\registerpicture\\";
            DirectoryInfo theFolder = new DirectoryInfo(str);

            FileInfo[] dirInfo = theFolder.GetFiles();
            comboBox_wheellist.Items.Clear(); //初始中心孔类型清空
            listOnit.Clear();
            foreach (FileInfo file in dirInfo)
            {
                if (file.Extension.Equals(".bmp"))
                {
                    string str2, str1;
                    
                    switch (comboBox_selecttype.Text)
                    {
                        case "中心孔":
                        case "Center hole":
                        case "Orificio central":
                        case "Trou au centre":
                            str1 = file.Name.Substring(file.Name.LastIndexOf('.') - 1, 1);

                            if (str1 == "C")
                            {
                                str2 = file.Name.Substring(0, file.Name.LastIndexOf('.'));
                                comboBox_wheellist.Items.Add(str2);

                                listOnit.Add(str2);
                            }



                            break;

                        case "帽止口":
                        case "cap stopper":
                        case "Tope de tapa":
                        case "Bouchon et stop":
                            str1 = file.Name.Substring(file.Name.LastIndexOf('.') - 1, 1);

                            if (str1 == "H")
                            {
                                str2 = file.Name.Substring(0, file.Name.LastIndexOf('.'));
                                comboBox_wheellist.Items.Add(str2);
                                listOnit.Add(str2);
                            }


                            break;

                    }
                }

            }

            try
            {
                m_holeScale = double.Parse(ConfigIni.GetIniKeyValue("参数配置", "中心孔比例", "0.027368337315", Application.StartupPath + "\\config.ini"));

                m_reduceRow = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRow", "3172", Application.StartupPath + "\\config.ini"));
                m_reduceCol = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceCol", "4731", Application.StartupPath + "\\config.ini"));
                m_reduceRadius = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius", "1650", Application.StartupPath + "\\config.ini"));

                m_reduceRow_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRow_C3", "2564", Application.StartupPath + "\\config.ini"));
                m_reduceCol_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceCol_C3", "2283", Application.StartupPath + "\\config.ini"));
                m_reduceRadius_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius_C3", "1892", Application.StartupPath + "\\config.ini"));
            }
            catch
            {
            }

        }


        private void btn_centrehole_Click(object sender, EventArgs e)
        {
            string m_mtrcensavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "centrehole.mtr";
            string m_shmcensavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "centre.shm";

            string m_mtrboltsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "bolthole.mtr";

            string m_mtrhatsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "hathole.mtr";
            string m_shmhatsavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "hat.shm";

            switch (comboBox_selecttype.Text)
            {

                case "中心孔":
                case "Center hole":
                case "Orificio central":
                case "Trou au centre":
                    double stdcendiametral = double.Parse(censtdradius_textBox.Text);
                    SaveCentre_HoleMtr(m_mtrcensavepath, m_shmcensavepath, stdcendiametral);
                    break;
                case "螺栓孔":
                case "Bolt hole":
                case "Orificios de perno":
                case "Trous pour boulons":
                    double[] stdboltdiametral = new double[8];
                    stdboltdiametral[0] = double.Parse(boltstdradius1_textBox.Text);
                    stdboltdiametral[1] = double.Parse(boltstdradius2_textBox.Text);
                    stdboltdiametral[2] = double.Parse(boltstdradius3_textBox.Text);
                    stdboltdiametral[3] = double.Parse(boltstdradius4_textBox.Text);
                    stdboltdiametral[4] = double.Parse(boltstdradius5_textBox.Text);
                    stdboltdiametral[5] = double.Parse(boltstdradius6_textBox.Text);
                    stdboltdiametral[6] = double.Parse(boltstdradius7_textBox.Text);
                    stdboltdiametral[7] = double.Parse(boltstdradius8_textBox.Text);
                    SaveBolt_HoleMtr(m_mtrboltsavepath, stdboltdiametral);
                    break;
                case "帽止口":
                case "cap stopper":
                case "Tope de tapa":
                case "Bouchon et stop":
                    double stdhatdiametral = double.Parse(hatstdradius_textBox.Text);
                    SaveHat_HoleMtr(m_mtrhatsavepath, m_shmhatsavepath, stdhatdiametral);

                    break;

            }

        }
        //中心孔测量模型保存
        private void SaveCentre_HoleMtr(string mtrsavepath, string shmsavepath, double stddiameter)
        {
            HTuple hv_ModelRow = new HTuple(), hv_ModelColumn = new HTuple();
            HTuple hv_ModelAngle = new HTuple(), hv_ModelScore = new HTuple(), hv_ModelID = new HTuple();
            HTuple hv_HomMat2DIdentity = new HTuple(), hv_HomMat2DRotate = new HTuple(), hv_HomMat2DTranslate = new HTuple();
            HTuple hv_MetrologyHandle = new HTuple();


            HObject ho_ShapeModel;


            HOperatorSet.GenEmptyObj(out ho_ShapeModel);



            hv_ModelRow.Dispose(); hv_ModelColumn.Dispose(); hv_ModelAngle.Dispose(); hv_ModelScore.Dispose();



            if ((ho_Image.CountObj() != 0) && (wheeltype != ""))
            {
                try
                {
                    if (File.Exists(shmsavepath))
                        HOperatorSet.ReadShapeModel(shmsavepath, out hv_ModelID);
                    else
                    {
                        MessageBoxEX.Show(res.GetString("notRecord"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        return;

                    }
                    HOperatorSet.FindShapeModel(ho_Image, hv_ModelID, 0, (new HTuple(360)).TupleRad()
                        , 0.4, 1, 0, "least_squares", 0, 0.7, out hv_ModelRow, out hv_ModelColumn,
                        out hv_ModelAngle, out hv_ModelScore);

                    m_halconwindow.Translate(hv_ModelAngle, hv_ModelRow, hv_ModelColumn,out hv_HomMat2DTranslate);

                    
                    ho_ContoursAffinTrans.Dispose();
                    ho_ShapeModel.Dispose();

                    HOperatorSet.GetShapeModelContours(out ho_ShapeModel, hv_ModelID, 1);
                    HOperatorSet.AffineTransContourXld(ho_ShapeModel, out ho_ContoursAffinTrans,
                        hv_HomMat2DTranslate);
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");

                    HOperatorSet.DispObj(ho_ContoursAffinTrans, hWindowControl1.HalconWindow);

                    int measure_length1 = int.Parse(cenmeasurelength_textBox.Text);
                    HTuple hv_measure_length1 = new HTuple();
                    hv_measure_length1[0] = measure_length1;
                    int measure_length2 = int.Parse(cenmeasurewidth_textBox.Text);
                    HTuple hv_measure_length2 = new HTuple();
                    hv_measure_length2[0] = measure_length2;
                    float sigma = float.Parse(censigmal_textBox.Text);
                    HTuple hv_sigma = new HTuple();
                    hv_sigma[0] = sigma;
                    int measure_threshold = int.Parse(cenmeasurethreshold_textBox.Text);
                    HTuple hv_measure_threshold = new HTuple();
                    hv_measure_threshold[0] = measure_threshold;
                    string measure_transition = cenpolarity_cboBox.Text;
                    HTuple hv_measure_transition = new HTuple();
                    hv_measure_transition[0] = measure_transition;
                    string measure_select = cenlineindex_cboBox.Text;
                    HTuple hv_measure_select = new HTuple();
                    hv_measure_select[0] = measure_select;
                    int num_measures = int.Parse(cennum_numericUpDown.Text);
                    HTuple hv_num_measures = new HTuple();
                    hv_num_measures[0] = num_measures;

                    float min_scores = float.Parse(cenminscores_textBox.Text);
                    HTuple hv_min_scores = new HTuple();
                    hv_min_scores[0] = min_scores;
                    m_caliper.CreateCallipersModel(hWindowControl1, hv_width, hv_height, hv_Row3, hv_Column3, hv_Radius3,
                          hv_ModelRow, hv_ModelColumn, hv_measure_length1, hv_measure_length2, hv_sigma,
                          hv_measure_threshold, hv_measure_transition, hv_measure_select, hv_num_measures, 1, hv_min_scores,
                          "bicubic",
                          out hv_MetrologyHandle);


                   

                    HOperatorSet.WriteMetrologyModel(hv_MetrologyHandle, mtrsavepath);
                    double stdratio, stdpixdiameter, CentreRow, CentreColumn;

                    m_caliper.MakeCentreHoleRatio(mtrsavepath, shmsavepath, ho_Image, hWindowControl1, stddiameter, out CentreRow, out CentreColumn, out stdpixdiameter, out stdratio);

                    //参数写入数据库
                    SQlFun m_sqlfun = new SQlFun();
                    m_sqlfun.connection(connection);
                    m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        //判断是否已经存在
                        bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);

                        if (exist)
                        {
                            DialogResult dr = new DialogResult();
                            dr = MessageBoxEX.Show(res.GetString("paramExit"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                            if (dr == DialogResult.OK)
                            {
                                m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "中心孔几何直径", wheeltype, stddiameter);
                                m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "中心孔像素直径", wheeltype, stdpixdiameter);
                                m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "中心孔比例", wheeltype, stdratio);
                                m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "中心孔Row", wheeltype, CentreRow);
                                m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "中心孔Column", wheeltype, CentreColumn);

                            }
                        }
                        else
                        {
                            int databasemaxindex = m_sqlfun.Sql_indexmax("中心孔位置度帽止口参数");
                            databasemaxindex++;
                            m_sqlfun.Sql_write_cenholeprepare(databasemaxindex, wheeltype, CentreRow, CentreColumn, stdpixdiameter, stddiameter, stdratio);
                        }

                        m_sqlfun.conn.Close();

                    }
                    else
                        MessageBoxEX.Show(res.GetString("dbNotOpen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                    btn_mtrsave.Enabled = false;
                    MessageBoxEX.Show(res.GetString("saveCen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                }
                catch (Exception ex)
                {
                    MessageBoxEX.Show(res.GetString("saveFault"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    return;
                }


            }

            else
                MessageBoxEX.Show(res.GetString("noImgOrType"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


            hv_ModelRow.Dispose();
            hv_ModelColumn.Dispose();
            hv_ModelAngle.Dispose();
            hv_ModelScore.Dispose();
            hv_ModelID.Dispose();
            hv_HomMat2DIdentity.Dispose();
            hv_HomMat2DRotate.Dispose();
            hv_HomMat2DTranslate.Dispose();
            hv_MetrologyHandle.Dispose();
            ho_ShapeModel.Dispose();



        }
        //螺栓孔测量模型保存
       
        private void SaveBolt_HoleMtr(string mtrsavepath, double[] stddiameter)
        {

            HTuple hv_MetrologyHandle = new HTuple();

            HObject ho_boltCircle, ho_SmcirImageReduced, ho_Region;
            HObject ho_ConnectedRegions, ho_SelectedRegions, ho_RegionClosing;
            HObject ho_RegionFillUp, ho_ContCircle3;


            HOperatorSet.GenEmptyObj(out ho_boltCircle);
            HOperatorSet.GenEmptyObj(out ho_SmcirImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Region);
            HOperatorSet.GenEmptyObj(out ho_ConnectedRegions);
            HOperatorSet.GenEmptyObj(out ho_SelectedRegions);
            HOperatorSet.GenEmptyObj(out ho_RegionClosing);
            HOperatorSet.GenEmptyObj(out ho_RegionFillUp);
            HOperatorSet.GenEmptyObj(out ho_ContCircle3);

            double m_stdboltrow, m_stdboltcolumn, m_stdboltradius;
            double[] m_stdratio, m_stdpixdiameter;

            HOperatorSet.SetDraw(hWindowControl1.HalconWindow, "margin");

            if ((ho_Image.CountObj() != 0) && (wheeltype != ""))
            {
                try
                {
                    ho_boltCircle.Dispose();
                    HOperatorSet.GenCircle(out ho_boltCircle, hv_Row3, hv_Column3, hv_Radius3);

                    ho_SmcirImageReduced.Dispose();
                    HOperatorSet.ReduceDomain(ho_Image, ho_boltCircle, out ho_SmcirImageReduced);

                    ho_Region.Dispose();
                    HOperatorSet.Threshold(ho_SmcirImageReduced, out ho_Region, 150, 255);
                    ho_ConnectedRegions.Dispose();
                    HOperatorSet.Connection(ho_Region, out ho_ConnectedRegions);

                    ho_SelectedRegions.Dispose();
                    HOperatorSet.SelectShape(ho_ConnectedRegions, out ho_SelectedRegions, "area",
                        "and", 50000, 1000000);
                    ho_RegionClosing.Dispose();
                    HOperatorSet.ClosingCircle(ho_SelectedRegions, out ho_RegionClosing, 5);
                    ho_RegionFillUp.Dispose();
                    HOperatorSet.FillUp(ho_RegionClosing, out ho_RegionFillUp);
                    //gen_contour_region_xld (SelectedRegions, SmcirEdges, 'border')
                    //edges_sub_pix (SmcirImageReduced, SmcirEdges, 'canny', 0.8, 40, 80)
                    //fit_circle_contour_xld (SmcirEdges, 'algebraic', -1, 0, 0, 3, 2, boltModelRow, boltModelColumn, Radius, StartPhi, EndPhi, PointOrder)
                    hv_boltModelRow.Dispose(); hv_boltModelColumn.Dispose(); hv_boltModelRadius.Dispose();
                    HOperatorSet.SmallestCircle(ho_RegionFillUp, out hv_boltModelRow, out hv_boltModelColumn,
                        out hv_boltModelRadius);
                    //create_shape_model_xld (SmcirEdges, 'auto', rad(0), rad(90), 'auto', 'auto', 'ignore_local_polarity', 3, SmcirModelID)
                    //在搜索图像中搜索模版
                    //find_shape_model (SmcirImageReduced, SmcirModelID, 0, rad(360), 0.4, 1, 0, 'least_squares', 0, 0.7, boltModelRow, boltModelColumn, ModelAngle, ModelScore)

                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");


                    ho_ContCircle3.Dispose();
                    HOperatorSet.GenCircleContourXld(out ho_ContCircle3, hv_boltModelRow, hv_boltModelColumn,
                        hv_boltModelRadius, 0, (new HTuple(360)).TupleRad(), "positive", 1);
                    HOperatorSet.DispObj(ho_ContCircle3, hWindowControl1.HalconWindow);


                    //*****************************************************螺栓孔测量模型创建*************************************************
                    int measure_length1 = int.Parse(boltmeasurelength_textBox.Text);
                    HTuple hv_measure_length1 = new HTuple();
                    hv_measure_length1[0] = measure_length1;
                    int measure_length2 = int.Parse(boltmeasurewidth_textBox.Text);
                    HTuple hv_measure_length2 = new HTuple();
                    hv_measure_length2[0] = measure_length2;
                    float sigma = float.Parse(boltsigmal_textBox.Text);
                    HTuple hv_sigma = new HTuple();
                    hv_sigma[0] = sigma;
                    int measure_threshold = int.Parse(boltthreshold_textBox.Text);
                    HTuple hv_measure_threshold = new HTuple();
                    hv_measure_threshold[0] = measure_threshold;
                    string measure_transition = boltpolarity_cboBox.Text;
                    HTuple hv_measure_transition = new HTuple();
                    hv_measure_transition[0] = measure_transition;
                    string measure_select = boltlineindex_cboBox.Text;
                    HTuple hv_measure_select = new HTuple();
                    hv_measure_select[0] = measure_select;
                    int num_measures = int.Parse(boltnum_numericUpDown.Text);
                    HTuple hv_num_measures = new HTuple();
                    hv_num_measures[0] = num_measures;

                    float min_scores = float.Parse(boltminscores_textBox.Text);
                    HTuple hv_min_scores = new HTuple();
                    hv_min_scores[0] = min_scores;

                    hv_MetrologyHandle.Dispose();
                    m_caliper.CreateCallipersModel(hWindowControl1, hv_width, hv_height, hv_boltModelRow, hv_boltModelColumn, hv_boltModelRadius,
                          hv_boltModelRow, hv_boltModelColumn, hv_measure_length1, hv_measure_length2, hv_sigma,
                          hv_measure_threshold, hv_measure_transition, hv_measure_select, hv_num_measures, 1, hv_min_scores,
                          "bicubic",
                          out hv_MetrologyHandle);

                    HOperatorSet.WriteMetrologyModel(hv_MetrologyHandle, mtrsavepath);

                    //从数据库读取中心孔坐标数据
                    m_sqlfun.connection(connection);
                    m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);
                        bool exist1 = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);
                        if (exist && exist1)
                        {
                            double m_cenrow, m_cencolumn, m_holenum;
                            m_sqlfun.Sql_Find("中心孔Row", "中心孔位置度帽止口参数", wheeltype, out m_cenrow);
                            m_sqlfun.Sql_Find("中心孔Column", "中心孔位置度帽止口参数", wheeltype, out m_cencolumn);
                            //制作螺栓孔测量用参数
                            m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out m_holenum);
                            //int m_holenum = int.Parse(holenum_numericUpDown.Value.ToString());
                            m_caliper.MakeBoltHoleRatio(hWindowControl1, mtrsavepath, ho_Image, hv_Row3, hv_Column3, hv_Radius3, m_cenrow, m_cencolumn, (int)m_holenum, stddiameter,
                            out m_stdpixdiameter, out m_stdratio, out m_stdboltrow, out m_stdboltcolumn, out m_stdboltradius);



                            DialogResult dr = new DialogResult();
                            dr = MessageBoxEX.Show(res.GetString("paramExit"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                            if (dr == DialogResult.OK)
                            {

                                m_sqlfun.Sql_write_boltholeprepare(wheeltype, (int)m_holenum, m_stdpixdiameter, stddiameter, m_stdratio, m_stdboltrow, m_stdboltcolumn, m_stdboltradius);


                            }
                        }
                        else
                        {
                            MessageBoxEX.Show(wheeltype + res.GetString("noParamOrHub"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                        }
                        m_sqlfun.conn.Close();

                    }
                    else
                        MessageBoxEX.Show(wheeltype + res.GetString("dbNotOpen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


                    btn_mtrsave.Enabled = false;
                    MessageBoxEX.Show(wheeltype + res.GetString("boltHoleSave"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                    return;
                }

            }
            else
                MessageBoxEX.Show(wheeltype + res.GetString("noImgOrType"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

        }

        //帽止口测量模型保存
        private void SaveHat_HoleMtr(string mtrsavepath, string shmsavepath, double stddiameter)
        {
            HTuple hv_ModelRow = new HTuple(), hv_ModelColumn = new HTuple();
            HTuple hv_ModelAngle = new HTuple(), hv_ModelScore = new HTuple(), hv_ModelID = new HTuple();
            HTuple hv_HomMat2DIdentity = new HTuple(), hv_HomMat2DRotate = new HTuple(), hv_HomMat2DTranslate = new HTuple();
            HTuple hv_MetrologyHandle = new HTuple();


            HObject ho_ShapeModel;


            HOperatorSet.GenEmptyObj(out ho_ShapeModel);



            hv_ModelRow.Dispose(); hv_ModelColumn.Dispose(); hv_ModelAngle.Dispose(); hv_ModelScore.Dispose();


            try
            {

                if ((ho_Image.CountObj() != 0) && (wheeltype != ""))
                {
                    try
                    {
                        if (File.Exists(shmsavepath))
                            HOperatorSet.ReadShapeModel(shmsavepath, out hv_ModelID);
                        else
                        {
                            MessageBoxEX.Show(wheeltype + res.GetString("notRecord"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            return;
                        }
                        HOperatorSet.FindShapeModel(ho_Image, hv_ModelID, 0, (new HTuple(360)).TupleRad()
                            , 0.4, 1, 0, "least_squares", 0, 0.7, out hv_ModelRow, out hv_ModelColumn,
                            out hv_ModelAngle, out hv_ModelScore);

                        m_halconwindow.Translate(hv_ModelAngle, hv_ModelRow, hv_ModelColumn, out hv_HomMat2DTranslate);

                        ho_ContoursAffinTrans.Dispose();
                        ho_ShapeModel.Dispose();

                        HOperatorSet.GetShapeModelContours(out ho_ShapeModel, hv_ModelID, 1);
                        HOperatorSet.AffineTransContourXld(ho_ShapeModel, out ho_ContoursAffinTrans,
                            hv_HomMat2DTranslate);
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");

                        HOperatorSet.DispObj(ho_ContoursAffinTrans, hWindowControl1.HalconWindow);

                        int measure_length1 = int.Parse(hatmeasurelength_textBox.Text);
                        HTuple hv_measure_length1 = new HTuple();
                        hv_measure_length1[0] = measure_length1;
                        int measure_length2 = int.Parse(hatmeasurewidth_textBox.Text);
                        HTuple hv_measure_length2 = new HTuple();
                        hv_measure_length2[0] = measure_length2;
                        float sigma = float.Parse(hatsigmal_textBox.Text);
                        HTuple hv_sigma = new HTuple();
                        hv_sigma[0] = sigma;
                        int measure_threshold = int.Parse(hatmeasurethreshold_textBox.Text);
                        HTuple hv_measure_threshold = new HTuple();
                        hv_measure_threshold[0] = measure_threshold;
                        string measure_transition = hatpolarity_cboBox.Text;
                        HTuple hv_measure_transition = new HTuple();
                        hv_measure_transition[0] = measure_transition;
                        string measure_select = hatlineindex_cboBox.Text;
                        HTuple hv_measure_select = new HTuple();
                        hv_measure_select[0] = measure_select;
                        int num_measures = int.Parse(hatnum_numericUpDown.Text);
                        HTuple hv_num_measures = new HTuple();
                        hv_num_measures[0] = num_measures;

                        float min_scores = float.Parse(hatminscores_textBox.Text);
                        HTuple hv_min_scores = new HTuple();
                        hv_min_scores[0] = min_scores;
                        m_caliper.CreateCallipersModel(hWindowControl1, hv_width, hv_height, hv_Row3, hv_Column3, hv_Radius3,
                              hv_ModelRow, hv_ModelColumn, hv_measure_length1, hv_measure_length2, hv_sigma,
                              hv_measure_threshold, hv_measure_transition, hv_measure_select, hv_num_measures, 1, hv_min_scores,
                              "bicubic",
                              out hv_MetrologyHandle);



                        HOperatorSet.WriteMetrologyModel(hv_MetrologyHandle, mtrsavepath);
                        double stdratio, stdpixdiameter, stdhatRow, stdhatColumn;

                        m_caliper.MakeCentreHoleRatio(mtrsavepath, shmsavepath, ho_Image, hWindowControl1, stddiameter, out stdhatRow, out stdhatColumn, out stdpixdiameter, out stdratio);

                        //参数写入数据库
                        SQlFun m_sqlfun = new SQlFun();
                        m_sqlfun.connection(connection);
                        m_sqlfun.Sql_open();
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {
                            //判断是否已经存在
                            bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);

                            if (exist)
                            {
                                DialogResult dr = new DialogResult();
                                dr = MessageBoxEX.Show(res.GetString("paramExit"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                                if (dr == DialogResult.OK)
                                {
                                    m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "帽止口几何直径", wheeltype, stddiameter);
                                    m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "帽止口像素直径", wheeltype, stdpixdiameter);
                                    m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "帽止口比例", wheeltype, stdratio);
                                    m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "帽止口Row", wheeltype, stdhatRow);
                                    m_sqlfun.Sql_modify("中心孔位置度帽止口参数", "帽止口Column", wheeltype, stdhatColumn);
                                }
                            }
                            else
                            {
                                int databasemaxindex = m_sqlfun.Sql_indexmax("中心孔位置度帽止口参数");
                                databasemaxindex++;
                                m_sqlfun.Sql_write_hatholeprepare(databasemaxindex, wheeltype, stdhatRow, stdhatColumn, stdpixdiameter, stddiameter, stdratio);
                            }

                            m_sqlfun.conn.Close();

                        }
                        else
                        {
                            MessageBoxEX.Show(res.GetString("dbNotOpen"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        }
                        btn_mtrsave.Enabled = false;
                        MessageBoxEX.Show(res.GetString("saveCap"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    catch (Exception ex)
                    {
                        MessageBoxEX.Show(res.GetString("saveFault") + "\n" + ex.Message, "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        return;
                    }


                }
                else
                {
                    MessageBoxEX.Show(res.GetString("noImgOrType"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
            }
            catch
            {

            }
            finally
            {
                hv_ModelRow.Dispose();
                hv_ModelColumn.Dispose();
                hv_ModelAngle.Dispose();
                hv_ModelScore.Dispose();
                hv_ModelID.Dispose();
                hv_HomMat2DIdentity.Dispose();
                hv_HomMat2DRotate.Dispose();
                hv_HomMat2DTranslate.Dispose();
                hv_MetrologyHandle.Dispose();
                ho_ShapeModel.Dispose();
            }



        }
        private void btn_test_Click(object sender, EventArgs e)
        {

            string m_mtrcensavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "centrehole.mtr";
            string m_shmcensavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "centre.shm";

            string m_mtrboltsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "bolthole.mtr";

            string m_mtrhatsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "hathole.mtr";
            string m_shmhatsavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "hat.shm";

            m_readimage = true;
            //string m_mtrboltsavepath = "D:\\技术\\设计\\2021\\设计\\中心孔位置度测量\\5.23\\23\\boltmtr.mtr";

            switch (comboBox_selecttype.Text)
            {

                case "中心孔":
                case "Center hole":
                case "Orificio central":
                case "Trou au centre":
                    try
                    {
                        m_sqlfun.connection(connection);
                        m_sqlfun.Sql_open();
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {
                            bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);

                            if (exist)
                            {
                                double m_stdcenratio, m_diameter;
                                HTuple m_cenrow = new HTuple(), m_cencolumn = new HTuple(), m_pixdiameter = new HTuple();
                                m_sqlfun.Sql_Find("中心孔比例", "中心孔位置度帽止口参数", wheeltype, out m_stdcenratio);

                                m_caliper.MeasureCentreHole(m_mtrcensavepath, m_shmcensavepath, ho_Image, hWindowControl1, m_stdcenratio, m_reduceRow, m_reduceCol, m_reduceRadius, int.Parse(numberofdecimal), out m_cenrow, out m_cencolumn, out m_pixdiameter, out m_diameter);

                                double resultval; string result;
                                m_caliper.CenHathole_CompareDesignVal(connection, wheeltype, "中心孔", m_diameter, false, out resultval, out result);
                                string dispstr = "中心孔尺寸:" + m_diameter.ToString() + "   " + result + "\r\n";



                                if (result == "OK")
                                {
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "green", "box_color", "white");
                                }
                                else
                                {
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "red", "box_color", "white");
                                }

                                m_sqlfun.conn.Close();

                                m_cenrow.Dispose();
                                m_cencolumn.Dispose();
                                m_pixdiameter.Dispose();
                            }
                            else
                            {
                                MessageBoxEX.Show(res.GetString("noParam"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                        return;
                    }



                    break;
                case "螺栓孔":
                case "Bolt hole":
                case "Orificios de perno":
                case "Trous pour boulons":
                    try
                    {
                        //int holenum = int.Parse(holenum_numericUpDown.Value.ToString());
                        double holenum, stdpcd, mmc_posdeg, chordlength;
                        m_sqlfun.connection(connection);
                        m_sqlfun.Sql_open();
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {

                            bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);
                            bool exist1 = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);



                            if (exist && exist1 && (File.Exists(m_mtrboltsavepath)))
                            {
                                m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out holenum);
                                m_sqlfun.Sql_Find("PCD节圆直径", "轮毂参数", wheeltype, out stdpcd);

                                m_sqlfun.Sql_Find("最大实体原则开关", "轮毂参数", wheeltype, out mmc_posdeg);
                                m_sqlfun.Sql_Find("弦长测量开关", "轮毂参数", wheeltype, out chordlength);

                                double m_stdcenrow, m_stdcencolumn, m_stdboltrow, m_stdboltcolumn, m_stdboltradius, tem_boltratio;
                                double[] stdboltratio = new double[(int)holenum];
                                m_sqlfun.Sql_Find("中心孔Row", "中心孔位置度帽止口参数", wheeltype, out m_stdcenrow);
                                m_sqlfun.Sql_Find("中心孔Column", "中心孔位置度帽止口参数", wheeltype, out m_stdcencolumn);
                                m_sqlfun.Sql_Find("螺栓孔1Row", "中心孔位置度帽止口参数", wheeltype, out m_stdboltrow);
                                m_sqlfun.Sql_Find("螺栓孔1Column", "中心孔位置度帽止口参数", wheeltype, out m_stdboltcolumn);
                                m_sqlfun.Sql_Find("螺栓孔1Radius", "中心孔位置度帽止口参数", wheeltype, out m_stdboltradius);

                                for (int i = 0; i < holenum; i++)
                                {
                                    m_sqlfun.Sql_Find("螺栓孔" + (i + 1).ToString() + "比例", "中心孔位置度帽止口参数", wheeltype, out tem_boltratio);
                                    stdboltratio[i] = tem_boltratio;
                                }

                                double m_stdcenratio;

                                HTuple m_cenrow = new HTuple(), m_cencolumn = new HTuple(), m_pixdiameter = new HTuple();
                                m_sqlfun.Sql_Find("中心孔比例", "中心孔位置度帽止口参数", wheeltype, out m_stdcenratio);

                                //stdboltratio[0] = 0.0231538;
                                m_caliper.MeasureCentreHole(m_mtrcensavepath, m_shmcensavepath, ho_Image, hWindowControl1, m_stdcenratio, m_reduceRow, m_reduceCol, m_reduceRadius, int.Parse(numberofdecimal), out m_cenrow, out m_cencolumn, out m_pixdiameter, out m_diameter);
                                double resultval; string result;
                                m_caliper.CenHathole_CompareDesignVal(connection, wheeltype, "中心孔", m_diameter, false, out resultval, out result);

                                m_caliper.MeasureBoltHole(hWindowControl1, m_mtrboltsavepath, ho_Image, m_stdcenrow, m_stdcencolumn, stdpcd,
                                m_stdboltrow, m_stdboltcolumn, m_stdboltradius, (int)holenum, stdboltratio, m_cenrow, m_cencolumn, int.Parse(numberofdecimal), m_holeScale, out m_boltholeresult);

                                m_cenrow.Dispose();
                                m_cencolumn.Dispose();
                                m_pixdiameter.Dispose();



                                var posDegReverse = ConfigIni.GetIniKeyValue("参数配置", "位置度顺序翻转", "True", Application.StartupPath + "\\config.ini").Equals("True");
                                if (posDegReverse)
                                {
                                    int hnum = (int)holenum;
                                    for (int i = 1; i <= (hnum - 1) / 2; ++i)
                                    {
                                        double t = m_boltholeresult.positondeg[i];
                                        m_boltholeresult.positondeg[i] = m_boltholeresult.positondeg[hnum - i];
                                        m_boltholeresult.positondeg[hnum - i] = t;
                                    }
                                }



                                //计算弦长
                                double[] resultval1 = new double[8]; string[] result1 = new string[8];
                                double[] chordlengthrel = new double[8];
                                if (chordlength == 2)
                                {

                                    MeasureChordLength(m_boltholeresult, holenum, out chordlengthrel);
                                    ChordLength_CompareDesignVal(connection, wheeltype, chordlengthrel, out resultval1, out result1);
                                }
                                else if (mmc_posdeg == 2)
                                {
                                    BoltholePosdeg_CompareDesignValMCC(connection, wheeltype, m_boltholeresult, out resultval1, out result1);

                                }
                                else
                                {
                                    //位置度跟理论值比较，得出结果

                                    m_caliper.BoltholePosdeg_CompareDesignVal(connection, wheeltype, m_boltholeresult.positondeg, out resultval1, out result1);
                                }



                                //螺栓孔直径跟理论值比较，得出结果
                                double[] resultval2 = new double[8]; string[] result2 = new string[8];
                                m_caliper.Boltholediameter_CompareDesignVal(connection, wheeltype, m_boltholeresult.boltdiameter, out resultval2, out result2);



                                //屏幕窗口1显示结果
                                string dispstr = "中心孔尺寸:" + m_diameter.ToString() + "  " + result + "\r\n";
                                if (result == "OK")
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "green", "box_color", "white");
                                else
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "red", "box_color", "white");


                                for (int i = 0; i < holenum; i++)
                                {
                                    if (chordlength == 2)
                                        dispstr = "螺栓孔" + i.ToString() + "弦长:" + chordlengthrel[i].ToString() + " " + result1[i] +
                             " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];
                                    else if (mmc_posdeg == 2)
                                        dispstr = "螺栓孔" + i.ToString() + "MCC位置度:" + m_boltholeresult.positondeg[i].ToString() + " " + result1[i] +
                              " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];
                                    else
                                        dispstr = "螺栓孔" + i.ToString() + "位置度:" + m_boltholeresult.positondeg[i].ToString() + " " + result1[i] +
                                  " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];


                                    if ((result1[i] == "OK") && (result2[i] == "OK"))
                                    {
                                        HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10 + 16 * (i + 2),
                                          10, "green", "box_color", "white");

                                    }

                                    else
                                        HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10 + 16 * (i + 2),
                                          10, "red", "box_color", "white");

                                }



                                m_sqlfun.conn.Close();

                            }
                            else
                            {
                                MessageBoxEX.Show(res.GetString("boltHoleLack"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                        MessageBoxEX.Show(res.GetString("boltHoleDecFail"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        return;
                    }

                    break;
                case "帽止口":
                case "cap stopper":
                case "Tope de tapa":
                case "Bouchon et stop":
                    try
                    {
                        m_sqlfun.connection(connection);
                        m_sqlfun.Sql_open();
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {
                            bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);

                            if (exist)
                            {
                                double m_stdhatratio, m_diameter;
                                HTuple m_hatrow = new HTuple(), m_hatcolumn = new HTuple(), m_pixdiameter = new HTuple();
                                m_sqlfun.Sql_Find("帽止口比例", "中心孔位置度帽止口参数", wheeltype, out m_stdhatratio);
                                m_caliper.MeasureCentreHole(m_mtrhatsavepath, m_shmhatsavepath, ho_Image, hWindowControl1, m_stdhatratio, m_reduceRow_C3, m_reduceCol_C3, m_reduceRadius_C3, int.Parse(numberofdecimal), out m_hatrow, out m_hatcolumn, out m_pixdiameter, out m_diameter);

                                double resultval; string result;
                                m_caliper.CenHathole_CompareDesignVal(connection, wheeltype, "帽止口", m_diameter, false, out resultval, out result);
                                string dispstr = "帽止口尺寸:" + m_diameter.ToString() + "   " + result + "\r\n";
                                if (result == "OK")
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "green", "box_color", "white");
                                else
                                    HOperatorSet.DispText(hWindowControl1.HalconWindow, dispstr, "window", 10,
                                      10, "red", "box_color", "white");



                                m_sqlfun.conn.Close();

                                m_hatrow.Dispose();
                                m_hatcolumn.Dispose();
                                m_pixdiameter.Dispose();

                            }
                            else
                            {
                                MessageBoxEX.Show(res.GetString("noParam"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            }
                        }

                        break;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                        return;
                    }
                default:
                    int int_attackers = Environment.ProcessorCount;
                    var semaphore = new SemaphoreSlim(int_attackers - 1);
                    ConcurrentQueue<SearchPat.ResultData> keyQueue = new ConcurrentQueue<SearchPat.ResultData>();

                    string typename;
                    string score;
                    string usetime;

                    SearchPat searchPat = new SearchPat();
                    searchPat.SearchPattem(ho_Image, hWindowControl1, semaphore, keyQueue, out typename, out score, out usetime);
                    
                    if (double.Parse(score) >= 0.6)
                    {
                        HOperatorSet.DispText(hWindowControl1.HalconWindow, typename, "window", 10, 10, "green", "box_color", "white");
                    }
                    else
                    {
                        HOperatorSet.DispText(hWindowControl1.HalconWindow, "NG", "window", 10, 10, "red", "box_color", "white");
                    }

                    HTuple Angle = null;
                    double[] angle = new double[5];
                    HTuple deg_angle = null;
                    double regionsize;
                    m_sqlfun.connection(connection);
                    m_sqlfun.Sql_open();

                    if (m_sqlfun.Sql_ExistColumn("气门孔范围直径", "轮毂型号", typename))
                    {
                        m_sqlfun.Sql_Find("半径", "气门孔范围直径", typename, out regionsize);
                        Findsmallcircle(ho_Image, typename, regionsize, hWindowControl1, out Angle);
                        HOperatorSet.TupleDeg(Angle, out deg_angle);
                        angle = deg_angle.ToDArr();
                        HOperatorSet.DispText(hWindowControl1.HalconWindow, angle, "window", 50, 50, "green", "box", "false");
                    }

                    Angle.Dispose();
                    deg_angle.Dispose();

                    break;
            }


        }


        private int Findsmallcircle(HObject ho_Image1, string typename, double regionradius, HWindowControl hWindowControl, out HTuple hv_Angle)
        {
            HObject ho_Circle, ho_Circle1, ho_RegionDifference;
            HObject ho_Contour, ho_ImageReduced;

            HOperatorSet.GenEmptyObj(out ho_Circle);
            HOperatorSet.GenEmptyObj(out ho_Circle1);
            HOperatorSet.GenEmptyObj(out ho_RegionDifference);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Contour);
            //此处为气门孔与中心孔连线的程序

            HTuple hv_ValveholeModelID = new HTuple();
            HTuple hv_ValveholeModelRow = new HTuple();
            HTuple hv_ValveholeModelColumn = new HTuple();
            HTuple hv_ValveholeModelAngle = new HTuple();
            HTuple hv_ValveholeModelScore = new HTuple();
            HTuple hv_CenterModelID = new HTuple();
            HTuple hv_CenterRow = new HTuple();
            HTuple hv_CenterColumn = new HTuple();
            HTuple hv_CenterAngle = new HTuple();
            HTuple hv_CenterScore = new HTuple();
            HTuple hubsize = new HTuple();
            try
            {
                //找中心孔
                if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "circenter.shm"))
                {

                    HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "circenter.shm",
                                                                out hv_CenterModelID);
                    HOperatorSet.FindShapeModel(ho_Image1, hv_CenterModelID, 0, 6.28, 0.5, 1,
                     0.5, "least_squares", 5, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
                     out hv_CenterScore);

                    //找气门孔
                    hubsize[0] = regionradius;
                    ho_Circle.Dispose();
                    HOperatorSet.GenCircle(out ho_Circle, hv_CenterRow, hv_CenterColumn, hubsize);
                    ho_Circle1.Dispose();
                    HOperatorSet.GenCircle(out ho_Circle1, hv_CenterRow, hv_CenterColumn, hubsize - 200);
                    ho_RegionDifference.Dispose();
                    HOperatorSet.Difference(ho_Circle, ho_Circle1, out ho_RegionDifference);
                    ho_ImageReduced.Dispose();
                    HOperatorSet.ReduceDomain(ho_Image1, ho_RegionDifference, out ho_ImageReduced);

                    if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "valvehole .shm"))
                    {
                        HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "valvehole .shm",
                                                out hv_ValveholeModelID);
                        HOperatorSet.FindShapeModel(ho_ImageReduced, hv_ValveholeModelID, 0, 4.14, 0.55, 1,
                         0.5, "least_squares", 8, 0.9, out hv_ValveholeModelRow, out hv_ValveholeModelColumn, out hv_ValveholeModelAngle,
                         out hv_ValveholeModelScore);

                        HOperatorSet.GenContourPolygonXld(out ho_Contour, (hv_CenterRow).TupleConcat(
                        hv_ValveholeModelRow), (hv_CenterColumn).TupleConcat(hv_ValveholeModelColumn));
                        HOperatorSet.SetColor(hWindowControl.HalconWindow, "green");
                        HOperatorSet.DispObj(ho_Contour, hWindowControl.HalconWindow);
                        HOperatorSet.AngleLx(hv_CenterRow, hv_CenterColumn, hv_ValveholeModelRow, hv_ValveholeModelColumn, out hv_Angle);
                        

                        return 0;
                    }
                    else
                    {
                        MessageBoxEX.Show(typename + "valvehole .shm 缺失", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        hv_Angle = 999.0;
                        return -1;
                    }


                }
                else
                {
                    MessageBoxEX.Show(typename + "center.shm 缺失", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    hv_Angle = 999.0;
                    return -1;
                }
                


            }
            catch (Exception ex)
            {
                hv_Angle = 999.0;
                return -1;
                throw (ex);

            }
            finally
            {
                hv_ValveholeModelID.Dispose();
                hv_ValveholeModelRow.Dispose();
                hv_ValveholeModelColumn.Dispose();
                hv_ValveholeModelAngle.Dispose();
                hv_ValveholeModelScore.Dispose();
                hv_CenterModelID.Dispose();
                hv_CenterRow.Dispose();
                hv_CenterColumn.Dispose();
                hv_CenterAngle.Dispose();
                hv_CenterScore.Dispose();
                hubsize.Dispose();

                ho_Circle.Dispose();
                ho_Circle1.Dispose();
                ho_RegionDifference.Dispose();
                ho_ImageReduced.Dispose();
                ho_Contour.Dispose();
            }


        }


        //增添的最大实体原则与弦长测试函数
        void MeasureChordLength(boltholeresult boltholres,double holenum, out double[] resultval)

        {
            resultval = new double[8];
            for (int i = 0; i < holenum; i++)
            {
                double dis_x, dis_y;
                if (i + 1 < holenum)
                {
                    dis_x = boltholres.boltholecen[i].bolt_x - boltholres.boltholecen[i + 1].bolt_x;
                    dis_y = boltholres.boltholecen[i].bolt_y - boltholres.boltholecen[i + 1].bolt_y;
                }
                else
                {
                    dis_x = boltholres.boltholecen[i].bolt_x - boltholres.boltholecen[0].bolt_x;
                    dis_y = boltholres.boltholecen[i].bolt_y - boltholres.boltholecen[0].bolt_y;
                }
                resultval[i] = Math.Sqrt(dis_x * dis_x + dis_y * dis_y);
                string str = resultval[i].ToString("f2");
                resultval[i] = double.Parse(str);


            }


        }

        void ChordLength_CompareDesignVal(string databasepath, string wheeltype, double[] measureval, out double[] resultval, out string[] result)
        {
            SQlFun m_sqlfun = new SQlFun();
            m_sqlfun.connection(databasepath);
            m_sqlfun.Sql_open();
            string[] m_resule = new string[8];
            double[] m_resultval = new double[8];
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);

                if (exist)
                {
                    double m_designval, m_tolp, m_toln, top, bottom, m_holenum;


                    m_sqlfun.Sql_Find("弦长", "轮毂参数", wheeltype, out m_designval);
                    m_sqlfun.Sql_Find("弦长上公差", "轮毂参数", wheeltype, out m_tolp);
                    m_sqlfun.Sql_Find("弦长下公差", "轮毂参数", wheeltype, out m_toln);
                    m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out m_holenum);
                    top = m_designval + m_tolp;
                    bottom = m_designval + m_toln;
                    for (int i = 0; i < m_holenum; i++)
                    {

                        if ((measureval[i] >= bottom) && (measureval[i] <= top))
                        {
                            m_resultval[i] = 0.0;
                            m_resule[i] = "OK";
                        }
                        else if (measureval[i] > top)
                        {
                            m_resule[i] = "NG";
                            m_resultval[i] = measureval[i] - top;


                        }
                        else if (measureval[i] < bottom)
                        {
                            m_resule[i] = "NG";
                            m_resultval[i] = measureval[i] - bottom;

                        }


                    }
                    resultval = m_resultval;
                    result = m_resule;

                }
                else
                {
                    for (int i = 0; i < 8; i++)
                    {
                        m_resultval[i] = 0.0;
                        m_resule[i] = "轮毂型号不存在";

                    }
                    resultval = m_resultval;
                    result = m_resule;
                }




            }
            else
            {
                for (int i = 0; i < 8; i++)
                {
                    m_resultval[i] = 0.0;
                    m_resule[i] = "数据库连接失败";

                }
                resultval = m_resultval;
                result = m_resule;

            }

            m_sqlfun.conn.Close();

        }

        void BoltholePosdeg_CompareDesignValMCC(string databasepath, string wheeltype, boltholeresult boltholres, out double[] resultval, out string[] result)
        {
            SQlFun m_sqlfun = new SQlFun();
            m_sqlfun.connection(databasepath);
            m_sqlfun.Sql_open();
            string[] m_resule = new string[8];
            double[] m_resultval = new double[8];
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);

                if (exist)
                {

                    double m_designval, m_boltdiadesignval, m_holenum;

                    m_sqlfun.Sql_Find("PCD位置度", "轮毂参数", wheeltype, out m_designval);
                    m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out m_holenum);
                    m_sqlfun.Sql_Find("螺栓孔直径", "轮毂参数", wheeltype, out m_boltdiadesignval);

                    for (int i = 0; i < m_holenum; i++)
                    {
                        double compensation = boltholres.boltdiameter[i] - m_boltdiadesignval;
                        if (compensation <= 0)
                            compensation = 0;


                        if (boltholres.positondeg[i] <= m_designval + compensation)
                        {
                            m_resultval[i] = 0.0;
                            m_resule[i] = "OK";
                        }

                        else
                        {
                            m_resultval[i] = boltholres.positondeg[i] - m_designval - compensation;
                            m_resule[i] = "NG";
                        }


                    }
                    resultval = m_resultval;
                    result = m_resule;


                }
                else
                {
                    for (int i = 0; i < 8; i++)
                    {
                        m_resultval[i] = 0.0;
                        m_resule[i] = "轮毂型号不存在";

                    }
                    resultval = m_resultval;
                    result = m_resule;
                }
            }
            else
            {
                for (int i = 0; i < 8; i++)
                {
                    m_resultval[i] = 0.0;
                    m_resule[i] = "数据库连接失败";

                }
                resultval = m_resultval;
                result = m_resule;

            }
            m_sqlfun.conn.Close();

        }


        private void comboBox_selecttype_SelectedIndexChanged(object sender, EventArgs e)
        {


            //导入存在的注册图片
            string str = Directory.GetCurrentDirectory() + "\\model\\registerpicture\\";
            DirectoryInfo theFolder = new DirectoryInfo(str);

            FileInfo[] dirInfo = theFolder.GetFiles();


            HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
            HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);

            HOperatorSet.GenEmptyObj(out m_caliper.ho_ContCircle1);
            HOperatorSet.GenEmptyObj(out m_caliper.ho_Cross);
            HOperatorSet.GenEmptyObj(out ho_ContCircle);


            switch (comboBox_selecttype.Text)
            {
                case "中心孔":
                case "Center hole":
                case "Orificio central":
                case "Trou au centre":
                    comboBox_wheellist.Items.Clear();
                    listOnit.Clear();
                    foreach (FileInfo file in dirInfo)
                    {
                        if (file.Extension.Equals(".bmp"))
                        {
                            string str2, str1;
                            str1 = file.Name.Substring(file.Name.LastIndexOf('.') - 1, 1);
                            if (str1 == "C")
                            {
                                str2 = file.Name.Substring(0, file.Name.LastIndexOf('.'));
                                comboBox_wheellist.Items.Add(str2);
                                listOnit.Add(str2);

                            }

                        }
                    }
                    groupBox2.Enabled = true;
                    groupBox3.Enabled = false;
                    groupBox5.Enabled = false;


                    break;
                case "螺栓孔":
                case "Bolt hole":
                case "Orificios de perno":
                case "Trous pour boulons":
                    groupBox2.Enabled = false;
                    groupBox3.Enabled = true;
                    groupBox5.Enabled = false;


                    break;
                case "帽止口":
                case "cap stopper":
                case "Tope de tapa":
                case "Bouchon et stop":
                    comboBox_wheellist.Items.Clear();
                    listOnit.Clear();
                    foreach (FileInfo file in dirInfo)
                    {
                        if (file.Extension.Equals(".bmp"))
                        {
                            string str2, str1;
                            str1 = file.Name.Substring(file.Name.LastIndexOf('.') - 1, 1);
                            if (str1 == "H")
                            {
                                str2 = file.Name.Substring(0, file.Name.LastIndexOf('.'));
                                comboBox_wheellist.Items.Add(str2);
                                listOnit.Add(str2);
                            
                            }

                        }
                    }

                    groupBox2.Enabled = false;
                    groupBox3.Enabled = false;
                    groupBox5.Enabled = true;
                    break;
                default:
                    comboBox_wheellist.Items.Clear();
                    listOnit.Clear();
                    groupBox2.Enabled = false;
                    groupBox3.Enabled = false;
                    groupBox5.Enabled = false;
                    break;
            }
        }
        //测试用读入图片
        private void btn_readimage_Click(object sender, EventArgs e)
        {
            m_readimage = false;

            //导入图片前清空前面痕迹

            HOperatorSet.GenEmptyObj(out m_caliper.ho_ContCircle1);
            HOperatorSet.GenEmptyObj(out m_caliper.ho_Cross);
            HOperatorSet.GenEmptyObj(out m_caliper.ho_Bolthole);

            HOperatorSet.GenEmptyObj(out m_caliper.ho_ContoursAffinTrans);
            HOperatorSet.GenEmptyObj(out m_caliper.ho_ContoursAffinTrans1);

            HOperatorSet.GenEmptyObj(out ho_ContCircle);
            HOperatorSet.GenEmptyObj(out ho_ContoursAffinTrans);

            string file = null;

            dialog.Multiselect = true;//该值确定是否可以选择多个文件
            dialog.Title = res.GetString("selectFile");
            dialog.Filter = res.GetString("allFile");

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                file = dialog.FileName;
            }
            if (!(file == null))
            {
                try
                {

                    ho_Image.Dispose();
                    HOperatorSet.ReadImage(out ho_Image, file);

                    HOperatorSet.GetImageSize(ho_Image, out hv_width, out hv_height);

                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hv_height, hv_width);
                    HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);




                }
                catch
                {
                    return;
                }
            }
        }

        private void comboBox_wheellistUpdate(object sender, EventArgs e)
        {
            //清空combobox

            this.comboBox_wheellist.Items.Clear();

            //清空listNew

            listNew.Clear();
            


            //遍历全部备查数据

            foreach (var item in listOnit)

            {

                if (item.Contains(this.comboBox_wheellist.Text))

                {

                    //符合，插入ListNew

                    listNew.Add(item);

                }

            }

            //combobox添加已经查到的关键词

            this.comboBox_wheellist.Items.AddRange(listNew.ToArray());

            //设置光标位置，否则光标位置始终保持在第一列，造成输入关键词的倒序排列

            this.comboBox_wheellist.SelectionStart = this.comboBox_wheellist.Text.Length;

            //保持鼠标指针原来状态，有时候鼠标指针会被下拉框覆盖，所以要进行一次设置。

            Cursor = Cursors.Default;

            //自动弹出下拉框

            this.comboBox_wheellist.DroppedDown = true;
        }

        private void hWindowControl1_HMouseMove(object sender, HMouseEventArgs e)
        {

        }

        private void groupBox4_Enter(object sender, EventArgs e)
        {

        }


        private void censtdradius_textBox_Validated(object sender, EventArgs e)
        {
            try
            { 
            double stdcendiametral = double.Parse(censtdradius_textBox.Text);
            wheeltype = comboBox_wheellist.Text;
            wheeltype = wheeltype.Substring(0, wheeltype.LastIndexOf('C'));
            //从数据库读取中心孔坐标数据
            m_sqlfun.connection(connection);
            m_sqlfun.Sql_open();
            double m_cendia = 0;
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);
                if (exist)
                {

                    m_sqlfun.Sql_Find("中心孔直径", "轮毂参数", wheeltype, out m_cendia);
                }
            }
            if (stdcendiametral - m_cendia > 0.3 || stdcendiametral - m_cendia < -0.3)
            {
                MessageBox.Show("中心孔直径输入异常：超出参数0.3以上");
            }
        }
            catch
            {

            }
        }


        private void boltstdradius1_textBox_Validated(object sender, EventArgs e)
        {
            
            try
            {
            double stdboltdiametral  = double.Parse(boltstdradius1_textBox.Text);
            wheeltype = comboBox_wheellist.Text;
            wheeltype = wheeltype.Substring(0, wheeltype.LastIndexOf('C'));
            //从数据库读取中心孔坐标数据
            m_sqlfun.connection(connection);
            m_sqlfun.Sql_open();
            double m_boltdia = 0;
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);
                if (exist)
                {

                    m_sqlfun.Sql_Find("螺栓孔直径", "轮毂参数", wheeltype, out m_boltdia);
                }
            }
            if (stdboltdiametral - m_boltdia > 0.3 || stdboltdiametral - m_boltdia < -0.3)
            {
                MessageBox.Show("螺栓口直径输入异常：超出参数0.3以上");
            }
             }
            catch
            {

            }
            
        }

        private void hatstdradius_textBox_Validated(object sender, EventArgs e)
        {
            try
            {
                double hatstdradius = double.Parse(hatstdradius_textBox.Text);
                wheeltype = comboBox_wheellist.Text;
                wheeltype = wheeltype.Substring(0, wheeltype.LastIndexOf('H'));
                //从数据库读取中心孔坐标数据
                m_sqlfun.connection(connection);
                m_sqlfun.Sql_open();
                double m_hatstdradius = 0;
                if (m_sqlfun.conn.State == ConnectionState.Open)
                {
                    bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);
                    if (exist)
                    {

                        m_sqlfun.Sql_Find("帽止口直径", "轮毂参数", wheeltype, out m_hatstdradius);
                    }
                }
                if (hatstdradius - m_hatstdradius > 0.5 || hatstdradius - m_hatstdradius < -0.5)
                {
                    MessageBox.Show("帽止口直径输入异常：超出参数0.5以上");
                }
            }
            catch
            {

            }
        }


        private void censtdradius_textBox_TextChanged(object sender, EventArgs e)
        {

        }



        private void comboBox_wheellist_SelectedIndexChanged(object sender, EventArgs e)
        {
            string file = Directory.GetCurrentDirectory() + "\\model\\registerpicture\\" + comboBox_wheellist.Text;
            switch (comboBox_selecttype.Text)
            {
                case "中心孔":
                case "Center hole":
                case "Orificio central":
                case "螺栓孔":
                case "Bolt hole":
                case "Orificios de perno":

                    wheeltype = comboBox_wheellist.SelectedItem.ToString();
                    wheeltype = wheeltype.Substring(0, wheeltype.LastIndexOf('C'));
                    break;

                case "帽止口":
                case "cap stopper":
                case "Tope de tapa":
                    wheeltype = comboBox_wheellist.SelectedItem.ToString();
                    wheeltype = wheeltype.Substring(0, wheeltype.LastIndexOf('H'));
                    break;
                default:
                    wheeltype = comboBox_wheellist.SelectedItem.ToString();
                    break;
            }

            if (m_readimage)
            {
                HOperatorSet.GenEmptyObj(out m_caliper.ho_ContCircle1);
                HOperatorSet.GenEmptyObj(out m_caliper.ho_Cross);
                HOperatorSet.GenEmptyObj(out m_caliper.ho_Bolthole);

                HOperatorSet.GenEmptyObj(out m_caliper.ho_ContoursAffinTrans);
                HOperatorSet.GenEmptyObj(out m_caliper.ho_ContoursAffinTrans1);

                HOperatorSet.GenEmptyObj(out ho_ContCircle);
                HOperatorSet.GenEmptyObj(out ho_ContoursAffinTrans);
                try
                {

                    ho_Image.Dispose();
                    HOperatorSet.ReadImage(out ho_Image, file);

                    HOperatorSet.GetImageSize(ho_Image, out hv_width, out hv_height);

                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hv_height, hv_width);
                    HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);
                }
                catch
                {
                    return;
                }
            }
        }



        private void hWindowControl1_HMouseDown(object sender, HMouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Middle)
            {
                HTuple hv_k = null;
                HTuple Row, Column, Button;
             
                m_halconwindow.GetMouseDown(e, hWindowControl1.HalconWindow, out Row, out Column);
                RowDown = Row;    //鼠标按下时的行坐标
                ColDown = Column; //鼠标按下时的列坐标
            }


        }



        private void hWindowControl1_HMouseUp(object sender, HMouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Middle)
            {
                m_halconwindow.MoveImage(e, hWindowControl1.HalconWindow, RowDown, ColDown);
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);

                if (ho_Image.CountObj() != 0)
                {
                    HOperatorSet.GetImageSize(ho_Image, out hv_width, out hv_height);
                    HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);
                }
                else
                {
                    MessageBoxEX.Show(res.GetString("loadImg1"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
                //测量轮廓的体现
                if (m_caliper.ho_ContCircle1.CountObj() != 0)
                {
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                    HOperatorSet.DispObj(m_caliper.ho_ContCircle1, hWindowControl1.HalconWindow);
                    HOperatorSet.DispObj(m_caliper.ho_Cross, hWindowControl1.HalconWindow);
                    HOperatorSet.DispObj(m_caliper.ho_Bolthole, hWindowControl1.HalconWindow);
                    HOperatorSet.DispText(hWindowControl1.HalconWindow, m_caliper.hv_result, "window", 50,
                   50, "green", "box", "false");


                    if (m_caliper.ho_Bolthole.CountObj() != 0)
                    {
                        string m_print = "螺栓孔1直径:" + (m_boltholeresult.boltdiameter[0]) + "螺栓孔1位置度:" + (m_boltholeresult.positondeg[0]) + "\r\n" +
                             "螺栓孔2直径:" + (m_boltholeresult.boltdiameter[1]) + "螺栓孔2位置度:" + (m_boltholeresult.positondeg[1]) + "\r\n" +
                             "螺栓孔3直径:" + (m_boltholeresult.boltdiameter[2]) + "螺栓孔3位置度:" + (m_boltholeresult.positondeg[2]) + "\r\n" +
                             "螺栓孔4直径:" + (m_boltholeresult.boltdiameter[3]) + "螺栓孔4位置度:" + (m_boltholeresult.positondeg[3]) + "\r\n" +
                             "螺栓孔5直径:" + (m_boltholeresult.boltdiameter[4]) + "螺栓孔5位置度:" + (m_boltholeresult.positondeg[4]) + "\r\n" +
                             "螺栓孔6直径:" + (m_boltholeresult.boltdiameter[5]) + "螺栓孔6位置度:" + (m_boltholeresult.positondeg[5]);
                        HOperatorSet.DispText(hWindowControl1.HalconWindow, m_print, "window", 10,
                       50, "green", "box_color", "white");
                    }


                }
                else
                {
                    ////测量模型
                    if (m_caliper.ho_ContoursAffinTrans.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");
                        HOperatorSet.DispObj(m_caliper.ho_ContoursAffinTrans, hWindowControl1.HalconWindow);
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                        HOperatorSet.DispObj(m_caliper.ho_ContoursAffinTrans1, hWindowControl1.HalconWindow);


                    }

                    //绘制测量圆时候的圆
                    if (ho_ContCircle.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                        HOperatorSet.DispObj(ho_ContCircle, hWindowControl1.HalconWindow);
                    }
                    //显示中心孔匹配效果
                    if (ho_ContoursAffinTrans.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                        HOperatorSet.DispObj(ho_ContoursAffinTrans, hWindowControl1.HalconWindow);

                    }
                }



            }



        }

        private void hWindowControl1_HMouseWheel(object sender, HMouseEventArgs e)
        {
            try
            {
                HTuple hv_k = null;

                m_halconwindow.ScaleImage(e, hWindowControl1.HalconWindow);
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);

                HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);

                HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                if (m_caliper.ho_ContCircle1.CountObj() != 0)
                {
                    HOperatorSet.DispObj(m_caliper.ho_ContCircle1, hWindowControl1.HalconWindow);

                    HOperatorSet.DispObj(m_caliper.ho_Cross, hWindowControl1.HalconWindow);

                    HOperatorSet.DispObj(m_caliper.ho_Bolthole, hWindowControl1.HalconWindow);

                    HOperatorSet.DispText(hWindowControl1.HalconWindow, m_caliper.hv_result, "window", 50,
                  50, "green", "box", "false");

                    if (m_caliper.ho_Bolthole.CountObj() != 0)
                    {

                        string m_print = "中心孔尺寸:" + m_diameter.ToString() + "\r\n" +
                                           "螺栓孔1直径:" + (m_boltholeresult.boltdiameter[0]) + "螺栓孔1位置度:" + (m_boltholeresult.positondeg[0]) + "\r\n" +
                                           "螺栓孔2直径:" + (m_boltholeresult.boltdiameter[1]) + "螺栓孔2位置度:" + (m_boltholeresult.positondeg[1]) + "\r\n" +
                                           "螺栓孔3直径:" + (m_boltholeresult.boltdiameter[2]) + "螺栓孔3位置度:" + (m_boltholeresult.positondeg[2]) + "\r\n" +
                                           "螺栓孔4直径:" + (m_boltholeresult.boltdiameter[3]) + "螺栓孔4位置度:" + (m_boltholeresult.positondeg[3]) + "\r\n" +
                                           "螺栓孔5直径:" + (m_boltholeresult.boltdiameter[4]) + "螺栓孔5位置度:" + (m_boltholeresult.positondeg[4]) + "\r\n" +
                                           "螺栓孔6直径:" + (m_boltholeresult.boltdiameter[5]) + "螺栓孔6位置度:" + (m_boltholeresult.positondeg[5]);

                        HOperatorSet.DispText(hWindowControl1.HalconWindow, m_print, "window", 10,
                            50, "green", "box_color", "white");
                    }






                }
                else
                {
                    //测量模型
                    if (m_caliper.ho_ContoursAffinTrans.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "green");
                        HOperatorSet.DispObj(m_caliper.ho_ContoursAffinTrans, hWindowControl1.HalconWindow);
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                        HOperatorSet.DispObj(m_caliper.ho_ContoursAffinTrans1, hWindowControl1.HalconWindow);
                    }

                    //绘制测量圆时候的圆
                    if (ho_ContCircle.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                        HOperatorSet.DispObj(ho_ContCircle, hWindowControl1.HalconWindow);
                    }
                    //显示中心孔匹配效果
                    if (ho_ContoursAffinTrans.CountObj() != 0)
                    {
                        HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                        HOperatorSet.DispObj(ho_ContoursAffinTrans, hWindowControl1.HalconWindow);

                    }
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }


        //HOperatorSet.DispObj(ho_SelectedContours, hWindowControl1.HalconWindow);
        //HOperatorSet.SetColor(hWindowControl1.HalconWindow, "magenta");
        //HOperatorSet.DispObj(ho_OEmptyObject1, hWindowControl1.HalconWindow);







        private void btn_drawcircle_Click(object sender, EventArgs e)
        {
            try
            {
                HObject templateRegion = null;
                HOperatorSet.GenEmptyObj(out templateRegion);
                if (ho_Image.CountObj() != 0)
                {
                    hv_Row3.Dispose(); hv_Column3.Dispose(); hv_Radius3.Dispose();

                    // HOperatorSet.SetLineWidth(hWindowControl1.HalconWindow, 3);
                    HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "cyan");
                    hWindowControl1.Focus();
                    string language = Properties.Settings.Default.DefaultLanguage;
                    switch(language)
                    {
                        case "zn":
                            HOperatorSet.DispText(hWindowControl1.HalconWindow, "选择区域，右键结束", "window", "top",
                                                    "left", "green", "box", "false");
                            break;
                        case "en":
                            HOperatorSet.DispText(hWindowControl1.HalconWindow, "Select the area and right-click to end", "window", "top",
                        "left", "green", "box", "false");
                            break;
                        case "es":
                            HOperatorSet.DispText(hWindowControl1.HalconWindow, "Seleccione una región y haga clic con El botón derecho en finalizar", "window", "top",
                        "left", "green", "box", "false");
                            break;
                        case "fr":
                            HOperatorSet.DispText(hWindowControl1.HalconWindow, "Sélection d'une zone, fin avec le bouton droit de la souris", "window", "top",
                        "left", "green", "box", "false");
                            break;


                    }

                    // templateRegion.Dispose();
                    // m_halconwindow.Roi_DrawCircle(hWindowControl1.HalconWindow, out templateRegion);


                    // HOperatorSet.GetDrawingObjectParams(m_halconwindow.hv_DrawID0, "row", out hv_Row3);
                    //HOperatorSet.GetDrawingObjectParams(m_halconwindow.hv_DrawID0, "column", out hv_Column3);
                    //HOperatorSet.GetDrawingObjectParams(m_halconwindow.hv_DrawID0, "radius", out hv_Radius3);

                    mDrawing = true;
                    panel2.Enabled = false;
                    panel3.Enabled = false;
                    panel4.Enabled = false;
                    panel5.Enabled = false;
                    panel6.Enabled = false;

                    HOperatorSet.DrawCircle(hWindowControl1.HalconWindow, out hv_Row3, out hv_Column3, out hv_Radius3);


                    mDrawing = false;
                    panel2.Enabled = true;
                    panel3.Enabled = true;
                    panel4.Enabled = true;
                    panel5.Enabled = true;
                    panel6.Enabled = true;


                    ho_ContCircle.Dispose();
                    HOperatorSet.GenCircleContourXld(out ho_ContCircle, hv_Row3, hv_Column3, hv_Radius3,
                        0, 6.28318, "positive", 1);
                    HOperatorSet.DispObj(ho_ContCircle, hWindowControl1.HalconWindow);


                    btn_mtrsave.Enabled = true;


                }
                else
                    MessageBoxEX.Show(res.GetString("loadImg"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }
            catch { }
            
        }


        private void CircleCaliper_FormClosing(object sender, FormClosingEventArgs e)
        {
            e.Cancel = mDrawing;
        }

        private void CircleCaliperFormClosed(object sender, FormClosedEventArgs e)
        {
            ho_Image.Dispose();
            ho_ContCircle.Dispose();
            ho_ContoursAffinTrans.Dispose();
            m_caliper.ho_ContoursAffinTrans.Dispose();
            m_caliper.ho_ContoursAffinTrans1.Dispose();
            m_caliper.ho_ContCircle1.Dispose();
            m_caliper.ho_Cross.Dispose();


            RowDown.Dispose();
            ColDown.Dispose();
            hv_width.Dispose();
            hv_height.Dispose();
            hv_Row3.Dispose();
            hv_Column3.Dispose();
            hv_Radius3.Dispose();
            hv_boltModelRow.Dispose();
            hv_boltModelColumn.Dispose();
            hv_boltModelRadius.Dispose();

            
        }
    }
}

