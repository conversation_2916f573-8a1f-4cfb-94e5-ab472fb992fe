﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BingSolhalcon.UI
{
    public partial class Setting : DevExpress.XtraEditors.XtraForm
    {
        public Setting()
        {
            InitializeComponent();
        }

        private void Setting_Load(object sender, EventArgs e)
        {
            if (File.Exists(Application.StartupPath + "\\config.ini"))
            {
                Edit_deepZ.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "深度Z轴", "100", Application.StartupPath + "\\config.ini");
                Edit_deepX.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "深度X轴", "100", Application.StartupPath + "\\config.ini");
                Edit_centreZ.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "中心孔Z轴", "100", Application.StartupPath + "\\config.ini");
                Edit_hatZ.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "帽止口Z轴", "100", Application.StartupPath + "\\config.ini");
                Edit_ThicknessZ.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "厚度Z轴", "100", Application.StartupPath + "\\config.ini");
                Edit_markZ.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "标记Z轴", "100", Application.StartupPath + "\\config.ini");

                cbo_decimalnum.EditValue = ConfigIni.GetIniKeyValue("参数配置", "有效数据位", "2", Application.StartupPath + "\\config.ini");

                Edit_HoleScale.EditValue = ConfigIni.GetIniKeyValue("参数配置", "中心孔比例", "0.027368337315", Application.StartupPath + "\\config.ini");
                Edit_HatFocal.EditValue = ConfigIni.GetIniKeyValue("参数配置", "帽止口焦距", "372", Application.StartupPath + "\\config.ini");

                Edit_ReduceRow.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceRow", "3172", Application.StartupPath + "\\config.ini");
                Edit_ReduceCol.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceCol", "4731", Application.StartupPath + "\\config.ini");
                Edit_ReduceRadius.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius", "1650", Application.StartupPath + "\\config.ini");

                Edit_ReduceRow_C3.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceRow_C3", "2564", Application.StartupPath + "\\config.ini");
                Edit_ReduceCol_C3.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceCol_C3", "2283", Application.StartupPath + "\\config.ini");
                Edit_ReduceRadius_C3.EditValue = ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius_C3", "1892", Application.StartupPath + "\\config.ini");

                rbtnMEGAPHASE_Yes.Checked = ConfigIni.GetIniKeyValue("参数配置", "MEGAPHASE", "True", Application.StartupPath + "\\config.ini").Equals("True");
                rbtnCamera2_CameraLink.Checked = ConfigIni.GetIniKeyValue("参数配置", "CameraLink", "False", Application.StartupPath + "\\config.ini").Equals("True");

                cbox_posDegReverse.Checked = ConfigIni.GetIniKeyValue("参数配置", "位置度顺序翻转", "False", Application.StartupPath + "\\config.ini").Equals("True");

                Edit_defExposureTimeCen.Text = ConfigIni.GetIniKeyValue("参数配置", "中心孔默认曝光时长", "140000", Application.StartupPath + "\\config.ini");
                Edit_defExposureTimeHat.Text = ConfigIni.GetIniKeyValue("参数配置", "帽止口默认曝光时长", "140000", Application.StartupPath + "\\config.ini");
                Edit_wheelParam.Text = ConfigIni.GetIniKeyValue("参数配置", "轮毂参数表", "", Application.StartupPath + "\\config.ini");

                cbox_sendNGPosition.Checked = ConfigIni.GetIniKeyValue("参数配置", "发送NG位置", "False", Application.StartupPath + "\\config.ini").Equals("True");
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            ConfigIni.WriteIniKey("通讯地址", "深度Z轴", Edit_deepZ.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "深度X轴", Edit_deepX.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "中心孔Z轴", Edit_centreZ.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "帽止口Z轴", Edit_hatZ.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "厚度Z轴", Edit_ThicknessZ.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("通讯地址", "标记Z轴", Edit_markZ.EditValue.ToString(), Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "有效数据位", cbo_decimalnum.EditValue.ToString(), Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "中心孔比例", Edit_HoleScale.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "帽止口焦距", Edit_HatFocal.EditValue.ToString(), Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "ReduceRow", Edit_ReduceRow.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "ReduceCol", Edit_ReduceCol.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "ReduceRadius", Edit_ReduceRadius.EditValue.ToString(), Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "ReduceRow_C3", Edit_ReduceRow_C3.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "ReduceCol_C3", Edit_ReduceCol_C3.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "ReduceRadius_C3", Edit_ReduceRadius_C3.EditValue.ToString(), Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "MEGAPHASE", rbtnMEGAPHASE_Yes.Checked ? "True" : "False", Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "CameraLink", rbtnCamera2_CameraLink.Checked ? "True" : "False", Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "位置度顺序翻转", cbox_posDegReverse.Checked ? "True" : "False", Application.StartupPath + "\\config.ini");

            ConfigIni.WriteIniKey("参数配置", "中心孔默认曝光时长", Edit_defExposureTimeCen.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey("参数配置", "帽止口默认曝光时长", Edit_defExposureTimeHat.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            ConfigIni.WriteIniKey2("参数配置", "轮毂参数表", Edit_wheelParam.EditValue.ToString(), Application.StartupPath + "\\config.ini");
            

            ConfigIni.WriteIniKey("参数配置", "发送NG位置", cbox_sendNGPosition.Checked ? "True" : "False", Application.StartupPath + "\\config.ini");

            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            Close();
        }
    }
}
