﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Networking
{
    public class ClientMgr
    {
        static AsyncTcpClient _tcpClient = null;
        private string _localIp = "127.0.0.1";
        private int _port = 888;
        public static bool _connectOk = false;

        /// <summary>
        /// 写数据是否成功标志量,1表示成功
        /// </summary>
        public static int WriteDataFlag = 0;
        public static int RecvDataFlag = 0;
        public static int RecvSnapFlag = 0;

        private Thread recvDataThrd = null;
        CancellationTokenSource recvDataCts = null;
        object recvObj = new object();

        #region 事件
        public delegate void ServerConnected(string ip);
        public ServerConnected OnServerConnected;
        public delegate void ServerDisconnected(string ip);
        public ServerDisconnected OnServerDisconnected;
        public delegate void ReceiveComData(ComData comdate);
        public ReceiveComData OnReceiveComData;
        #endregion

        #region 构造器
        public ClientMgr()
            : this("127.0.0.1", 888)
        {
        }

        public ClientMgr(string ip, int port)
        {
            this._localIp = ip;
            this._port = port;
        }
        #endregion

        public bool open()
        {
            //测试用，可以不指定由系统选择端口
            _tcpClient = new AsyncTcpClient(this._localIp, this._port, (new ComData()).GetType());
            _tcpClient.Encoding = Encoding.UTF8;
            _tcpClient.ServerExceptionOccurred +=
                new EventHandler<TcpServerExceptionOccurredEventArgs>(client_ServerExceptionOccurred);
            _tcpClient.ServerConnected +=
                new EventHandler<TcpServerConnectedEventArgs>(client_ServerConnected);
            _tcpClient.ServerDisconnected +=
                new EventHandler<TcpServerDisconnectedEventArgs>(client_ServerDisconnected);
            _tcpClient.PlaintextReceived +=
                new EventHandler<TcpDatagramReceivedEventArgs<string>>(client_PlaintextReceived);
            _tcpClient.Connect();

            //启动侦听线程
            this.recvDataCts = new CancellationTokenSource();
            this.recvDataThrd = new Thread(new ThreadStart(this.RecvDataFunc));
            this.recvDataThrd.IsBackground = true;
            this.recvDataThrd.Start();

            return true;
        }

        public void close()
        {
            _tcpClient.Dispose();
        }

        /// <summary>
        /// 服务器发生异常
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void client_ServerExceptionOccurred(
          object sender, TcpServerExceptionOccurredEventArgs e)
        {
            //Logger.Debug(string.Format(CultureInfo.InvariantCulture,
            //  "TCP server {0} exception occurred, {1}.",
            //  e.ToString(), e.Exception.Message));
        }

        private void client_ServerConnected(
          object sender, TcpServerConnectedEventArgs e)
        {
            //Logger.Debug(string.Format(CultureInfo.InvariantCulture,
            //  "TCP server {0} has connected.", e.ToString()));

            if (OnServerConnected != null)
            {
                OnServerConnected(e.ToString());
            }

            _connectOk = true;
        }

        private void client_ServerDisconnected(
          object sender, TcpServerDisconnectedEventArgs e)
        {
            //Logger.Debug(string.Format(CultureInfo.InvariantCulture,
            //  "TCP server {0} has disconnected.", e.ToString()));
            if (OnServerDisconnected != null)
            {
                OnServerDisconnected(e.ToString());
            }

            _connectOk = false;
        }

        static void client_PlaintextReceived(
          object sender, TcpDatagramReceivedEventArgs<string> e)
        {
            //Console.Write(string.Format("Server : {0} --> ",
            //  e.TcpClient.Client.RemoteEndPoint.ToString()));
            //Console.WriteLine(string.Format("{0}", e.Datagram));
        }

        #region 指令解析线程
        //指令集解析线程
        private void RecvDataFunc()
        {
            while (true)
            {
                if (recvDataCts.Token.IsCancellationRequested)
                    return;

                // 线程锁
                lock (recvObj)
                {
                    if (_tcpClient.comDataQueue.Count < 1)
                        continue;
                }
                // 查询指令队列对首
                ComData _comData = _tcpClient.comDataQueue.Peek();
                // 将数据发送去
                if (OnReceiveComData != null)
                {
                    OnReceiveComData(_comData);
                }
                // 将队首的指令移除
                if (_tcpClient.comDataQueue.Count > 0)
                    _tcpClient.comDataQueue.Dequeue();
            }
        }
        #endregion

        #region 成品检测1相机
        #endregion

        public void SendData(ComData comData)
        {
            _tcpClient.Send(comData);
        }

        #region 字符串处理
        public static string CharToString(char[] chars)
        {
            string str = "";
            int len = 0;
            for (int i = 0; i < chars.Length; i++)
            {
                if (chars[i] == '\0')
                {
                    len = i;
                    break;
                }
            }
            string msg = new string(chars);
            str = msg.Substring(0, len);
            return str;
        }

        /// <summary>
        /// 将列表中的数据通过-号连接，并返回连接后的字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="lst"></param>
        /// <returns></returns>
        public static string GetString<T>(List<T> lst)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < lst.Count; i++)
            {
                sb.Append(lst[i]);
                if (i < lst.Count - 1)
                    sb.Append("-");
            }
            return sb.ToString();
        }
        #endregion
    }
}
