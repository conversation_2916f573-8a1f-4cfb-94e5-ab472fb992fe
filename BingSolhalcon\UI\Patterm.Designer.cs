﻿namespace BingSolhalcon
{
    partial class Patterm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Patterm));
            this.label1 = new System.Windows.Forms.Label();
            this.wheelmodol = new System.Windows.Forms.TextBox();
            this.tbc_shapeMatch = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.panel3 = new System.Windows.Forms.Panel();
            this.comboBox_camera = new System.Windows.Forms.ComboBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.comboBox_selecttype = new System.Windows.Forms.ComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.typeLength = new System.Windows.Forms.ComboBox();
            this.threshold_groupBox = new System.Windows.Forms.GroupBox();
            this.threshold_textBox = new System.Windows.Forms.TextBox();
            this.threshold_trackBar = new System.Windows.Forms.TrackBar();
            this.label12 = new System.Windows.Forms.Label();
            this.panel1 = new System.Windows.Forms.Panel();
            this.cbox_zml = new System.Windows.Forms.CheckBox();
            this.ConvertHalcon = new System.Windows.Forms.Button();
            this.ReadImage_button = new System.Windows.Forms.Button();
            this.btn_clearpattrem = new System.Windows.Forms.Button();
            this.btn_FullWindow = new System.Windows.Forms.Button();
            this.Save = new System.Windows.Forms.Button();
            this.btn_add = new System.Windows.Forms.Button();
            this.hWindowControl1 = new HalconDotNet.HWindowControl();
            this.hWindowControl2 = new HalconDotNet.HWindowControl();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.label11 = new System.Windows.Forms.Label();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.cb_polarity = new System.Windows.Forms.ComboBox();
            this.tb_externtime = new System.Windows.Forms.TextBox();
            this.nUpDown_AngleStep = new System.Windows.Forms.NumericUpDown();
            this.nUpDown_AngleRang = new System.Windows.Forms.NumericUpDown();
            this.nUpDown_StartAngle = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.listView_wheeltable = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.tbc_shapeMatch.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.panel3.SuspendLayout();
            this.threshold_groupBox.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.threshold_trackBar)).BeginInit();
            this.panel1.SuspendLayout();
            this.tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_AngleStep)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_AngleRang)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_StartAngle)).BeginInit();
            this.tabPage5.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // wheelmodol
            // 
            resources.ApplyResources(this.wheelmodol, "wheelmodol");
            this.wheelmodol.Name = "wheelmodol";
            this.wheelmodol.TextChanged += new System.EventHandler(this.wheelmodol_TextChanged);
            // 
            // tbc_shapeMatch
            // 
            this.tbc_shapeMatch.Controls.Add(this.tabPage1);
            this.tbc_shapeMatch.Controls.Add(this.tabPage3);
            this.tbc_shapeMatch.Controls.Add(this.tabPage5);
            resources.ApplyResources(this.tbc_shapeMatch, "tbc_shapeMatch");
            this.tbc_shapeMatch.Name = "tbc_shapeMatch";
            this.tbc_shapeMatch.SelectedIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.BackColor = System.Drawing.Color.LightGray;
            this.tabPage1.Controls.Add(this.panel3);
            this.tabPage1.Controls.Add(this.panel1);
            this.tabPage1.Controls.Add(this.hWindowControl1);
            this.tabPage1.Controls.Add(this.hWindowControl2);
            resources.ApplyResources(this.tabPage1, "tabPage1");
            this.tabPage1.Name = "tabPage1";
            // 
            // panel3
            // 
            resources.ApplyResources(this.panel3, "panel3");
            this.panel3.Controls.Add(this.comboBox_camera);
            this.panel3.Controls.Add(this.label14);
            this.panel3.Controls.Add(this.label1);
            this.panel3.Controls.Add(this.label2);
            this.panel3.Controls.Add(this.comboBox_selecttype);
            this.panel3.Controls.Add(this.label13);
            this.panel3.Controls.Add(this.typeLength);
            this.panel3.Controls.Add(this.threshold_groupBox);
            this.panel3.Controls.Add(this.wheelmodol);
            this.panel3.Name = "panel3";
            // 
            // comboBox_camera
            // 
            this.comboBox_camera.AutoCompleteCustomSource.AddRange(new string[] {
            resources.GetString("comboBox_camera.AutoCompleteCustomSource"),
            resources.GetString("comboBox_camera.AutoCompleteCustomSource1"),
            resources.GetString("comboBox_camera.AutoCompleteCustomSource2")});
            resources.ApplyResources(this.comboBox_camera, "comboBox_camera");
            this.comboBox_camera.FormattingEnabled = true;
            this.comboBox_camera.Items.AddRange(new object[] {
            resources.GetString("comboBox_camera.Items"),
            resources.GetString("comboBox_camera.Items1"),
            resources.GetString("comboBox_camera.Items2")});
            this.comboBox_camera.Name = "comboBox_camera";
            this.comboBox_camera.SelectedIndexChanged += new System.EventHandler(this.comboBox_camera_SelectedIndexChanged);
            // 
            // label14
            // 
            resources.ApplyResources(this.label14, "label14");
            this.label14.Name = "label14";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // comboBox_selecttype
            // 
            this.comboBox_selecttype.AutoCompleteCustomSource.AddRange(new string[] {
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource"),
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource1"),
            resources.GetString("comboBox_selecttype.AutoCompleteCustomSource2")});
            resources.ApplyResources(this.comboBox_selecttype, "comboBox_selecttype");
            this.comboBox_selecttype.FormattingEnabled = true;
            this.comboBox_selecttype.Items.AddRange(new object[] {
            resources.GetString("comboBox_selecttype.Items"),
            resources.GetString("comboBox_selecttype.Items1"),
            resources.GetString("comboBox_selecttype.Items2"),
            resources.GetString("comboBox_selecttype.Items3"),
            resources.GetString("comboBox_selecttype.Items4"),
            resources.GetString("comboBox_selecttype.Items5")});
            this.comboBox_selecttype.Name = "comboBox_selecttype";
            this.comboBox_selecttype.TextChanged += new System.EventHandler(this.comboBox_selecttype_TextChanged);
            // 
            // label13
            // 
            resources.ApplyResources(this.label13, "label13");
            this.label13.Name = "label13";
            // 
            // typeLength
            // 
            this.typeLength.FormattingEnabled = true;
            this.typeLength.Items.AddRange(new object[] {
            resources.GetString("typeLength.Items"),
            resources.GetString("typeLength.Items1"),
            resources.GetString("typeLength.Items2")});
            resources.ApplyResources(this.typeLength, "typeLength");
            this.typeLength.Name = "typeLength";
            // 
            // threshold_groupBox
            // 
            this.threshold_groupBox.Controls.Add(this.threshold_textBox);
            this.threshold_groupBox.Controls.Add(this.threshold_trackBar);
            this.threshold_groupBox.Controls.Add(this.label12);
            resources.ApplyResources(this.threshold_groupBox, "threshold_groupBox");
            this.threshold_groupBox.Name = "threshold_groupBox";
            this.threshold_groupBox.TabStop = false;
            // 
            // threshold_textBox
            // 
            resources.ApplyResources(this.threshold_textBox, "threshold_textBox");
            this.threshold_textBox.Name = "threshold_textBox";
            // 
            // threshold_trackBar
            // 
            this.threshold_trackBar.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.threshold_trackBar, "threshold_trackBar");
            this.threshold_trackBar.Maximum = 255;
            this.threshold_trackBar.Name = "threshold_trackBar";
            this.threshold_trackBar.Value = 80;
            this.threshold_trackBar.Scroll += new System.EventHandler(this.threshold_trackBar_Scroll);
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // panel1
            // 
            resources.ApplyResources(this.panel1, "panel1");
            this.panel1.Controls.Add(this.cbox_zml);
            this.panel1.Controls.Add(this.ConvertHalcon);
            this.panel1.Controls.Add(this.ReadImage_button);
            this.panel1.Controls.Add(this.btn_clearpattrem);
            this.panel1.Controls.Add(this.btn_FullWindow);
            this.panel1.Controls.Add(this.Save);
            this.panel1.Controls.Add(this.btn_add);
            this.panel1.Name = "panel1";
            // 
            // cbox_zml
            // 
            resources.ApplyResources(this.cbox_zml, "cbox_zml");
            this.cbox_zml.Name = "cbox_zml";
            this.cbox_zml.UseVisualStyleBackColor = true;
            this.cbox_zml.CheckedChanged += new System.EventHandler(this.cbox_zml_CheckedChanged);
            // 
            // ConvertHalcon
            // 
            this.ConvertHalcon.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.ConvertHalcon, "ConvertHalcon");
            this.ConvertHalcon.FlatAppearance.BorderSize = 0;
            this.ConvertHalcon.Name = "ConvertHalcon";
            this.ConvertHalcon.UseVisualStyleBackColor = false;
            this.ConvertHalcon.Click += new System.EventHandler(this.ConvertHalcon_Click);
            // 
            // ReadImage_button
            // 
            this.ReadImage_button.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.ReadImage_button, "ReadImage_button");
            this.ReadImage_button.FlatAppearance.BorderSize = 0;
            this.ReadImage_button.Name = "ReadImage_button";
            this.ReadImage_button.UseVisualStyleBackColor = false;
            this.ReadImage_button.Click += new System.EventHandler(this.ReadImage_button_Click);
            // 
            // btn_clearpattrem
            // 
            this.btn_clearpattrem.BackColor = System.Drawing.SystemColors.Control;
            this.btn_clearpattrem.FlatAppearance.BorderSize = 0;
            resources.ApplyResources(this.btn_clearpattrem, "btn_clearpattrem");
            this.btn_clearpattrem.Name = "btn_clearpattrem";
            this.btn_clearpattrem.UseVisualStyleBackColor = false;
            this.btn_clearpattrem.Click += new System.EventHandler(this.btn_clearpattrem_Click);
            // 
            // btn_FullWindow
            // 
            this.btn_FullWindow.BackColor = System.Drawing.SystemColors.Control;
            this.btn_FullWindow.FlatAppearance.BorderSize = 0;
            resources.ApplyResources(this.btn_FullWindow, "btn_FullWindow");
            this.btn_FullWindow.Name = "btn_FullWindow";
            this.btn_FullWindow.UseVisualStyleBackColor = false;
            this.btn_FullWindow.Click += new System.EventHandler(this.btn_FullWindow_Click);
            // 
            // Save
            // 
            this.Save.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.Save, "Save");
            this.Save.FlatAppearance.BorderSize = 0;
            this.Save.Name = "Save";
            this.Save.UseVisualStyleBackColor = false;
            this.Save.Click += new System.EventHandler(this.Save_Click);
            // 
            // btn_add
            // 
            this.btn_add.BackColor = System.Drawing.SystemColors.Control;
            this.btn_add.FlatAppearance.BorderSize = 0;
            resources.ApplyResources(this.btn_add, "btn_add");
            this.btn_add.Name = "btn_add";
            this.btn_add.UseVisualStyleBackColor = false;
            this.btn_add.Click += new System.EventHandler(this.btn_add_Click);
            // 
            // hWindowControl1
            // 
            resources.ApplyResources(this.hWindowControl1, "hWindowControl1");
            this.hWindowControl1.BackColor = System.Drawing.Color.Black;
            this.hWindowControl1.BorderColor = System.Drawing.Color.Black;
            this.hWindowControl1.ImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hWindowControl1.Name = "hWindowControl1";
            this.hWindowControl1.WindowSize = new System.Drawing.Size(827, 648);
            this.hWindowControl1.HMouseUp += new HalconDotNet.HMouseEventHandler(this.hWindowControl_HMouseUp);
            // 
            // hWindowControl2
            // 
            resources.ApplyResources(this.hWindowControl2, "hWindowControl2");
            this.hWindowControl2.BackColor = System.Drawing.Color.Black;
            this.hWindowControl2.BorderColor = System.Drawing.Color.Black;
            this.hWindowControl2.ImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hWindowControl2.Name = "hWindowControl2";
            this.hWindowControl2.WindowSize = new System.Drawing.Size(291, 222);
            this.hWindowControl2.HMouseDown += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseDown);
            this.hWindowControl2.HMouseUp += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseUp);
            this.hWindowControl2.HMouseWheel += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseWheel);
            // 
            // tabPage3
            // 
            this.tabPage3.BackColor = System.Drawing.Color.LightGray;
            this.tabPage3.Controls.Add(this.label11);
            this.tabPage3.Controls.Add(this.checkBox1);
            this.tabPage3.Controls.Add(this.label10);
            this.tabPage3.Controls.Add(this.label9);
            this.tabPage3.Controls.Add(this.label8);
            this.tabPage3.Controls.Add(this.cb_polarity);
            this.tabPage3.Controls.Add(this.tb_externtime);
            this.tabPage3.Controls.Add(this.nUpDown_AngleStep);
            this.tabPage3.Controls.Add(this.nUpDown_AngleRang);
            this.tabPage3.Controls.Add(this.nUpDown_StartAngle);
            this.tabPage3.Controls.Add(this.label7);
            this.tabPage3.Controls.Add(this.label6);
            this.tabPage3.Controls.Add(this.label5);
            this.tabPage3.Controls.Add(this.label4);
            this.tabPage3.Controls.Add(this.label3);
            resources.ApplyResources(this.tabPage3, "tabPage3");
            this.tabPage3.Name = "tabPage3";
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // checkBox1
            // 
            this.checkBox1.Checked = true;
            this.checkBox1.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkBox1, "checkBox1");
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // label10
            // 
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // label9
            // 
            resources.ApplyResources(this.label9, "label9");
            this.label9.Name = "label9";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // cb_polarity
            // 
            this.cb_polarity.FormattingEnabled = true;
            this.cb_polarity.Items.AddRange(new object[] {
            resources.GetString("cb_polarity.Items"),
            resources.GetString("cb_polarity.Items1"),
            resources.GetString("cb_polarity.Items2"),
            resources.GetString("cb_polarity.Items3")});
            resources.ApplyResources(this.cb_polarity, "cb_polarity");
            this.cb_polarity.Name = "cb_polarity";
            // 
            // tb_externtime
            // 
            resources.ApplyResources(this.tb_externtime, "tb_externtime");
            this.tb_externtime.Name = "tb_externtime";
            // 
            // nUpDown_AngleStep
            // 
            resources.ApplyResources(this.nUpDown_AngleStep, "nUpDown_AngleStep");
            this.nUpDown_AngleStep.Name = "nUpDown_AngleStep";
            this.nUpDown_AngleStep.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // nUpDown_AngleRang
            // 
            resources.ApplyResources(this.nUpDown_AngleRang, "nUpDown_AngleRang");
            this.nUpDown_AngleRang.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nUpDown_AngleRang.Name = "nUpDown_AngleRang";
            this.nUpDown_AngleRang.Value = new decimal(new int[] {
            360,
            0,
            0,
            0});
            // 
            // nUpDown_StartAngle
            // 
            resources.ApplyResources(this.nUpDown_StartAngle, "nUpDown_StartAngle");
            this.nUpDown_StartAngle.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nUpDown_StartAngle.Minimum = new decimal(new int[] {
            180,
            0,
            0,
            -2147483648});
            this.nUpDown_StartAngle.Name = "nUpDown_StartAngle";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // tabPage5
            // 
            this.tabPage5.BackColor = System.Drawing.Color.LightGray;
            this.tabPage5.Controls.Add(this.listView_wheeltable);
            resources.ApplyResources(this.tabPage5, "tabPage5");
            this.tabPage5.Name = "tabPage5";
            // 
            // listView_wheeltable
            // 
            resources.ApplyResources(this.listView_wheeltable, "listView_wheeltable");
            this.listView_wheeltable.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listView_wheeltable.GridLines = true;
            this.listView_wheeltable.HideSelection = false;
            this.listView_wheeltable.Name = "listView_wheeltable";
            this.listView_wheeltable.UseCompatibleStateImageBehavior = false;
            this.listView_wheeltable.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            resources.ApplyResources(this.columnHeader1, "columnHeader1");
            // 
            // columnHeader2
            // 
            resources.ApplyResources(this.columnHeader2, "columnHeader2");
            // 
            // columnHeader3
            // 
            resources.ApplyResources(this.columnHeader3, "columnHeader3");
            // 
            // Patterm
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tbc_shapeMatch);
            this.Name = "Patterm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.TrainPattemClosed);
            this.Load += new System.EventHandler(this.Patterm_Load);
            this.Resize += new System.EventHandler(this.Form1_Resize);
            this.tbc_shapeMatch.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            this.threshold_groupBox.ResumeLayout(false);
            this.threshold_groupBox.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.threshold_trackBar)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_AngleStep)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_AngleRang)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nUpDown_StartAngle)).EndInit();
            this.tabPage5.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox wheelmodol;
     
        private System.Windows.Forms.TabControl tbc_shapeMatch;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.CheckBox checkBox1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ComboBox cb_polarity;
        private System.Windows.Forms.TextBox tb_externtime;
        private System.Windows.Forms.NumericUpDown nUpDown_AngleStep;
        private System.Windows.Forms.NumericUpDown nUpDown_AngleRang;
        private System.Windows.Forms.NumericUpDown nUpDown_StartAngle;
        private System.Windows.Forms.TextBox threshold_textBox;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TrackBar threshold_trackBar;
        private System.Windows.Forms.GroupBox threshold_groupBox;
        private System.Windows.Forms.ComboBox comboBox_selecttype;
        private System.Windows.Forms.Label label2;
        public System.Windows.Forms.ListView listView_wheeltable;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private HalconDotNet.HWindowControl hWindowControl1;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.ComboBox typeLength;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.ComboBox comboBox_camera;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.CheckBox cbox_zml;
        private System.Windows.Forms.Button ConvertHalcon;
        private System.Windows.Forms.Button ReadImage_button;
        private System.Windows.Forms.Button btn_clearpattrem;
        private System.Windows.Forms.Button btn_FullWindow;
        private System.Windows.Forms.Button Save;
        private System.Windows.Forms.Button btn_add;
        private HalconDotNet.HWindowControl hWindowControl2;
    }
}