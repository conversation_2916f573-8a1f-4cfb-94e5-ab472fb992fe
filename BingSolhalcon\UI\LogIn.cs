﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BingSolhalcon.resources;

namespace BingSolhalcon
{
    public partial class LogIn : Form
    {

        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx));

        public LogIn()
        {
            InitializeComponent();
        }



        private void pictureBox3_Click(object sender, EventArgs e)
        {
            this.Close();
            this.Dispose();
        }

        private void pictureBox3_MouseEnter(object sender, EventArgs e)
        {
            pictureBox3.BackColor = Color.Red;
        }

        private void pictureBox3_MouseLeave(object sender, EventArgs e)
        {
            pictureBox3.BackColor = Color.FromArgb(40, 43, 53);
        }

        private void btnLogIn_Click_1(object sender, EventArgs e)
        {
            try
            {
                string dbserver = ConfigIni.GetIniKeyValue("通讯地址", "数据库名称", ".", Application.StartupPath + "\\config.ini");
                string connection = @"Server=" + dbserver + ";Database=MyDB;Trusted_Connection=SSPI";

                string loginName = this.txtLoginName.Text.Trim();
                DataSet dset = new DataSet();
                SQlFun m_sqlfun = new SQlFun();
                m_sqlfun.connection(connection);
                m_sqlfun.Sql_open();
                if (m_sqlfun.conn.State == ConnectionState.Open)
                {

                    string language = Properties.Settings.Default.DefaultLanguage;
                    switch (language)
                    {
                        case "en":
                        case "fr":
                            if (loginName == "Operator")
                                loginName = "操作员";
                            if (loginName == "Technician")
                                loginName = "技术员";
                            if (loginName == "Expert")
                                loginName = "专家";
                            break;
                        case "zh":
                            break;
                        case "es":
                            if (loginName == "operador")
                                loginName = "操作员";
                            if (loginName == "técnico")
                                loginName = "技术员";
                            if (loginName == "experto")
                                loginName = "专家";
                            break;
                        default:

                            break;
                    }

                    m_sqlfun.fill_datatable("syuser");
                    DataRow Userrows;
                    for (int i = 0; i < m_sqlfun.dTable.Rows.Count; i++)
                    {
                        Userrows = m_sqlfun.dTable.Rows[i];
                        if (loginName == Userrows[1].ToString().Trim() && txtPassword.Text == Userrows[2].ToString().Trim())
                        {
                            MessageBoxEX.Show(res.GetString("loginSuccess"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

                            this.DialogResult = DialogResult.OK;

                            // this.Dispose();
                            // this.Close();
                            return;
                        }
                        else
                        {

                            continue;
                        }
                    }
                    MessageBoxEX.Show(res.GetString("faultPaw"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
                else
                {
                    MessageBoxEX.Show(res.GetString("databaseFault"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }

                //if (loginName == "技术员" && txtPassword.Text == "123456")
                //{

                //    MessageBox.Show("登录成功");
                //    this.DialogResult = DialogResult.OK;
                //    this.Dispose();
                //    this.Close();
                //}
                //else
                //{
                //    MessageBox.Show("密码不正确");
                //    return;
                //}
            }
            catch (Exception err)
            {
                MessageBox.Show(err.Message);
            }


        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            Application.ExitThread();
        }

        private void LogIn_Load(object sender, EventArgs e)
        {

            //加载默认语言
            //MultiLanguage multilanguage = new MultiLanguage();

            //multilanguage.LoadDefaultLanguage(this, typeof(LogIn));
        }

        private void linkModifyPassword_OpenLink(object sender, DevExpress.XtraEditors.Controls.OpenLinkEventArgs e)
        {
            Hide();
            new ModifyPassword().ShowDialog();
            Show();
        }
    }
}
