﻿#pragma checksum "..\..\..\..\Windows\CameraControlWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "091BE59FB98FE06BC185D19B9B742932362911AC"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CameraTechVerify.Windows {
    
    
    /// <summary>
    /// CameraControlWindow
    /// </summary>
    public partial class CameraControlWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshDevicesBtn;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DeviceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshGigEBtn;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DeviceComboBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl ConnectionTabControl;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectBtn;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectBtn;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IpAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PortTextBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PingBtn;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectByIPBtn;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectBySerialBtn;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MacAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectByMacBtn;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartGrabbingBtn;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopGrabbingBtn;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CaptureBtn;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveImageBtn;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExposureTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GainTextBox;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FrameRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TriggerModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SoftwareTriggerBtn;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ForceIPMacTextBox;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ForceIPAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ForceSubnetMaskTextBox;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ForceGatewayTextBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ForceIPBtn;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageInfoText;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageCountText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CameraImage;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoImageText;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameText;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelNameText;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberText;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionTypeText;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentExposureText;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentGainText;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentFrameRateText;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentResolutionText;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTriggerModeText;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalImagesText;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrameRateStatsText;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastImageTimeText;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\Windows\CameraControlWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SDKStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CameraTechVerify;component/windows/cameracontrolwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\CameraControlWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.RefreshDevicesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.RefreshDevicesBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshDevicesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeviceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 85 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.DeviceTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DeviceTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RefreshGigEBtn = ((System.Windows.Controls.Button)(target));
            
            #line 92 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.RefreshGigEBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshGigEBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DeviceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 96 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.DeviceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DeviceComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ConnectionTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 7:
            this.ConnectBtn = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.ConnectBtn.Click += new System.Windows.RoutedEventHandler(this.ConnectBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DisconnectBtn = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.DisconnectBtn.Click += new System.Windows.RoutedEventHandler(this.DisconnectBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.IpAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.PortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.PingBtn = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.PingBtn.Click += new System.Windows.RoutedEventHandler(this.PingBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ConnectByIPBtn = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.ConnectByIPBtn.Click += new System.Windows.RoutedEventHandler(this.ConnectByIPBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SerialNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ConnectBySerialBtn = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.ConnectBySerialBtn.Click += new System.Windows.RoutedEventHandler(this.ConnectBySerialBtn_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.MacAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.ConnectByMacBtn = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.ConnectByMacBtn.Click += new System.Windows.RoutedEventHandler(this.ConnectByMacBtn_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.StartGrabbingBtn = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.StartGrabbingBtn.Click += new System.Windows.RoutedEventHandler(this.StartGrabbingBtn_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.StopGrabbingBtn = ((System.Windows.Controls.Button)(target));
            
            #line 154 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.StopGrabbingBtn.Click += new System.Windows.RoutedEventHandler(this.StopGrabbingBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CaptureBtn = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.CaptureBtn.Click += new System.Windows.RoutedEventHandler(this.CaptureBtn_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.SaveImageBtn = ((System.Windows.Controls.Button)(target));
            
            #line 159 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.SaveImageBtn.Click += new System.Windows.RoutedEventHandler(this.SaveImageBtn_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ExposureTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            
            #line 171 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetExposureBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.GainTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            
            #line 179 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetGainBtn_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.FrameRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            
            #line 187 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetFrameRateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.TriggerModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 193 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.TriggerModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TriggerModeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 28:
            this.SoftwareTriggerBtn = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.SoftwareTriggerBtn.Click += new System.Windows.RoutedEventHandler(this.SoftwareTriggerBtn_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 206 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ReadParametersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 208 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveParametersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 210 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadParametersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.ForceIPMacTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.ForceIPAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.ForceSubnetMaskTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.ForceGatewayTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.ForceIPBtn = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\..\Windows\CameraControlWindow.xaml"
            this.ForceIPBtn.Click += new System.Windows.RoutedEventHandler(this.ForceIPBtn_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 257 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeAPIBtn_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            
            #line 259 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 261 "..\..\..\..\Windows\CameraControlWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestBasicBtn_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.ImageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.ImageCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.CameraImage = ((System.Windows.Controls.Image)(target));
            return;
            case 43:
            this.NoImageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.DeviceNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.ModelNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.SerialNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 47:
            this.ConnectionTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.CurrentExposureText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.CurrentGainText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.CurrentFrameRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.CurrentResolutionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.CurrentTriggerModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.TotalImagesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.FrameRateStatsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 55:
            this.LastImageTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 57:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.SDKStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

