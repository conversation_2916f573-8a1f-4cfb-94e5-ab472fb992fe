﻿namespace BingSolhalcon
{
    partial class CameraParaSet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        /// 

        //protected override void Dispose(bool disposing)
        //{
        //    //if (disposing && (components != null))
        //    //{
        //    //    components.Dispose();
        //    //}
        //    //base.Dispose(disposing);
        //    Hide();
        //}
        protected override void Dispose(bool disposing)
        {
            //if (disposing && (components != null))
            //{
            //    components.Dispose();
            //}
            //base.Dispose(disposing);
            Hide();
        }  

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CameraParaSet));
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.rdbHWTrigger2 = new System.Windows.Forms.RadioButton();
            this.rdbSWTrigger2 = new System.Windows.Forms.RadioButton();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.rdbtrigger2 = new System.Windows.Forms.RadioButton();
            this.rdbFreerun2 = new System.Windows.Forms.RadioButton();
            this.txtBExposure2 = new System.Windows.Forms.TextBox();
            this.txtBGain2 = new System.Windows.Forms.TextBox();
            this.btnExecute2 = new System.Windows.Forms.Button();
            this.tkbGain2 = new System.Windows.Forms.TrackBar();
            this.label4 = new System.Windows.Forms.Label();
            this.tkbExposure2 = new System.Windows.Forms.TrackBar();
            this.label3 = new System.Windows.Forms.Label();
            this.CameraControl = new System.Windows.Forms.TabControl();
            this.Camera1 = new System.Windows.Forms.TabPage();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.rdbHWTrigger1 = new System.Windows.Forms.RadioButton();
            this.rdbSWTrigger1 = new System.Windows.Forms.RadioButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rdbtrigger1 = new System.Windows.Forms.RadioButton();
            this.rdbFreerun1 = new System.Windows.Forms.RadioButton();
            this.txtBExposure1 = new System.Windows.Forms.TextBox();
            this.txtBGain1 = new System.Windows.Forms.TextBox();
            this.btnExecute1 = new System.Windows.Forms.Button();
            this.tkbGain1 = new System.Windows.Forms.TrackBar();
            this.label2 = new System.Windows.Forms.Label();
            this.tkbExposure1 = new System.Windows.Forms.TrackBar();
            this.label1 = new System.Windows.Forms.Label();
            this.Camera2 = new System.Windows.Forms.TabPage();
            this.Camera3 = new System.Windows.Forms.TabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.rdbHWTrigger3 = new System.Windows.Forms.RadioButton();
            this.rdbSWTrigger3 = new System.Windows.Forms.RadioButton();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.rdbtrigger3 = new System.Windows.Forms.RadioButton();
            this.rdbFreerun3 = new System.Windows.Forms.RadioButton();
            this.txtBExposure3 = new System.Windows.Forms.TextBox();
            this.txtBGain3 = new System.Windows.Forms.TextBox();
            this.btnExecute3 = new System.Windows.Forms.Button();
            this.tkbGain3 = new System.Windows.Forms.TrackBar();
            this.label6 = new System.Windows.Forms.Label();
            this.tkbExposure3 = new System.Windows.Forms.TrackBar();
            this.label5 = new System.Windows.Forms.Label();
            this.Camera4 = new System.Windows.Forms.TabPage();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.btnExecute4 = new System.Windows.Forms.Button();
            this.groupBox6.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure2)).BeginInit();
            this.CameraControl.SuspendLayout();
            this.Camera1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure1)).BeginInit();
            this.Camera2.SuspendLayout();
            this.Camera3.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.groupBox8.SuspendLayout();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure3)).BeginInit();
            this.Camera4.SuspendLayout();
            this.groupBox12.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox6
            // 
            resources.ApplyResources(this.groupBox6, "groupBox6");
            this.groupBox6.Controls.Add(this.groupBox5);
            this.groupBox6.Controls.Add(this.groupBox4);
            this.groupBox6.Controls.Add(this.txtBExposure2);
            this.groupBox6.Controls.Add(this.txtBGain2);
            this.groupBox6.Controls.Add(this.btnExecute2);
            this.groupBox6.Controls.Add(this.tkbGain2);
            this.groupBox6.Controls.Add(this.label4);
            this.groupBox6.Controls.Add(this.tkbExposure2);
            this.groupBox6.Controls.Add(this.label3);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.TabStop = false;
            // 
            // groupBox5
            // 
            resources.ApplyResources(this.groupBox5, "groupBox5");
            this.groupBox5.Controls.Add(this.rdbHWTrigger2);
            this.groupBox5.Controls.Add(this.rdbSWTrigger2);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.TabStop = false;
            // 
            // rdbHWTrigger2
            // 
            resources.ApplyResources(this.rdbHWTrigger2, "rdbHWTrigger2");
            this.rdbHWTrigger2.Name = "rdbHWTrigger2";
            this.rdbHWTrigger2.UseVisualStyleBackColor = true;
            this.rdbHWTrigger2.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect1);
            // 
            // rdbSWTrigger2
            // 
            resources.ApplyResources(this.rdbSWTrigger2, "rdbSWTrigger2");
            this.rdbSWTrigger2.Checked = true;
            this.rdbSWTrigger2.Name = "rdbSWTrigger2";
            this.rdbSWTrigger2.TabStop = true;
            this.rdbSWTrigger2.UseVisualStyleBackColor = true;
            this.rdbSWTrigger2.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect2);
            // 
            // groupBox4
            // 
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Controls.Add(this.rdbtrigger2);
            this.groupBox4.Controls.Add(this.rdbFreerun2);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            // 
            // rdbtrigger2
            // 
            resources.ApplyResources(this.rdbtrigger2, "rdbtrigger2");
            this.rdbtrigger2.Checked = true;
            this.rdbtrigger2.Name = "rdbtrigger2";
            this.rdbtrigger2.TabStop = true;
            this.rdbtrigger2.UseVisualStyleBackColor = true;
            this.rdbtrigger2.CheckedChanged += new System.EventHandler(this.model_select2);
            // 
            // rdbFreerun2
            // 
            resources.ApplyResources(this.rdbFreerun2, "rdbFreerun2");
            this.rdbFreerun2.Name = "rdbFreerun2";
            this.rdbFreerun2.UseVisualStyleBackColor = true;
            this.rdbFreerun2.CheckedChanged += new System.EventHandler(this.model_select2);
            // 
            // txtBExposure2
            // 
            resources.ApplyResources(this.txtBExposure2, "txtBExposure2");
            this.txtBExposure2.Name = "txtBExposure2";
            // 
            // txtBGain2
            // 
            resources.ApplyResources(this.txtBGain2, "txtBGain2");
            this.txtBGain2.Name = "txtBGain2";
            // 
            // btnExecute2
            // 
            resources.ApplyResources(this.btnExecute2, "btnExecute2");
            this.btnExecute2.BackColor = System.Drawing.Color.Silver;
            this.btnExecute2.FlatAppearance.BorderSize = 0;
            this.btnExecute2.Name = "btnExecute2";
            this.btnExecute2.UseVisualStyleBackColor = false;
            this.btnExecute2.Click += new System.EventHandler(this.btnExecute2_Click);
            // 
            // tkbGain2
            // 
            resources.ApplyResources(this.tkbGain2, "tkbGain2");
            this.tkbGain2.Maximum = 19;
            this.tkbGain2.Name = "tkbGain2";
            this.tkbGain2.Scroll += new System.EventHandler(this.tkbGain2_Scroll);
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // tkbExposure2
            // 
            resources.ApplyResources(this.tkbExposure2, "tkbExposure2");
            this.tkbExposure2.Maximum = 100000;
            this.tkbExposure2.Minimum = 100;
            this.tkbExposure2.Name = "tkbExposure2";
            this.tkbExposure2.Value = 1000;
            this.tkbExposure2.Scroll += new System.EventHandler(this.tkbExposure2_Scroll);
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // CameraControl
            // 
            resources.ApplyResources(this.CameraControl, "CameraControl");
            this.CameraControl.Controls.Add(this.Camera1);
            this.CameraControl.Controls.Add(this.Camera2);
            this.CameraControl.Controls.Add(this.Camera3);
            this.CameraControl.Controls.Add(this.Camera4);
            this.CameraControl.Name = "CameraControl";
            this.CameraControl.SelectedIndex = 0;
            // 
            // Camera1
            // 
            resources.ApplyResources(this.Camera1, "Camera1");
            this.Camera1.Controls.Add(this.groupBox3);
            this.Camera1.Name = "Camera1";
            this.Camera1.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Controls.Add(this.groupBox2);
            this.groupBox3.Controls.Add(this.groupBox1);
            this.groupBox3.Controls.Add(this.txtBExposure1);
            this.groupBox3.Controls.Add(this.txtBGain1);
            this.groupBox3.Controls.Add(this.btnExecute1);
            this.groupBox3.Controls.Add(this.tkbGain1);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.tkbExposure1);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // groupBox2
            // 
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Controls.Add(this.rdbHWTrigger1);
            this.groupBox2.Controls.Add(this.rdbSWTrigger1);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // rdbHWTrigger1
            // 
            resources.ApplyResources(this.rdbHWTrigger1, "rdbHWTrigger1");
            this.rdbHWTrigger1.Name = "rdbHWTrigger1";
            this.rdbHWTrigger1.UseVisualStyleBackColor = true;
            this.rdbHWTrigger1.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect1);
            // 
            // rdbSWTrigger1
            // 
            resources.ApplyResources(this.rdbSWTrigger1, "rdbSWTrigger1");
            this.rdbSWTrigger1.Checked = true;
            this.rdbSWTrigger1.Name = "rdbSWTrigger1";
            this.rdbSWTrigger1.TabStop = true;
            this.rdbSWTrigger1.UseVisualStyleBackColor = true;
            this.rdbSWTrigger1.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect1);
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.rdbtrigger1);
            this.groupBox1.Controls.Add(this.rdbFreerun1);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // rdbtrigger1
            // 
            resources.ApplyResources(this.rdbtrigger1, "rdbtrigger1");
            this.rdbtrigger1.Checked = true;
            this.rdbtrigger1.Name = "rdbtrigger1";
            this.rdbtrigger1.TabStop = true;
            this.rdbtrigger1.UseVisualStyleBackColor = true;
            this.rdbtrigger1.CheckedChanged += new System.EventHandler(this.model_select1);
            // 
            // rdbFreerun1
            // 
            resources.ApplyResources(this.rdbFreerun1, "rdbFreerun1");
            this.rdbFreerun1.Name = "rdbFreerun1";
            this.rdbFreerun1.UseVisualStyleBackColor = true;
            this.rdbFreerun1.CheckedChanged += new System.EventHandler(this.model_select1);
            // 
            // txtBExposure1
            // 
            resources.ApplyResources(this.txtBExposure1, "txtBExposure1");
            this.txtBExposure1.Name = "txtBExposure1";
            // 
            // txtBGain1
            // 
            resources.ApplyResources(this.txtBGain1, "txtBGain1");
            this.txtBGain1.Name = "txtBGain1";
            // 
            // btnExecute1
            // 
            resources.ApplyResources(this.btnExecute1, "btnExecute1");
            this.btnExecute1.BackColor = System.Drawing.Color.Silver;
            this.btnExecute1.FlatAppearance.BorderSize = 0;
            this.btnExecute1.Name = "btnExecute1";
            this.btnExecute1.UseVisualStyleBackColor = false;
            this.btnExecute1.Click += new System.EventHandler(this.btnExecute1_Click1);
            // 
            // tkbGain1
            // 
            resources.ApplyResources(this.tkbGain1, "tkbGain1");
            this.tkbGain1.Maximum = 19;
            this.tkbGain1.Name = "tkbGain1";
            this.tkbGain1.Scroll += new System.EventHandler(this.tkbGain1_Scroll);
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // tkbExposure1
            // 
            resources.ApplyResources(this.tkbExposure1, "tkbExposure1");
            this.tkbExposure1.Maximum = 100000;
            this.tkbExposure1.Minimum = 200;
            this.tkbExposure1.Name = "tkbExposure1";
            this.tkbExposure1.Value = 200;
            this.tkbExposure1.Scroll += new System.EventHandler(this.tkbExposure1_Scroll);
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // Camera2
            // 
            resources.ApplyResources(this.Camera2, "Camera2");
            this.Camera2.Controls.Add(this.groupBox6);
            this.Camera2.Name = "Camera2";
            this.Camera2.UseVisualStyleBackColor = true;
            // 
            // Camera3
            // 
            resources.ApplyResources(this.Camera3, "Camera3");
            this.Camera3.Controls.Add(this.groupBox9);
            this.Camera3.Name = "Camera3";
            this.Camera3.UseVisualStyleBackColor = true;
            // 
            // groupBox9
            // 
            resources.ApplyResources(this.groupBox9, "groupBox9");
            this.groupBox9.Controls.Add(this.groupBox8);
            this.groupBox9.Controls.Add(this.groupBox7);
            this.groupBox9.Controls.Add(this.txtBExposure3);
            this.groupBox9.Controls.Add(this.txtBGain3);
            this.groupBox9.Controls.Add(this.btnExecute3);
            this.groupBox9.Controls.Add(this.tkbGain3);
            this.groupBox9.Controls.Add(this.label6);
            this.groupBox9.Controls.Add(this.tkbExposure3);
            this.groupBox9.Controls.Add(this.label5);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.TabStop = false;
            // 
            // groupBox8
            // 
            resources.ApplyResources(this.groupBox8, "groupBox8");
            this.groupBox8.Controls.Add(this.rdbHWTrigger3);
            this.groupBox8.Controls.Add(this.rdbSWTrigger3);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.TabStop = false;
            // 
            // rdbHWTrigger3
            // 
            resources.ApplyResources(this.rdbHWTrigger3, "rdbHWTrigger3");
            this.rdbHWTrigger3.Name = "rdbHWTrigger3";
            this.rdbHWTrigger3.UseVisualStyleBackColor = true;
            this.rdbHWTrigger3.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect3);
            // 
            // rdbSWTrigger3
            // 
            resources.ApplyResources(this.rdbSWTrigger3, "rdbSWTrigger3");
            this.rdbSWTrigger3.Checked = true;
            this.rdbSWTrigger3.Name = "rdbSWTrigger3";
            this.rdbSWTrigger3.TabStop = true;
            this.rdbSWTrigger3.UseVisualStyleBackColor = true;
            this.rdbSWTrigger3.CheckedChanged += new System.EventHandler(this.Extern_SoftSelect2);
            // 
            // groupBox7
            // 
            resources.ApplyResources(this.groupBox7, "groupBox7");
            this.groupBox7.Controls.Add(this.rdbtrigger3);
            this.groupBox7.Controls.Add(this.rdbFreerun3);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.TabStop = false;
            // 
            // rdbtrigger3
            // 
            resources.ApplyResources(this.rdbtrigger3, "rdbtrigger3");
            this.rdbtrigger3.Checked = true;
            this.rdbtrigger3.Name = "rdbtrigger3";
            this.rdbtrigger3.TabStop = true;
            this.rdbtrigger3.UseVisualStyleBackColor = true;
            this.rdbtrigger3.CheckedChanged += new System.EventHandler(this.model_select3);
            // 
            // rdbFreerun3
            // 
            resources.ApplyResources(this.rdbFreerun3, "rdbFreerun3");
            this.rdbFreerun3.Name = "rdbFreerun3";
            this.rdbFreerun3.UseVisualStyleBackColor = true;
            this.rdbFreerun3.CheckedChanged += new System.EventHandler(this.model_select3);
            // 
            // txtBExposure3
            // 
            resources.ApplyResources(this.txtBExposure3, "txtBExposure3");
            this.txtBExposure3.Name = "txtBExposure3";
            // 
            // txtBGain3
            // 
            resources.ApplyResources(this.txtBGain3, "txtBGain3");
            this.txtBGain3.Name = "txtBGain3";
            // 
            // btnExecute3
            // 
            resources.ApplyResources(this.btnExecute3, "btnExecute3");
            this.btnExecute3.BackColor = System.Drawing.Color.Silver;
            this.btnExecute3.FlatAppearance.BorderSize = 0;
            this.btnExecute3.Name = "btnExecute3";
            this.btnExecute3.UseVisualStyleBackColor = false;
            this.btnExecute3.Click += new System.EventHandler(this.btnExecute3_Click);
            // 
            // tkbGain3
            // 
            resources.ApplyResources(this.tkbGain3, "tkbGain3");
            this.tkbGain3.Maximum = 19;
            this.tkbGain3.Name = "tkbGain3";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // tkbExposure3
            // 
            resources.ApplyResources(this.tkbExposure3, "tkbExposure3");
            this.tkbExposure3.Maximum = 100000;
            this.tkbExposure3.Minimum = 200;
            this.tkbExposure3.Name = "tkbExposure3";
            this.tkbExposure3.Value = 200;
            this.tkbExposure3.Scroll += new System.EventHandler(this.tkbExposure3_Scroll);
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // Camera4
            // 
            resources.ApplyResources(this.Camera4, "Camera4");
            this.Camera4.Controls.Add(this.groupBox12);
            this.Camera4.Name = "Camera4";
            this.Camera4.UseVisualStyleBackColor = true;
            // 
            // groupBox12
            // 
            resources.ApplyResources(this.groupBox12, "groupBox12");
            this.groupBox12.Controls.Add(this.btnExecute4);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.TabStop = false;
            // 
            // btnExecute4
            // 
            resources.ApplyResources(this.btnExecute4, "btnExecute4");
            this.btnExecute4.BackColor = System.Drawing.Color.Silver;
            this.btnExecute4.FlatAppearance.BorderSize = 0;
            this.btnExecute4.Name = "btnExecute4";
            this.btnExecute4.UseVisualStyleBackColor = false;
            this.btnExecute4.Click += new System.EventHandler(this.btnExecute4_Click);
            // 
            // CameraParaSet
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.CameraControl);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CameraParaSet";
            this.Load += new System.EventHandler(this.CameraParaSet_Load);
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure2)).EndInit();
            this.CameraControl.ResumeLayout(false);
            this.Camera1.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure1)).EndInit();
            this.Camera2.ResumeLayout(false);
            this.Camera3.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tkbGain3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tkbExposure3)).EndInit();
            this.Camera4.ResumeLayout(false);
            this.groupBox12.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        public System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.TextBox txtBExposure2;
        private System.Windows.Forms.RadioButton rdbHWTrigger2;
        private System.Windows.Forms.TextBox txtBGain2;
        private System.Windows.Forms.RadioButton rdbSWTrigger2;
        private System.Windows.Forms.RadioButton rdbFreerun2;
        private System.Windows.Forms.Button btnExecute2;
        public System.Windows.Forms.TrackBar tkbGain2;
        private System.Windows.Forms.Label label4;
        public System.Windows.Forms.TrackBar tkbExposure2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.RadioButton rdbtrigger2;
        private System.Windows.Forms.TabPage Camera2;
        private System.Windows.Forms.TabPage Camera3;
        public System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.RadioButton rdbHWTrigger3;
        private System.Windows.Forms.RadioButton rdbSWTrigger3;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.RadioButton rdbtrigger3;
        private System.Windows.Forms.RadioButton rdbFreerun3;
        private System.Windows.Forms.TextBox txtBExposure3;
        private System.Windows.Forms.TextBox txtBGain3;
        private System.Windows.Forms.Button btnExecute3;
        public System.Windows.Forms.TrackBar tkbGain3;
        private System.Windows.Forms.Label label6;
        public System.Windows.Forms.TrackBar tkbExposure3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TabPage Camera1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.RadioButton rdbHWTrigger1;
        private System.Windows.Forms.RadioButton rdbSWTrigger1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rdbtrigger1;
        private System.Windows.Forms.RadioButton rdbFreerun1;
        private System.Windows.Forms.TextBox txtBExposure1;
        private System.Windows.Forms.TextBox txtBGain1;
        private System.Windows.Forms.Button btnExecute1;
        public System.Windows.Forms.TrackBar tkbGain1;
        private System.Windows.Forms.Label label2;
        public System.Windows.Forms.TrackBar tkbExposure1;
        private System.Windows.Forms.Label label1;
        public System.Windows.Forms.TabControl CameraControl;
        public System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.TabPage Camera4;
        private System.Windows.Forms.Button btnExecute4;
        public System.Windows.Forms.GroupBox groupBox12;
    }
}