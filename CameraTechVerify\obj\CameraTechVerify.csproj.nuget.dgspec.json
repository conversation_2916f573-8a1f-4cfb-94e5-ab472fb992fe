{"format": 1, "restore": {"G:\\project\\lizhongbaijian_Halcon19.11\\CameraTechVerify\\CameraTechVerify.csproj": {}}, "projects": {"G:\\project\\lizhongbaijian_Halcon19.11\\CameraTechVerify\\CameraTechVerify.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\project\\lizhongbaijian_Halcon19.11\\CameraTechVerify\\CameraTechVerify.csproj", "projectName": "CameraTechVerify", "projectPath": "G:\\project\\lizhongbaijian_Halcon19.11\\CameraTechVerify\\CameraTechVerify.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\project\\lizhongbaijian_Halcon19.11\\CameraTechVerify\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\ComponentOne\\WPF Edition\\bin\\v6\\", "C:\\Program Files (x86)\\ComponentOne\\WinForms Edition\\bin\\v6\\", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\ComponentOne.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\ComponentOne\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}