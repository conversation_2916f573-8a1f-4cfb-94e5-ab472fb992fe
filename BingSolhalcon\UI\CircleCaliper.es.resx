﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value />
  </data>
  <data name="btn_drawcircle.Text" xml:space="preserve">
    <value>Dibujar un círculo de medición</value>
  </data>
  <data name="btn_mtrsave.Text" xml:space="preserve">
    <value>Salvar</value>
  </data>
  <data name="btn_readimage.Text" xml:space="preserve">
    <value>Cargar la imagen</value>
  </data>
  <data name="btn_test.Text" xml:space="preserve">
    <value>Prueba</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource" xml:space="preserve">
    <value>Reconocimiento de tipo de rueda</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource1" xml:space="preserve">
    <value>Orificio central</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource2" xml:space="preserve">
    <value>Orificios de válvula</value>
  </data>
  <data name="comboBox_selecttype.Items" xml:space="preserve">
    <value>Orificio central</value>
  </data>
  <data name="comboBox_selecttype.Items1" xml:space="preserve">
    <value>Orificios de perno</value>
  </data>
  <data name="comboBox_selecttype.Items2" xml:space="preserve">
    <value>Tope de tapa</value>
  </data>
  <data name="comboBox_selecttype.Text" xml:space="preserve">
    <value>Orificio central</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Configuración de parámetros del taladro central</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Configuración de parámetros de orificio de perno</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>Configuración del parámetro Cap stop</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Selección de orden de borde</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Lisura</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>La puntuación mínima</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>Selección de orden de borde</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>polaridad</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>El número de áreas de medición</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>Medir la longitud del rectángulo</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>Medir el ancho del rectángulo</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>Diámetro del prototipo</value>
  </data>
  <data name="label19.Text" xml:space="preserve">
    <value>El tipo de plantilla</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>La puntuación mínima</value>
  </data>
  <data name="label20.Text" xml:space="preserve">
    <value>El diámetro del peque?o orificio en la rueda de muestra</value>
  </data>
  <data name="label22.Text" xml:space="preserve">
    <value>Escala de grises mínima</value>
  </data>
  <data name="label23.Text" xml:space="preserve">
    <value>Escala de grises mínima</value>
  </data>
  <data name="label24.Text" xml:space="preserve">
    <value>Lisura</value>
  </data>
  <data name="label25.Text" xml:space="preserve">
    <value>La puntuación mínima</value>
  </data>
  <data name="label26.Text" xml:space="preserve">
    <value>Selección de orden de borde</value>
  </data>
  <data name="label27.Text" xml:space="preserve">
    <value>polaridad</value>
  </data>
  <data name="label28.Text" xml:space="preserve">
    <value>Diámetro del prototipo</value>
  </data>
  <data name="label29.Text" xml:space="preserve">
    <value>El número de áreas de medición</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Modelo de concentrador</value>
  </data>
  <data name="label30.Text" xml:space="preserve">
    <value>Medir la longitud del rectángulo</value>
  </data>
  <data name="label31.Text" xml:space="preserve">
    <value>Medir el ancho del rectángulo</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Escala de grises mínima</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Medir el ancho del rectángulo</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Lisura</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Medir la longitud del rectángulo</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>El número de áreas de medición</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>polaridad</value>
  </data>
  <data name="allFile" xml:space="preserve">
    <value>Todos los archivos (*.*) |*.*</value>
  </data>
  <data name="boltHoleDecFail" xml:space="preserve">
    <value>Error en la detección del orificio del perno</value>
  </data>
  <data name="boltHoleLack" xml:space="preserve">
    <value>Formulaciones sin parámetros o formulaciones de orificios de perno faltantes</value>
  </data>
  <data name="boltHoleSave" xml:space="preserve">
    <value>El modelo de orificio de perno se guardó correctamente</value>
  </data>
  <data name="dbNotOpen" xml:space="preserve">
    <value>La base de datos no está abierta</value>
  </data>
  <data name="hubExit" xml:space="preserve">
    <value>"Se ha introducido este concentrador, si se debe reemplazarlo"</value>
  </data>
  <data name="loadImg" xml:space="preserve">
    <value>"Por favor, cargue la imagen"</value>
  </data>
  <data name="loadImg1" xml:space="preserve">
    <value>"Por favor, cargue una imagen"</value>
  </data>
  <data name="noImgOrType" xml:space="preserve">
    <value>La imagen no está cargada o el número de modelo de la rueda no se introduce</value>
  </data>
  <data name="noParam" xml:space="preserve">
    <value>Formulación sin parámetros</value>
  </data>
  <data name="noParamOrHub" xml:space="preserve">
    <value>La receta no se ha creado o la tabla de parámetros del concentrador no existe</value>
  </data>
  <data name="notRecordCap" xml:space="preserve">
    <value>No se introduce la plantilla de orificio de parada de tapa</value>
  </data>
  <data name="notRecordCen" xml:space="preserve">
    <value>No se introduce la plantilla de taladro central</value>
  </data>
  <data name="paramExit" xml:space="preserve">
    <value>"El parámetro de orificio central de este cubo ya existe, ya sea para agregar el parámetro de orificio de perno"</value>
  </data>
  <data name="saveCap" xml:space="preserve">
    <value>El modelo de tope se guardó correctamente</value>
  </data>
  <data name="saveCen" xml:space="preserve">
    <value>El modelo de taladro central se guardó correctamente</value>
  </data>
  <data name="saveFault" xml:space="preserve">
    <value>?Error al guardar!</value>
  </data>
  <data name="selectFile" xml:space="preserve">
    <value>Seleccione una carpeta</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>DE ACUERDO</value>
  </data>
</root>