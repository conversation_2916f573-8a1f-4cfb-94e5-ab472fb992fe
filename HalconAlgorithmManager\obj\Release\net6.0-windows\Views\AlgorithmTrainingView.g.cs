﻿#pragma checksum "..\..\..\..\Views\AlgorithmTrainingView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "44A444CC4721A75B04017F6E4B5E584B7B78C2FF"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HalconAlgorithmManager.Views {
    
    
    /// <summary>
    /// AlgorithmTrainingView
    /// </summary>
    public partial class AlgorithmTrainingView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 65 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SampleCountText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox PositiveSamplesListBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox NegativeSamplesListBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ValidationSamplesListBox;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentSampleText;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas SampleImageCanvas;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CurrentSampleImage;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas AnnotationCanvas;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultSamplePanel;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AnnotationToolbar;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ROITool;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton PointTool;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton LineTool;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AnnotationInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlgorithmTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TrainingModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider TrainingEpochsSlider;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingEpochsText;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider LearningRateSlider;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LearningRateText;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingStatusText;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentEpochText;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar TrainingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingAccuracyText;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationAccuracyText;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingLossText;
        
        #line default
        #line hidden
        
        
        #line 423 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedTimeText;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BestAccuracyText;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelSizeText;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InferenceSpeedText;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelGradeText;
        
        #line default
        #line hidden
        
        
        #line 497 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TrainingLogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HalconAlgorithmManager;component/views/algorithmtrainingview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadTrainingSamples_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 33 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StartTraining_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 36 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveModel_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SampleCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PositiveSamplesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 83 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.PositiveSamplesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SampleListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.NegativeSamplesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 123 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.NegativeSamplesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SampleListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ValidationSamplesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 163 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.ValidationSamplesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SampleListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 206 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPositiveSample_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 209 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddNegativeSample_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 212 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddValidationSample_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 215 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AutoClassify_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CurrentSampleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SampleImageCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 247 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.SampleImageCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SampleImageCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 248 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.SampleImageCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.SampleImageCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 249 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            this.SampleImageCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.SampleImageCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CurrentSampleImage = ((System.Windows.Controls.Image)(target));
            return;
            case 15:
            this.AnnotationCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 16:
            this.DefaultSamplePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.AnnotationToolbar = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.ROITool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 19:
            this.PointTool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 20:
            this.LineTool = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 21:
            this.AnnotationInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.AlgorithmTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.TrainingModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 24:
            this.TrainingEpochsSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 25:
            this.TrainingEpochsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.LearningRateSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 27:
            this.LearningRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.TrainingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.CurrentEpochText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.TrainingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 31:
            this.TrainingAccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.ValidationAccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.TrainingLossText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.EstimatedTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.BestAccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.ModelSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.InferenceSpeedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.ModelGradeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            
            #line 481 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportModel_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            
            #line 484 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportModel_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            
            #line 487 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RetrainModel_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 490 "..\..\..\..\Views\AlgorithmTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearModel_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.TrainingLogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

