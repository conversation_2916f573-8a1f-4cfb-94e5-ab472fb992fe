﻿namespace BingSolhalcon.UI
{
    partial class HKLaser
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(HKLaser));
            this.hWindowControl1 = new HalconDotNet.HWindowControl();
            this.ReadImage_button = new System.Windows.Forms.Button();
            this.btn_BaseLine = new System.Windows.Forms.Button();
            this.btn_Test = new System.Windows.Forms.Button();
            this.wheelmodol = new System.Windows.Forms.TextBox();
            this.btn_Save = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.btn_CreaTepattern = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // hWindowControl1
            // 
            resources.ApplyResources(this.hWindowControl1, "hWindowControl1");
            this.hWindowControl1.BackColor = System.Drawing.Color.Black;
            this.hWindowControl1.BorderColor = System.Drawing.Color.Black;
            this.hWindowControl1.ImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hWindowControl1.Name = "hWindowControl1";
            this.hWindowControl1.WindowSize = new System.Drawing.Size(686, 702);
            this.hWindowControl1.HMouseMove += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseMove);
            this.hWindowControl1.HMouseDown += new HalconDotNet.HMouseEventHandler(this.hWindowControl2_HMouseDown);
            this.hWindowControl1.HMouseUp += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseUp);
            this.hWindowControl1.HMouseWheel += new HalconDotNet.HMouseEventHandler(this.hWindowControl1_HMouseWheel);
            // 
            // ReadImage_button
            // 
            resources.ApplyResources(this.ReadImage_button, "ReadImage_button");
            this.ReadImage_button.Name = "ReadImage_button";
            this.ReadImage_button.UseVisualStyleBackColor = true;
            this.ReadImage_button.Click += new System.EventHandler(this.ReadImage_button_Click);
            // 
            // btn_BaseLine
            // 
            resources.ApplyResources(this.btn_BaseLine, "btn_BaseLine");
            this.btn_BaseLine.Name = "btn_BaseLine";
            this.btn_BaseLine.UseVisualStyleBackColor = true;
            this.btn_BaseLine.Click += new System.EventHandler(this.btn_BaseLine_Click);
            // 
            // btn_Test
            // 
            resources.ApplyResources(this.btn_Test, "btn_Test");
            this.btn_Test.Name = "btn_Test";
            this.btn_Test.UseVisualStyleBackColor = true;
            this.btn_Test.Click += new System.EventHandler(this.btn_Test_Click);
            // 
            // wheelmodol
            // 
            resources.ApplyResources(this.wheelmodol, "wheelmodol");
            this.wheelmodol.Name = "wheelmodol";
            // 
            // btn_Save
            // 
            resources.ApplyResources(this.btn_Save, "btn_Save");
            this.btn_Save.Name = "btn_Save";
            this.btn_Save.UseVisualStyleBackColor = true;
            this.btn_Save.Click += new System.EventHandler(this.btn_Save_Click);
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // btn_CreaTepattern
            // 
            resources.ApplyResources(this.btn_CreaTepattern, "btn_CreaTepattern");
            this.btn_CreaTepattern.Name = "btn_CreaTepattern";
            this.btn_CreaTepattern.UseVisualStyleBackColor = true;
            this.btn_CreaTepattern.Click += new System.EventHandler(this.btn_CreaTepattern_Click);
            // 
            // HKLaser
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btn_CreaTepattern);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.btn_Save);
            this.Controls.Add(this.wheelmodol);
            this.Controls.Add(this.btn_Test);
            this.Controls.Add(this.btn_BaseLine);
            this.Controls.Add(this.ReadImage_button);
            this.Controls.Add(this.hWindowControl1);
            this.Name = "HKLaser";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.Form_Closed);
            this.Load += new System.EventHandler(this.HKLaser_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private HalconDotNet.HWindowControl hWindowControl1;
        private System.Windows.Forms.Button ReadImage_button;
        private System.Windows.Forms.Button btn_BaseLine;
        private System.Windows.Forms.Button btn_Test;
        private System.Windows.Forms.TextBox wheelmodol;
        private System.Windows.Forms.Button btn_Save;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btn_CreaTepattern;
    }
}