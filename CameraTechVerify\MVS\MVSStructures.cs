using System;
using System.Runtime.InteropServices;

namespace CameraTechVerify.MVS
{
    /// <summary>
    /// MVS 4.5.1 SDK 数据结构定义
    /// </summary>
    public static class MVSStructures
    {
        // 常量定义
        public const uint MV_OK = 0x00000000;
        public const uint MV_GIGE_DEVICE = 0x00000001;
        public const uint MV_USB_DEVICE = 0x00000002;
        public const uint MV_UNKNOW_DEVICE = 0x00000000;
        public const uint MV_ALL_DEVICE = 0x00000000;

        // 最大设备数量
        public const int MV_MAX_DEVICE_NUM = 256;
        public const int MV_MAX_TLS_NUM = 8;
        public const int MV_MAX_XML_SYMBOLIC_NUM = 64;

        // 字符串长度
        public const int MV_MAX_XML_SYMBOLIC_STRLEN = 64;
        public const int MV_MAX_XML_STRVALUE_STRLEN = 64;
        public const int MV_MAX_XML_DISC_STRLEN = 512;
        public const int MV_MAX_XML_NODE_STRLEN = 512;

        /// <summary>
        /// 设备信息
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MV_CC_DEVICE_INFO
        {
            public ushort nMajorVer;
            public ushort nMinorVer;
            public uint nMacAddrHigh;
            public uint nMacAddrLow;
            public uint nTLayerType;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public uint[] nReserved;
            public MV_GIGE_DEVICE_INFO SpecialInfo;
        }

        /// <summary>
        /// GigE 设备信息
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MV_GIGE_DEVICE_INFO
        {
            public uint nCurrentIp;
            public uint nCurrentSubNetMask;
            public uint nDefultGateWay;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chManufacturerName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chModelName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chDeviceVersion;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chManufacturerSpecificInfo;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chSerialNumber;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string chUserDefinedName;
            public uint nNetExport;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public uint[] nReserved;
        }

        /// <summary>
        /// USB 设备信息
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MV_USB3_DEVICE_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chDeviceGUID;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chVendorName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chModelName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chFamilyName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chDeviceVersion;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chManufacturerName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chSerialNumber;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string chUserDefinedName;
            public uint nbcdUSB;
            public uint nDeviceNumber;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 3)]
            public uint[] nReserved;
        }

        /// <summary>
        /// 设备信息列表
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MV_CC_DEVICE_INFO_LIST
        {
            public uint nDeviceNum;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = MV_MAX_DEVICE_NUM)]
            public IntPtr[] pDeviceInfo;
        }

        /// <summary>
        /// 图像输出信息
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MV_FRAME_OUT_INFO_EX
        {
            public ushort nWidth;
            public ushort nHeight;
            public uint nPixelType;
            public IntPtr pImageBuf;
            public uint nImageLen;
            public uint nLostPacket;
            public uint nHostTimeStamp;
            public uint nDevTimeStamp;
            public uint nFrameNum;
            public uint nTriggerIndex;
            public uint nInput;
            public uint nOutput;
            public ushort nOffsetX;
            public ushort nOffsetY;
            public uint nChunkWidth;
            public uint nChunkHeight;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
            public uint[] nReserved;
        }

        /// <summary>
        /// 浮点参数值
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MVCC_FLOATVALUE
        {
            public uint nCurValue;
            public float fCurValue;
            public float fMax;
            public float fMin;
        }

        /// <summary>
        /// 整型参数值
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MVCC_INTVALUE
        {
            public uint nCurValue;
            public uint nMax;
            public uint nMin;
            public uint nInc;
        }

        /// <summary>
        /// 枚举参数值
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MVCC_ENUMVALUE
        {
            public uint nCurValue;
            public uint nSupportedNum;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = MV_MAX_XML_SYMBOLIC_NUM)]
            public uint[] nSupportValue;
        }

        /// <summary>
        /// 字符串参数值
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MVCC_STRINGVALUE
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = MV_MAX_XML_STRVALUE_STRLEN)]
            public string chCurValue;
            public long nMaxLength;
        }

        /// <summary>
        /// 像素格式
        /// </summary>
        public enum MvGvspPixelType : uint
        {
            PixelType_Gvsp_Undefined = 0x80000000,
            PixelType_Gvsp_Mono8 = 0x01080001,
            PixelType_Gvsp_Mono10 = 0x01100003,
            PixelType_Gvsp_Mono10_Packed = 0x010C0004,
            PixelType_Gvsp_Mono12 = 0x01100005,
            PixelType_Gvsp_Mono12_Packed = 0x010C0006,
            PixelType_Gvsp_Mono14 = 0x01100025,
            PixelType_Gvsp_Mono16 = 0x01100007,

            PixelType_Gvsp_BayerGR8 = 0x01080008,
            PixelType_Gvsp_BayerRG8 = 0x01080009,
            PixelType_Gvsp_BayerGB8 = 0x0108000A,
            PixelType_Gvsp_BayerBG8 = 0x0108000B,

            PixelType_Gvsp_RGB8_Packed = 0x02180014,
            PixelType_Gvsp_BGR8_Packed = 0x02180015,
            PixelType_Gvsp_RGBA8_Packed = 0x02200016,
            PixelType_Gvsp_BGRA8_Packed = 0x02200017,

            PixelType_Gvsp_YUV422_Packed = 0x0210001F,
            PixelType_Gvsp_YUV422_YUYV_Packed = 0x02100032,
        }

        /// <summary>
        /// 触发模式
        /// </summary>
        public enum MV_TRIGGER_MODE
        {
            MV_TRIGGER_MODE_OFF = 0,
            MV_TRIGGER_MODE_ON = 1,
        }

        /// <summary>
        /// 触发源
        /// </summary>
        public enum MV_TRIGGER_SOURCE
        {
            MV_TRIGGER_SOURCE_LINE0 = 0,
            MV_TRIGGER_SOURCE_LINE1 = 1,
            MV_TRIGGER_SOURCE_LINE2 = 2,
            MV_TRIGGER_SOURCE_LINE3 = 3,
            MV_TRIGGER_SOURCE_COUNTER0 = 4,
            MV_TRIGGER_SOURCE_SOFTWARE = 7,
            MV_TRIGGER_SOURCE_FrequencyConverter = 8,
        }

        /// <summary>
        /// 图像回调函数委托
        /// </summary>
        /// <param name="pData">图像数据</param>
        /// <param name="pFrameInfo">图像信息</param>
        /// <param name="pUser">用户数据</param>
        public delegate void cbOutputExdelegate(IntPtr pData, ref MV_FRAME_OUT_INFO_EX pFrameInfo, IntPtr pUser);

        /// <summary>
        /// 异常回调函数委托
        /// </summary>
        /// <param name="nMsgType">异常消息类型</param>
        /// <param name="pUser">用户数据</param>
        public delegate void cbExceptiondelegate(uint nMsgType, IntPtr pUser);

        /// <summary>
        /// 事件回调函数委托
        /// </summary>
        /// <param name="nExceptionType">事件类型</param>
        /// <param name="pUser">用户数据</param>
        public delegate void cbEventdelegate(uint nExceptionType, IntPtr pUser);
    }
}
